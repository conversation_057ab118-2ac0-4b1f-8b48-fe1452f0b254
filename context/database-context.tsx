import React, { createContext, useContext, ReactNode } from 'react';
import { useDatabase, DatabaseState } from '@/hooks/database/use-database';
import { useMigration, MigrationState } from '@/hooks/database/use-migration';

type DatabaseContextType = DatabaseState & {
  migration: MigrationState;
};

const DatabaseContext = createContext<DatabaseContextType | undefined>(undefined);

interface DatabaseProviderProps {
  children: ReactNode;
}

export const DatabaseProvider: React.FC<DatabaseProviderProps> = ({ children }) => {
  const databaseState = useDatabase();
  const migrationState = useMigration();

  const contextValue: DatabaseContextType = {
    ...databaseState,
    migration: migrationState
  };

  return (
    <DatabaseContext.Provider value={contextValue}>
      {children}
    </DatabaseContext.Provider>
  );
};

export const useDatabaseContext = (): DatabaseContextType => {
  const context = useContext(DatabaseContext);
  if (context === undefined) {
    throw new Error('useDatabaseContext must be used within a DatabaseProvider');
  }
  return context;
}; 