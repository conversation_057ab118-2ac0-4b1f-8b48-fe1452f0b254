import React from "react";
import { createContext, useContext, useState, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
type ThemeType = 'light' | 'dark' | 'system';
interface ThemeContextType {
  theme: ThemeType;
  currentTheme: 'light' | 'dark';
  setTheme: (theme: ThemeType) => void;
}
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setThemeState] = useState<ThemeType>('system');
  const [isInitialized, setIsInitialized] = useState(false);
  const systemColorScheme = useColorScheme() || 'light';
  
  // Load saved theme preference
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem('theme');
        if (savedTheme) {
          setThemeState(savedTheme as ThemeType);
        }
        setIsInitialized(true);
      } catch (_error) {
        console.error('Failed to load theme preference:', _error);
        setIsInitialized(true);
      }
    };
    
    loadTheme();
  }, []);
  
  // Save theme preference when it changes
  const setTheme = async (newTheme: ThemeType) => {
    setThemeState(newTheme);
    try {
      await AsyncStorage.setItem('theme', newTheme);
    } catch (_error) {
      console.error('Failed to save theme preference:', _error);
    }
  };
  
  // Determine the actual theme based on system or user preference
  const currentTheme = theme === 'system' ? systemColorScheme : theme;
  
  // Only render children after initialization to prevent flashing
  if (!isInitialized) {
    return null;
  }
  
  return (
    <ThemeContext.Provider value={{ theme, currentTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};