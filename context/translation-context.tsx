import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUserStore } from '@/store/user/user-store';
import { 
  translationService, 
  type SupportedLanguage,
  type NestedTranslationKey,
  type InterpolationValues 
} from '@/services/translation';

interface TranslationContextType {
  currentLanguage: SupportedLanguage;
  setLanguage: (language: SupportedLanguage) => void;
  t: (key: NestedTranslationKey, interpolation?: InterpolationValues) => string;
  getMoodLabel: (value: number) => string;
  getCravingLabel: (value: number) => string;
  getHealthMetricLabel: (metric: string, value: number) => string;
  getContactCategoryName: (category: string) => string;
  getCommonTriggers: () => string[];
  getAvailableLanguages: () => SupportedLanguage[];
}

const TranslationContext = createContext<TranslationContextType | undefined>(undefined);

interface TranslationProviderProps {
  children: React.ReactNode;
}

export function TranslationProvider({ children }: TranslationProviderProps) {
  const { profile, updateProfile } = useUserStore();
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>('en');

  // Initialize language from profile
  useEffect(() => {
    const userLanguage = profile?.language as SupportedLanguage;
    if (userLanguage && ['en', 'nl'].includes(userLanguage)) {
      setCurrentLanguage(userLanguage);
      translationService.setLanguage(userLanguage);
    } else {
      setCurrentLanguage('en');
      translationService.setLanguage('en');
    }
  }, [profile?.language]);

  const setLanguage = (language: SupportedLanguage) => {
    setCurrentLanguage(language);
    translationService.setLanguage(language);
    
    // Update profile with new language
    if (profile) {
      updateProfile({ language });
    }
  };

  const t = (key: NestedTranslationKey, interpolation?: InterpolationValues): string => {
    return translationService.translate(key, interpolation);
  };

  const getMoodLabel = (value: number): string => {
    return translationService.getMoodLabel(value);
  };

  const getCravingLabel = (value: number): string => {
    return translationService.getCravingLabel(value);
  };

  const getHealthMetricLabel = (metric: string, value: number): string => {
    return translationService.getHealthMetricLabel(metric, value);
  };

  const getContactCategoryName = (category: string): string => {
    return translationService.getContactCategoryName(category);
  };

  const getCommonTriggers = (): string[] => {
    return translationService.getCommonTriggers();
  };

  const getAvailableLanguages = (): SupportedLanguage[] => {
    return translationService.getAvailableLanguages();
  };

  const value: TranslationContextType = {
    currentLanguage,
    setLanguage,
    t,
    getMoodLabel,
    getCravingLabel,
    getHealthMetricLabel,
    getContactCategoryName,
    getCommonTriggers,
    getAvailableLanguages,
  };

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  );
}

export function useTranslationContext(): TranslationContextType {
  const context = useContext(TranslationContext);
  if (context === undefined) {
    throw new Error('useTranslationContext must be used within a TranslationProvider');
  }
  return context;
} 