---
description: 
globs: 
alwaysApply: false
---
# SobrixHealth Project Overview

## Project Description
SobrixHealth is a React Native health and wellness application built with Expo Router, focusing on health tracking, mindfulness, and user wellness management.

## Technology Stack
- **Framework**: React Native with Expo SDK 53
- **Navigation**: Expo Router v5 for file-based routing
- **Styling**: NativeWind v4 (Tailwind CSS for React Native)
- **State Management**: Zustand for global state
- **Language**: TypeScript with strict type checking
- **Package Manager**: Yarn (enforced via [enforce-yarn.js](mdc:enforce-yarn.js))

## Key Dependencies
From [package.json](mdc:package.json):
- **React Native**: 0.79.2 with React 19.0.0
- **Expo Router**: ~5.0.7 for navigation
- **NativeWind**: ^4.1.23 for styling
- **Zustand**: ^5.0.4 for state management
- **Lucide React Native**: ^0.475.0 for icons

## Project Structure
```
sobrixhealth/
├── app/                    # Expo Router pages
│   └── (tabs)/            # Tab navigation
├── components/            # Feature-organized components
│   ├── dashboard/         # Dashboard components
│   ├── health/           # Health tracking
│   ├── mindfulness/      # Meditation/mindfulness
│   ├── progress/         # Progress tracking
│   ├── settings/         # App settings
│   └── shared/           # Reusable components
├── style/                # Global styling system
├── store/                # Zustand stores
├── types/                # TypeScript type definitions
├── hooks/                # Custom React hooks
├── utils/                # Utility functions
└── constants/            # App constants
```

## Development Environment
- **Node**: >=18 (specified in [package.json](mdc:package.json))
- **Yarn**: >=1.22.0 (enforced)
- **Platform**: macOS (darwin 24.5.0)
- **Shell**: Zsh

## Build Configuration
- **Babel**: [babel.config.cjs](mdc:babel.config.cjs) for JavaScript transformation
- **TypeScript**: [tsconfig.json](mdc:tsconfig.json) for type checking
- **ESLint**: [eslint.config.js](mdc:eslint.config.js) for code quality
- **Tailwind**: [tailwind.config.js](mdc:tailwind.config.js) with NativeWind preset
- **PostCSS**: [postcss.config.js](mdc:postcss.config.js) for CSS processing

## Key Features
- **Health Tracking**: Monitor various health metrics
- **Dashboard**: Central hub for user data
- **Mindfulness**: Meditation and wellness features
- **Progress Tracking**: Visual progress indicators
- **Settings**: User preferences and app configuration
- **Contacts**: Contact management functionality

## Removed Features
- **Resources**: Completely removed from the application
