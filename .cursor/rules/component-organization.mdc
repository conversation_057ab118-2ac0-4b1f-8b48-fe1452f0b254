---
description: 
globs: 
alwaysApply: false
---
# Component Organization Rules

## Directory Structure
The components are organized by feature in the [components/](mdc:components) directory:

- **[components/contacts/](mdc:components/contacts)** - Contact management components
- **[components/dashboard/](mdc:components/dashboard)** - Dashboard and main app components  
- **[components/health/](mdc:components/health)** - Health tracking components
- **[components/mindfulness/](mdc:components/mindfulness)** - Mindfulness and meditation components
- **[components/onboarding/](mdc:components/onboarding)** - User onboarding flow components
- **[components/progress/](mdc:components/progress)** - Progress tracking components with [shared/](mdc:components/progress/shared) utilities
- **[components/settings/](mdc:components/settings)** - Settings and configuration components
- **[components/shared/](mdc:components/shared)** - Reusable components with [hooks/](mdc:components/shared/hooks) and [utils/](mdc:components/shared/utils)

## Settings Modal Pattern
Settings modals in [components/settings/](mdc:components/settings) follow a specific pattern:
- **Direct React-Native Modal**: Use native Modal component instead of shared ModalWrapper
- **Consistent UI**: Share unified styles within each modal for consistency
- **Modal types**: AboutModal, HelpSupportModal, UsageModal, NotificationsModal, PrivacyModal

## Progress Components
Components in [components/progress/](mdc:components/progress) must:
- **Use consistent styles**: Share the same style definitions across all progress components
- **Follow UI patterns**: Maintain unified UI conventions within the progress directory
- **Leverage shared utilities**: Use [components/progress/shared/](mdc:components/progress/shared) for common functionality

## Removed Features
- **Resources feature removed**: Do not create or reference components under `components/resources`
- **No Resources tab**: Do not include Resources in navigation

## Component Best Practices
```typescript
// ✅ Correct: Feature-based organization
components/
  dashboard/
    DashboardCard.tsx
    MetricsView.tsx
  health/
    HealthTracker.tsx
    VitalsInput.tsx

// ✅ Correct: Shared utilities
import { useHealthMetrics } from '@/components/shared/hooks';
import { formatDate } from '@/components/shared/utils';

// ❌ Incorrect: Flat organization
components/
  DashboardCard.tsx
  HealthTracker.tsx
  SettingsModal.tsx
