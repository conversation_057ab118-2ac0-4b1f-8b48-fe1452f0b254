---
description: 
globs: 
alwaysApply: false
---
# Expo Router Navigation Rules

## Project Structure
This is an Expo Router project with the following navigation structure:

## App Directory Structure
- **[app/(tabs)/](mdc:app/(tabs)/)** - Tab-based navigation routes
- **[app/(tabs)/dashboard/](mdc:app/(tabs)/dashboard/)** - Dashboard tab route
- **No Resources tab**: The Resources feature has been removed - do not create `app/(tabs)/resources`

## Expo Configuration
- **Main config**: [app.json](mdc:app.json) contains Expo project configuration
- **EAS config**: [eas.json](mdc:eas.json) for build and deployment settings
- **Entry point**: Uses `expo-router/entry` as defined in [package.json](mdc:package.json)

## Navigation Patterns
```typescript
// ✅ Correct: Using Expo Router navigation
import { router } from 'expo-router';
import { Link } from 'expo-router';

// Navigate programmatically
const handleNavigation = () => {
  router.push('/dashboard');
  router.replace('/onboarding');
  router.back();
};

// Declarative navigation
<Link href="/settings" asChild>
  <Pressable>
    <Text>Go to Settings</Text>
  </Pressable>
</Link>

// ❌ Incorrect: Don't create resources routes
// app/(tabs)/resources/index.tsx - This should not exist
```

## Tab Navigation
```typescript
// ✅ Correct: Tab structure (without resources)
app/
  (tabs)/
    dashboard/
      index.tsx
    health/
      index.tsx
    mindfulness/
      index.tsx
    settings/
      index.tsx
    _layout.tsx

// ❌ Incorrect: Including removed resources tab
app/
  (tabs)/
    resources/  // This should not exist
      index.tsx
```

## Route Parameters
```typescript
// ✅ Dynamic routes
app/
  health/
    [id].tsx        // /health/123
  settings/
    modal.tsx       // /settings/modal

// ✅ Accessing route parameters
import { useLocalSearchParams } from 'expo-router';

const HealthDetail = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  return <Text>Health ID: {id}</Text>;
};
```

## Development Setup
- **Start command**: `yarn start` runs Expo with tunnel mode
- **Platform commands**: `yarn android` and `yarn ios` for platform-specific builds
- **Web support**: `yarn start-web` for web development
