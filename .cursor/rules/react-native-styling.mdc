---
description: 
globs: 
alwaysApply: false
---
# React Native Styling Rules

## Global Style System
- **Always use the global style system** from [style/index.ts](mdc:style/index.ts) instead of defining styles locally
- **Import shared styles** from `@/style` directory for consistency across components
- **Use style categories**: modal styles from [style/modal.ts](mdc:style/modal.ts), form styles from [style/forms.ts](mdc:style/forms.ts)

## ESLint Style Rules (Enforced)
- **No inline styles**: The `react-native/no-inline-styles` rule is enforced - always use StyleSheet objects
- **Alphabetical style ordering**: The `react-native/sort-styles` rule requires style properties to be in ascending alphabetical order
- **No unused styles**: The `react-native/no-unused-styles` rule prevents unused style definitions

## Style Organization
```typescript
// ✅ Correct: Use global styles
import { modalStyles, formStyles } from '@/style';

// ✅ Correct: StyleSheet with alphabetical ordering
const styles = StyleSheet.create({
  button: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 12,
  },
  container: {
    flex: 1,
    padding: 16,
  },
});

// ❌ Incorrect: Inline styles
<View style={{ padding: 16, backgroundColor: 'white' }} />

// ❌ Incorrect: Non-alphabetical ordering
const styles = StyleSheet.create({
  container: {
    padding: 16,        // Should come after backgroundColor
    backgroundColor: 'white',
    flex: 1,           // Should come before padding
  },
});
```

## NativeWind Integration
- **Tailwind config**: [tailwind.config.js](mdc:tailwind.config.js) is configured with NativeWind v4 preset
- **PostCSS setup**: [postcss.config.js](mdc:postcss.config.js) processes Tailwind directives
- **Global CSS**: [global.css](mdc:global.css) contains Tailwind base imports

## Style Utilities
- **Use styleUtils** from [style/index.ts](mdc:style/index.ts) for dynamic styling
- **Selection patterns**: Use `getSelectionStyle()` for toggle states
- **Color patterns**: Reference `colorPatterns` for consistent theming
