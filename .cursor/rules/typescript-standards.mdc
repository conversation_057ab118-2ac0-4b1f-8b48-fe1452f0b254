---
description: 
globs: 
alwaysApply: false
---
# TypeScript Standards

## ESLint Configuration
The project uses [eslint.config.js](mdc:eslint.config.js) with strict TypeScript rules:

## No Explicit Any Rule
- **Rule**: `@typescript-eslint/no-explicit-any` is enforced
- **Requirement**: Avoid using the `any` type throughout the codebase
- **Solution**: Use proper TypeScript typings instead

```typescript
// ❌ Incorrect: Using explicit any
const handleData = (data: any) => {
  return data.someProperty;
};

// ✅ Correct: Proper typing
interface UserData {
  id: string;
  name: string;
  email: string;
}

const handleData = (data: UserData) => {
  return data.name;
};

// ✅ Correct: Generic types for flexibility
const handleData = <T extends Record<string, unknown>>(data: T) => {
  return data;
};
```

## TypeScript Configuration
- **Config file**: [tsconfig.json](mdc:tsconfig.json) defines project TypeScript settings
- **Parser options**: ESLint uses TypeScript parser with project reference
- **Module system**: Uses ES modules with latest ECMAScript features

## Type Safety Best Practices
```typescript
// ✅ Use proper interface definitions
interface HealthMetric {
  id: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
}

// ✅ Use union types for specific values
type ModalType = 'about' | 'help' | 'usage' | 'notifications' | 'privacy';

// ✅ Use generic constraints
interface ApiResponse<T> {
  data: T;
  status: 'success' | 'error';
  message?: string;
}

// ❌ Avoid any types
const processResponse = (response: any) => response.data;

// ✅ Use proper typing
const processResponse = <T>(response: ApiResponse<T>): T => response.data;
```

## React TypeScript Patterns
```typescript
// ✅ Proper component typing
interface Props {
  title: string;
  onPress?: () => void;
  disabled?: boolean;
}

const CustomButton: React.FC<Props> = ({ title, onPress, disabled = false }) => {
  // Component implementation
};

// ✅ Proper hook typing
const useHealthData = (): {
  data: HealthMetric[];
  loading: boolean;
  error: string | null;
} => {
  // Hook implementation
};
```
