/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./app/**/*.{js,jsx,ts,tsx}", // For Expo Router, scan files in the app directory
    "./components/**/*.{js,jsx,ts,tsx}", // Scan files in a components directory
    // Add any other paths where you use Tailwind classes
  ],
  presets: [require("nativewind/preset")], // Essential for NativeWind v4
  theme: {
    extend: {
      // You can extend your theme here (colors, fonts, etc.)
    },
  },
  plugins: [],
};
