import { UserProfile } from "@/types/user";
import { format, subDays, isSameDay } from "date-fns";
import { nl, enUS } from "date-fns/locale";
import AsyncStorage from "@react-native-async-storage/async-storage";
export interface UserHealthData {
  sleep?: number;
  pills?: number;
  hydration?: number;
  exercise?: number;
}
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof import('@expo/vector-icons').Ionicons.glyphMap;
  isUnlocked: boolean;
  progress?: number;
  maxProgress?: number;
  rarity: "common" | "rare" | "epic" | "legendary";
  unlockedAt?: string;
}
export interface Insight {
  id: string;
  title: string;
  description: string;
  action?: string; // Optional action link or identifier
  // Fields required by InsightsCard
  trend: "up" | "down" | "stable";
  value: string; // e.g., "7 hours sleep", "Mood: Positive"
  change: string; // e.g., "+1 hr", "+5%"
  type: "mood" | "health" | "progress"; // Domain type for UI in InsightsCard
  // Original type, renamed to avoid conflict
  category?: "positive" | "warning" | "tip" | "milestone";
}
export interface DayProgress {
  date: string;
  dayName?: string;
  mood: number | null;
  cravingIntensity: number;
  completed: boolean;
  goalsCompleted?: number;
  goalsTracked?: number;
}
export interface HealthMetric {
  id: string;
  type: "sleep" | "pills" | "hydration" | "exercise";
  value: number;
  maxValue: number | null;
  unit: string;
  trend: "up" | "down" | "stable";
}
const HEALTH_DATA_PREFIX = "healthData_";
export const saveUserHealthData = async (
  dateString: string,
  data: UserHealthData
): Promise<void> => {
  try {
    const key = `${HEALTH_DATA_PREFIX}${dateString}`;
    await AsyncStorage.setItem(key, JSON.stringify(data));
  } catch (_error) {
    console.error("Failed to save health data to AsyncStorage", _error);
    // Optionally, rethrow or handle error appropriately for better UX
  }
};
export const loadUserHealthData = async (
  dateString: string
): Promise<UserHealthData | null> => {
  try {
    const key = `${HEALTH_DATA_PREFIX}${dateString}`;
    const jsonData = await AsyncStorage.getItem(key);
    if (jsonData) {
      return JSON.parse(jsonData) as UserHealthData;
    }
    return null;
  } catch (_error) {
    console.error("Failed to load health data from AsyncStorage", _error);
    return null;
  }
};
export const generateAchievements = (
  profile: UserProfile,
  diffDays: number,
  language: string
): Achievement[] => {
  const achievements: Achievement[] = [];
  // Days-based achievements
  achievements.push({
    id: "first-day",
    title: language === "nl" ? "Eerste Dag" : "First Day",
    description:
      language === "nl" ? "Je eerste dag voltooid!" : "First day completed!",
    icon: "star",
    isUnlocked: diffDays >= 1,
    rarity: "common",
    unlockedAt: diffDays >= 1 ? new Date().toISOString() : undefined,
  });
  achievements.push({
    id: "first-week",
    title: language === "nl" ? "Eerste Week" : "First Week",
    description: language === "nl" ? "7 dagen volgehouden!" : "7 days strong!",
    icon: "trophy",
    isUnlocked: diffDays >= 7,
    progress: Math.min(diffDays, 7),
    maxProgress: 7,
    rarity: "common",
    unlockedAt: diffDays >= 7 ? new Date().toISOString() : undefined,
  });
  achievements.push({
    id: "first-month",
    title: language === "nl" ? "Eerste Maand" : "First Month",
    description: language === "nl" ? "30 dagen bereikt!" : "30 days achieved!",
    icon: "medal",
    isUnlocked: diffDays >= 30,
    progress: Math.min(diffDays, 30),
    maxProgress: 30,
    rarity: "rare",
    unlockedAt: diffDays >= 30 ? new Date().toISOString() : undefined,
  });
  achievements.push({
    id: "three-months",
    title: language === "nl" ? "Drie Maanden" : "Three Months",
    description: language === "nl" ? "90 dagen nuchter!" : "90 days sober!",
    icon: "ribbon",
    isUnlocked: diffDays >= 90,
    progress: Math.min(diffDays, 90),
    maxProgress: 90,
    rarity: "epic",
    unlockedAt: diffDays >= 90 ? new Date().toISOString() : undefined,
  });
  achievements.push({
    id: "one-year",
    title: language === "nl" ? "Een Jaar" : "One Year",
    description:
      language === "nl"
        ? "365 dagen - ongelooflijk!"
        : "365 days - incredible!",
    icon: "flash",
    isUnlocked: diffDays >= 365,
    progress: Math.min(diffDays, 365),
    maxProgress: 365,
    rarity: "legendary",
    unlockedAt: diffDays >= 365 ? new Date().toISOString() : undefined,
  });
  // Mood-based achievements
  const moodEntries = profile.moodEntries || [];
  const recentMoodEntries = moodEntries.slice(-7);
  const avgMood =
    recentMoodEntries.length > 0
      ? recentMoodEntries.reduce((sum, entry) => sum + entry.mood, 0) /
        recentMoodEntries.length
      : 0;
  achievements.push({
    id: "mood-master",
    title: language === "nl" ? "Stemming Meester" : "Mood Master",
    description:
      language === "nl"
        ? "Gemiddelde stemming > 4 deze week"
        : "Average mood > 4 this week",
    icon: "star",
    isUnlocked: avgMood >= 4,
    rarity: "rare",
    unlockedAt: avgMood >=4 ? new Date().toISOString() : undefined,
  });
  // Savings-based achievements
  const savedAmount = calculateSavings(profile, diffDays);
  achievements.push({
    id: "money-saver",
    title: language === "nl" ? "Geld Bespaarder" : "Money Saver",
    description: language === "nl" ? "€100+ bespaard" : "$100+ saved",
    icon: "location",
    isUnlocked: savedAmount >= 100,
    rarity: "common",
    unlockedAt: savedAmount >= 100 ? new Date().toISOString() : undefined,
  });
  return achievements;
};
// Helper function to get health data for the last N days
async function getLastNDaysHealthData(days: number): Promise<UserHealthData[]> {
  const data: UserHealthData[] = [];
  const today = new Date();
  for (let i = 0; i < days; i++) {
    const targetDate = subDays(today, i);
    const dateString = format(targetDate, "yyyy-MM-dd");
    const dayData = await loadUserHealthData(dateString);
    if (dayData) {
      data.push(dayData);
    }
  }
  return data; // Returns data from today (index 0) backwards
}
export const generateInsights = async (
  profile: UserProfile,
  diffDays: number,
  language: string
): Promise<Insight[]> => {
  const insights: Insight[] = [];
  const today = new Date();
  const todayDateString = format(today, "yyyy-MM-dd");
  const yesterdayDateString = format(subDays(today, 1), "yyyy-MM-dd");
  // --- 1. Recovery Progress Insight ---
  insights.push({
    id: "recovery-progress",
    title: language === "nl" ? "Herstel Vooruitgang" : "Recovery Progress",
    value: `${diffDays} ${language === "nl" ? "dagen" : "days"}`,
    change: diffDays > 0 ? `+1` : "0", // As per image, trend is +1 if days > 0
    trend: diffDays > 0 ? "up" : "stable",
    description:
      language === "nl"
        ? `Je bent al ${diffDays} dagen nuchter! Ga zo door.`
        : `You've been sober for ${diffDays} days! Keep up the great work.`,
    type: "progress",
    category: "milestone",
  });
  // --- Fetch health data for Sleep and Stress ---
  const last7DaysData = await getLastNDaysHealthData(7);
  const todayData = await loadUserHealthData(todayDateString);
  const yesterdayData = await loadUserHealthData(yesterdayDateString);
  // --- 2. Sleep Quality Insight ---
  const loggedSleepLast7Days = last7DaysData
    .map((d) => d.sleep)
    .filter((s) => typeof s === "number") as number[];
  if (loggedSleepLast7Days.length > 0) {
    const avgSleep =
      loggedSleepLast7Days.reduce((sum, s) => sum + s, 0) /
      loggedSleepLast7Days.length;
    const sleepTarget = profile.sleepGoal || 7.5; // Use profile's sleep goal or default
    let sleepTrend: "up" | "down" | "stable" = "stable";
    let sleepChangeDisplay = "";
    if (todayData?.sleep !== undefined && yesterdayData?.sleep !== undefined) {
      const dailyDiff = todayData.sleep - yesterdayData.sleep;
      sleepChangeDisplay = `${dailyDiff >= 0 ? "+" : ""}${dailyDiff.toFixed(
        1
      )}h`;
      if (dailyDiff > 0.25) sleepTrend = "up";
      else if (dailyDiff < -0.25) sleepTrend = "down";
    } else if (todayData?.sleep !== undefined) {
      const dailyDiffFromTarget = todayData.sleep - sleepTarget;
      sleepChangeDisplay = `${
        dailyDiffFromTarget >= 0 ? "+" : ""
      }${dailyDiffFromTarget.toFixed(1)}h`;
      if (dailyDiffFromTarget > 0.25) sleepTrend = "up";
      else if (dailyDiffFromTarget < -0.25) sleepTrend = "down";
    } else {
      // If no today's data, use average vs target for trend display
      const avgDiffFromTarget = avgSleep - sleepTarget;
      sleepChangeDisplay = `${
        avgDiffFromTarget >= 0 ? "+" : ""
      }${avgDiffFromTarget.toFixed(1)}h`;
      if (avgDiffFromTarget > 0.25) sleepTrend = "up";
      else if (avgDiffFromTarget < -0.25) sleepTrend = "down";
    }
    insights.push({
      id: "sleep-quality",
      title: language === "nl" ? "Slaapkwaliteit" : "Sleep Quality",
      value: `${avgSleep.toFixed(1)}h`,
      change: sleepChangeDisplay,
      trend: sleepTrend,
      description:
        language === "nl"
          ? `Je slaapt gemiddeld ${avgSleep.toFixed(1)} uur per nacht. Dit is ${
              avgSleep >= sleepTarget - 0.5
                ? "goed"
                : avgSleep >= sleepTarget - 1.5
                ? "redelijk"
                : "laag"
            }.`
          : `You're averaging ${avgSleep.toFixed(
              1
            )} hours of sleep per night. This is ${
              avgSleep >= sleepTarget - 0.5
                ? "good"
                : avgSleep >= sleepTarget - 1.5
                ? "okay"
                : "low"
            }.`,
      type: "health",
      category: "positive",
    });
  } else if (todayData?.sleep !== undefined) {
    const currentSleep = todayData.sleep;
    insights.push({
      id: "sleep-quality",
      title: language === "nl" ? "Slaapkwaliteit" : "Sleep Quality",
      value: `${currentSleep.toFixed(1)}h`,
      change: "",
      trend: "stable",
      description:
        language === "nl"
          ? `Vannacht ${currentSleep.toFixed(1)} uur geslapen.`
          : `Slept ${currentSleep.toFixed(1)} hours last night.`,
      type: "health",
      category: "positive",
    });
  }
  // --- 3. Stress Levels Insight ---
  if (todayData?.pills !== undefined) {
    const currentPills = todayData.pills;
    let pillsTrend: "up" | "down" | "stable" = "stable";
    let pillsChange = "0";
    if (yesterdayData?.pills !== undefined) {
      const diff = currentPills - yesterdayData.pills;
      if (diff > 0.5) {
        pillsTrend = "up";
        pillsChange = `+${diff.toFixed(0)}`;
      } else if (diff < -0.5) {
        pillsTrend = "down";
        pillsChange = `${diff.toFixed(0)}`;
      }
    }
    insights.push({
      id: "pills-tracking",
      title: language === "nl" ? "Medicatie" : "Medication",
      value: `${currentPills}`,
      change: pillsChange,
      trend: pillsTrend,
      description:
        language === "nl"
          ? `Je hebt ${currentPills} pillen genomen. ${
              currentPills <= 2 ? "Goed gedaan!" : "Let op je dosering."
            }`
          : `You took ${currentPills} pills. ${
              currentPills <= 2 ? "Well done!" : "Monitor your dosage."
            }`,
      type: "health",
      category:
        currentPills <= 2 ? "positive" : currentPills <= 4 ? "tip" : "warning",
    });
  }
  const orderedInsights: Insight[] = [];
  const insightOrder = ["recovery-progress", "sleep-quality", "pills-tracking"];
  for (const id of insightOrder) {
    const found = insights.find((_insight) => _insight.id === id);
    if (found) {
      orderedInsights.push(found);
    }
  }
  return orderedInsights.slice(0, 3);
};
export const generateWeeklyProgress = (profile: UserProfile): DayProgress[] => {
  const today = new Date();
  const weekData: DayProgress[] = [];
  for (let dayIndex = 6; dayIndex >= 0; dayIndex--) {
    const day = subDays(today, dayIndex);
    const dayString = format(day, "yyyy-MM-dd");
    const moodEntry = profile.moodEntries?.find((entry) =>
      isSameDay(new Date(entry.date), day)
    );
    let goalsCompleted = 0;
    let goalsTracked = 0;
    profile.healthGoals?.forEach((goal) => {
      if (goal.enabled) {
        goalsTracked++;
        const historyEntry = goal.history?.find((h) => h.date === dayString);
        if (historyEntry && goal.target && historyEntry.value >= goal.target) {
          goalsCompleted++;
        }
      }
    });
    weekData.push({
      date: dayString,
      dayName: format(day, "EEE", {
        locale: profile.language === "nl" ? nl : enUS,
      }),
      mood: moodEntry?.mood ?? null,
      cravingIntensity: 0, // Default value
      completed: goalsCompleted > 0, // Convert to boolean based on goals completed
      goalsCompleted,
      goalsTracked,
    });
  }
  return weekData;
};
export const generateHealthMetrics = async (
  profile: UserProfile,
  dateString: string
): Promise<HealthMetric[]> => {
  const storedData = await loadUserHealthData(dateString);
  const userGoals = profile.healthGoals || [];
  // If no goals are set or enabled in profile, return empty or a default message structure
  const enabledGoals = userGoals.filter((goal) => goal.enabled);
  if (enabledGoals.length === 0) {
    // Return a structure that HealthMetricsCard can identify as "no active goals"
    // Or, for now, let's return an empty array, and the HomeScreen will show "Log Health Data"
    return [];
  }
  return enabledGoals.map((goal) => {
    let iconType: "sleep" | "pills" | "hydration" | "exercise" = "exercise"; // Default
    if (goal.id === "sleep") iconType = "sleep";
    else if (goal.id === "exercise") iconType = "exercise";
    else if (goal.id === "pills") iconType = "pills";
    else if (goal.id === "waterIntake") iconType = "hydration";
    // Force hydration to use "glasses" unit regardless of onboarding setting
    let displayUnit = goal.unit;
    if (goal.id === "waterIntake") {
      displayUnit = "glasses";
    }
    // Map goal ID to stored data key
    let dataKey: keyof UserHealthData;
    if (goal.id === "waterIntake") {
      dataKey = "hydration"; // Map waterIntake goal to hydration data
    } else {
      dataKey = goal.id as keyof UserHealthData;
    }
    return {
      id: goal.id,
      type: iconType,
      value: storedData?.[dataKey] ?? 0,
      maxValue: (goal.target ?? (goal.id === "sleep"
          ? 8
          : goal.id === "exercise"
          ? 30
          : goal.id === "pills"
          ? 2
          : goal.id === "waterIntake"
          ? 8
          : 0)) as number,
      unit: displayUnit,
      trend: "stable" as const,
    };
  });
};
const calculateSavings = (profile: UserProfile, diffDays: number): number => {
  let savedAmount = 0;
  if (profile.usageCost && profile.usageCost > 0 && profile.costFrequency) {
    if (profile.costFrequency === "daily") {
      savedAmount = profile.usageCost * diffDays;
    } else if (profile.costFrequency === "weekly") {
      savedAmount = profile.usageCost * (diffDays / 7);
    } else if (profile.costFrequency === "monthly") {
      savedAmount = profile.usageCost * (diffDays / 30);
    }
  }
  return savedAmount;
};