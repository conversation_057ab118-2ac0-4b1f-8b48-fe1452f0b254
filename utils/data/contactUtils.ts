import { AlertTriangle, Users2, UserPlus, Briefcase, LucideIcon } from 'lucide-react-native';
export type ContactCategory = 'emergency' | 'family' | 'friends' | 'work';
export interface ContactCategoryConfig {
  size?: number;
  strokeWidth?: number;
}
export interface ContactCategoryInfo {
  IconComponent: LucideIcon;
  color: string;
  name: string;
  gradient: [string, string];
}
export const getContactCategoryIconComponent = (category: ContactCategory): LucideIcon => {
  switch (category) {
    case 'emergency':
      return AlertTriangle;
    case 'family':
      return Users2;
    case 'friends':
      return UserPlus;
    case 'work':
      return Briefcase;
    default:
      return AlertTriangle;
  }
};
interface ColorScheme {
  danger: string;
  info: string;
  success: string;
  warning: string;
  dangerDark?: string;
  infoDark?: string;
  successDark?: string;
  warningDark?: string;
}
export const getContactCategoryColor = (category: ContactCategory, colors: ColorScheme) => {
  switch (category) {
    case 'emergency':
      return colors.danger;
    case 'family':
      return colors.info;
    case 'friends':
      return colors.success;
    case 'work':
      return colors.warning;
    default:
      return colors.danger;
  }
};
// ✅ MIGRATED: Use translationService.getContactCategoryName() instead
export const getContactCategoryName = (category: ContactCategory, language: string) => {
  // This function is deprecated - use translationService.getContactCategoryName() or useTranslation().getContactCategoryName()
  console.warn('getContactCategoryName with language parameter is deprecated. Use translationService.getContactCategoryName() instead.');
  
  if (language === 'nl') {
    switch (category) {
      case 'emergency': return 'Nood';
      case 'family': return 'Familie';
      case 'friends': return 'Vrienden';
      case 'work': return 'Werk';
      default: return 'Nood';
    }
  } else {
    switch (category) {
      case 'emergency': return 'Emergency';
      case 'family': return 'Family';
      case 'friends': return 'Friends';
      case 'work': return 'Work';
      default: return 'Emergency';
    }
  }
};
export const getContactCategoryGradient = (category: ContactCategory, colors: ColorScheme): [string, string] => {
  switch (category) {
    case 'emergency':
      return [colors.danger, colors.dangerDark || colors.danger];
    case 'family':
      return [colors.info, colors.infoDark || colors.info];
    case 'friends':
      return [colors.success, colors.successDark || colors.success];
    case 'work':
      return [colors.warning, colors.warningDark || colors.warning];
    default:
      return [colors.danger, colors.dangerDark || colors.danger];
  }
};
export const getContactCategoryInfo = (
  category: ContactCategory,
  colors: ColorScheme,
  language: string
): ContactCategoryInfo => {
  const color = getContactCategoryColor(category, colors);
  
  return {
    IconComponent: getContactCategoryIconComponent(category),
    color,
    name: getContactCategoryName(category, language),
    gradient: getContactCategoryGradient(category, colors),
  };
};
export const contactCategories: Array<{
  id: ContactCategory;
  label: string;
  labelNL: string;
}> = [
  { id: 'emergency', label: 'Emergency', labelNL: 'Nood' },
  { id: 'family', label: 'Family', labelNL: 'Familie' },
  { id: 'friends', label: 'Friends', labelNL: 'Vrienden' },
  { id: 'work', label: 'Work', labelNL: 'Werk' },
]; 