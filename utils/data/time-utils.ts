/**
 * Formats a duration in milliseconds to a string in the format MM:SS
 * @param milliseconds Duration in milliseconds
 * @returns Formatted string in MM:SS format
 */
export const formatDuration = (milliseconds: number): string => {
  if (!milliseconds || isNaN(milliseconds)) {
    return '00:00';
  }
  
  const totalSeconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};
/**
 * Formats a date string to a localized date string
 * @param dateString Date string in ISO format
 * @param locale Locale string (e.g. 'en-US', 'nl-NL')
 * @returns Formatted date string
 */
export const formatDate = (dateString: string, locale: string = 'en-US'): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (_error) {
    console.error('Error formatting date:', _error);
    return dateString;
  }
};
/**
 * Formats a date string to a localized time string
 * @param dateString Date string in ISO format
 * @param locale Locale string (e.g. 'en-US', 'nl-NL')
 * @returns Formatted time string
 */
export const formatTime = (dateString: string, locale: string = 'en-US'): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleTimeString(locale, {
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (_error) {
    console.error('Error formatting time:', _error);
    return '';
  }
};
/**
 * Formats a date string to a relative time string (e.g. "2 days ago")
 * @param dateString Date string in ISO format
 * @param language Language code ('en' or 'nl')
 * @returns Relative time string
 */
export const formatRelativeTime = (dateString: string, language: string = 'en'): string => {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffSecs < 60) {
      return language === 'nl' ? 'zojuist' : 'just now';
    } else if (diffMins < 60) {
      return language === 'nl' 
        ? `${diffMins} ${diffMins === 1 ? 'minuut' : 'minuten'} geleden`
        : `${diffMins} ${diffMins === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffHours < 24) {
      return language === 'nl'
        ? `${diffHours} ${diffHours === 1 ? 'uur' : 'uren'} geleden`
        : `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffDays < 7) {
      return language === 'nl'
        ? `${diffDays} ${diffDays === 1 ? 'dag' : 'dagen'} geleden`
        : `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
    } else {
      return formatDate(dateString, language === 'nl' ? 'nl-NL' : 'en-US');
    }
  } catch (_error) {
    console.error('Error formatting relative time:', _error);
    return dateString;
  }
};