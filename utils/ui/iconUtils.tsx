import React from "react";
import {
  DollarSign,
  Wine,
  Cigarette,
  Coffee,
  Wallet,
  TrendingUp,
  TrendingDown,
  Minus,
  Calendar,
  Clock,
  Award,
  Star,
  Zap,
  Trophy,
  Target,
  Heart,
  Shield,
  Wind,
  Brain,
  Anchor,
  Scan,
  Eye,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "lucide-react-native";

// ============================================================================
// SUBSTANCE ICONS
// ============================================================================

export interface SubstanceIconConfig {
  size?: number;
  color?: string;
  strokeWidth?: number;
}

export const getSubstanceIcon = (
  substanceType?: string,
  config: SubstanceIconConfig = {}
) => {
  const { size = 24, color = "#000", strokeWidth = 2 } = config;

  if (!substanceType) {
    return <DollarSign size={size} color={color} strokeWidth={strokeWidth} />;
  }

  const type = substanceType.toLowerCase();

  if (
    type.includes("alcohol") ||
    type.includes("drink") ||
    type.includes("wine") ||
    type.includes("beer")
  ) {
    return <Wine size={size} color={color} strokeWidth={strokeWidth} />;
  } else if (
    type.includes("smoke") ||
    type.includes("cigarette") ||
    type.includes("tobacco")
  ) {
    return <Cigarette size={size} color={color} strokeWidth={strokeWidth} />;
  } else if (type.includes("coffee") || type.includes("caffeine")) {
    return <Coffee size={size} color={color} strokeWidth={strokeWidth} />;
  } else {
    return <DollarSign size={size} color={color} strokeWidth={strokeWidth} />;
  }
};

// Alternative version that returns Wallet instead of DollarSign for savings contexts
export const getSubstanceIconForSavings = (
  substanceType?: string,
  config: SubstanceIconConfig = {}
) => {
  const { size = 24, color = "#000", strokeWidth = 2 } = config;

  if (!substanceType) {
    return <Wallet size={size} color={color} strokeWidth={strokeWidth} />;
  }

  const type = substanceType.toLowerCase();

  if (
    type.includes("alcohol") ||
    type.includes("drink") ||
    type.includes("wine") ||
    type.includes("beer")
  ) {
    return <Wine size={size} color={color} strokeWidth={strokeWidth} />;
  } else if (
    type.includes("smoke") ||
    type.includes("cigarette") ||
    type.includes("tobacco")
  ) {
    return <Cigarette size={size} color={color} strokeWidth={strokeWidth} />;
  } else if (type.includes("coffee") || type.includes("caffeine")) {
    return <Coffee size={size} color={color} strokeWidth={strokeWidth} />;
  } else {
    return <Wallet size={size} color={color} strokeWidth={strokeWidth} />;
  }
};

// ============================================================================
// TREND ICONS
// ============================================================================

export type TrendType = "up" | "down" | "stable" | "improving" | "declining";

export interface TrendIconConfig {
  size?: number;
  color?: string;
  strokeWidth?: number;
}

export const getTrendIcon = (
  trend: TrendType,
  config: TrendIconConfig = {}
) => {
  const { size = 16, color = "#000", strokeWidth = 2 } = config;

  switch (trend) {
    case "up":
    case "improving":
      return <TrendingUp size={size} color={color} strokeWidth={strokeWidth} />;
    case "down":
    case "declining":
      return (
        <TrendingDown size={size} color={color} strokeWidth={strokeWidth} />
      );
    case "stable":
    default:
      return <Minus size={size} color={color} strokeWidth={strokeWidth} />;
  }
};

// ============================================================================
// MILESTONE ICONS
// ============================================================================

export type MilestoneIconType =
  | "calendar"
  | "clock"
  | "award"
  | "star"
  | "zap"
  | "trophy"
  | "target"
  | "heart"
  | "shield";

export interface MilestoneIconConfig {
  size?: number;
  color?: string;
  strokeWidth?: number;
}

export const getMilestoneIcon = (
  iconName: MilestoneIconType,
  config: MilestoneIconConfig = {}
) => {
  const { size = 24, color = "#000", strokeWidth = 2 } = config;

  switch (iconName) {
    case "calendar":
      return <Calendar size={size} color={color} strokeWidth={strokeWidth} />;
    case "clock":
      return <Clock size={size} color={color} strokeWidth={strokeWidth} />;
    case "award":
      return <Award size={size} color={color} strokeWidth={strokeWidth} />;
    case "star":
      return <Star size={size} color={color} strokeWidth={strokeWidth} />;
    case "zap":
      return <Zap size={size} color={color} strokeWidth={strokeWidth} />;
    case "trophy":
      return <Trophy size={size} color={color} strokeWidth={strokeWidth} />;
    case "target":
      return <Target size={size} color={color} strokeWidth={strokeWidth} />;
    case "heart":
      return <Heart size={size} color={color} strokeWidth={strokeWidth} />;
    case "shield":
      return <Shield size={size} color={color} strokeWidth={strokeWidth} />;
    default:
      return <Trophy size={size} color={color} strokeWidth={strokeWidth} />;
  }
};

// ============================================================================
// EXERCISE TYPE ICONS
// ============================================================================

export type ExerciseType =
  | "breathing"
  | "meditation"
  | "grounding"
  | "bodyscan"
  | "visualization"
  | "gratitude"
  | "muscle-relaxation"
  | "custom";

export interface ExerciseIconConfig {
  size?: number;
  color?: string;
  strokeWidth?: number;
}

export const getExerciseIcon = (
  type: ExerciseType,
  config: ExerciseIconConfig = {}
) => {
  const { size = 24, color = "#000", strokeWidth = 2 } = config;

  switch (type) {
    case "breathing":
      return <Wind size={size} color={color} strokeWidth={strokeWidth} />;
    case "meditation":
      return <Brain size={size} color={color} strokeWidth={strokeWidth} />;
    case "grounding":
      return <Anchor size={size} color={color} strokeWidth={strokeWidth} />;
    case "bodyscan":
      return <Scan size={size} color={color} strokeWidth={strokeWidth} />;
    case "visualization":
      return <Eye size={size} color={color} strokeWidth={strokeWidth} />;
    case "gratitude":
      return <Heart size={size} color={color} strokeWidth={strokeWidth} />;
    case "muscle-relaxation":
      return <Dumbbell size={size} color={color} strokeWidth={strokeWidth} />;
    case "custom":
    default:
      return <Sparkles size={size} color={color} strokeWidth={strokeWidth} />;
  }
};

// ============================================================================
// INSIGHT TYPE ICONS
// ============================================================================

export type InsightType = "mood" | "health" | "progress";

export interface InsightIconConfig {
  size?: number;
  color?: string;
  strokeWidth?: number;
}

export const getInsightTypeIcon = (
  type: InsightType,
  config: InsightIconConfig = {}
) => {
  const { size = 20, color = "#000", strokeWidth = 2 } = config;

  switch (type) {
    case "mood":
      return <Heart size={size} color={color} strokeWidth={strokeWidth} />;
    case "health":
      return <Brain size={size} color={color} strokeWidth={strokeWidth} />;
    case "progress":
      return <Target size={size} color={color} strokeWidth={strokeWidth} />;
    default:
      return <Brain size={size} color={color} strokeWidth={strokeWidth} />;
  }
};
