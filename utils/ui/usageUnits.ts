// ============================================================================
// USAGE UNITS UTILITY
// ============================================================================

export interface UsageUnit {
  label: string;
  value: string;
  labelNL: string;
}

export type AddictionType =
  | "Alcohol"
  | "Tobacco"
  | "Cannabis"
  | "Opioids"
  | "Stimulants"
  | "Gambling"
  | "Social Media"
  | "Other";

// Centralized usage units for different addiction types
export const usageUnits: Record<AddictionType, UsageUnit[]> = {
  Alcohol: [
    { label: "Bottles", value: "bottles", labelNL: "Flessen" },
    { label: "Glasses", value: "glasses", labelNL: "Glazen" },
    { label: "Cans", value: "cans", labelNL: "Blikjes" },
    { label: "Shots", value: "shots", labelNL: "Shots" },
    { label: "Units", value: "units", labelNL: "Eenheden" },
  ],
  Tobacco: [
    { label: "Cigarettes", value: "cigarettes", labelNL: "Sigaretten" },
    { label: "Packs", value: "packs", labelNL: "Pakjes" },
    { label: "Cigars", value: "cigars", labelNL: "Sigaren" },
    { label: "Grams", value: "grams", labelNL: "Grammen" },
  ],
  Cannabis: [
    { label: "Joints", value: "joints", labelNL: "Joints" },
    { label: "Grams", value: "grams", labelNL: "Grammen" },
    { label: "Sessions", value: "sessions", labelNL: "Sessies" },
  ],
  Opioids: [
    { label: "Pills", value: "pills", labelNL: "Pillen" },
    { label: "Doses", value: "doses", labelNL: "Doses" },
    { label: "Milligrams", value: "mg", labelNL: "Milligrammen" },
  ],
  Stimulants: [
    { label: "Pills", value: "pills", labelNL: "Pillen" },
    { label: "Lines", value: "lines", labelNL: "Lijnen" },
    { label: "Grams", value: "grams", labelNL: "Grammen" },
    { label: "Doses", value: "doses", labelNL: "Doses" },
  ],
  Gambling: [
    { label: "Sessions", value: "sessions", labelNL: "Sessies" },
    { label: "Bets", value: "bets", labelNL: "Weddenschappen" },
    { label: "Hours", value: "hours", labelNL: "Uren" },
  ],
  "Social Media": [
    { label: "Hours", value: "hours", labelNL: "Uren" },
    { label: "Sessions", value: "sessions", labelNL: "Sessies" },
    { label: "Checks", value: "checks", labelNL: "Controles" },
  ],
  Other: [
    { label: "Units", value: "units", labelNL: "Eenheden" },
    { label: "Times", value: "times", labelNL: "Keren" },
    { label: "Hours", value: "hours", labelNL: "Uren" },
    { label: "Sessions", value: "sessions", labelNL: "Sessies" },
  ],
};

/**
 * Get available units for a specific addiction type
 * @param addiction - The addiction type
 * @returns Array of usage units for the addiction type
 */
export const getUnitsForAddiction = (addiction?: string): UsageUnit[] => {
  if (!addiction) return usageUnits.Other;

  // Direct match
  if (addiction in usageUnits) {
    return usageUnits[addiction as AddictionType];
  }

  // Fallback for partial matches or custom addictions
  return usageUnits.Other;
};

/**
 * Get available units for a substance type (more flexible matching)
 * @param substanceType - The substance type (can be more descriptive)
 * @returns Array of usage units for the substance type
 */
export const getUnitsForSubstanceType = (
  substanceType?: string
): UsageUnit[] => {
  if (!substanceType) return usageUnits.Other;

  // Check if the substance type matches any of our predefined categories
  for (const [category, units] of Object.entries(usageUnits)) {
    if (substanceType.includes(category)) {
      return units;
    }
  }

  return usageUnits.Other;
};
