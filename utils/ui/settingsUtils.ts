import { Platform } from 'react-native';
import * as Haptics from 'expo-haptics';

// Standardized color scheme interface for settings components
export interface SettingsColorScheme {
  // Core colors
  background: string;
  card: string;
  cardGradient?: string;
  border: string;
  text: string;
  textSecondary?: string;
  primary: string;
  primaryDark?: string;
  muted: string;
  
  // Status colors
  success?: string;
  successDark?: string;
  danger?: string;
  dangerDark?: string;
  warning?: string;
  info?: string;
  secondary?: string;
  accent?: string;
}

// Language type for settings
export type SettingsLanguage = 'en' | 'nl';

// Common haptic feedback function
export const triggerHapticFeedback = (type: 'selection' | 'impact' | 'notification' = 'selection') => {
  if (Platform.OS === 'web') return;
  
  switch (type) {
    case 'selection':
      Haptics.selectionAsync();
      break;
    case 'impact':
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      break;
    case 'notification':
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      break;
  }
};

// ✅ MIGRATED: Use translationService.translate() instead
export const getSettingsText = (key: string, language: SettingsLanguage): string => {
  // This function is deprecated - use translationService.translate() or useTranslation().t()
  console.warn('getSettingsText with language parameter is deprecated. Use translationService.translate() instead.');
  
  const translations: Record<string, Record<SettingsLanguage, string>> = {
    cancel: { en: 'Cancel', nl: 'Annuleren' },
    save: { en: 'Save', nl: 'Opslaan' },
    close: { en: 'Close', nl: 'Sluiten' },
    edit: { en: 'Edit', nl: 'Bewerken' },
    delete: { en: 'Delete', nl: 'Verwijderen' },
    confirm: { en: 'Confirm', nl: 'Bevestigen' },
    error: { en: 'Error', nl: 'Fout' },
    success: { en: 'Success', nl: 'Succes' },
    loading: { en: 'Loading...', nl: 'Laden...' },
    required: { en: 'Required', nl: 'Verplicht' },
    optional: { en: 'Optional', nl: 'Optioneel' },
    
    // Settings specific
    notifications: { en: 'Notifications', nl: 'Notificaties' },
    privacy: { en: 'Privacy', nl: 'Privacy' },
    about: { en: 'About', nl: 'Over' },
    help: { en: 'Help & Support', nl: 'Help & Ondersteuning' },
    profile: { en: 'Profile', nl: 'Profiel' },
    usage: { en: 'Usage Details', nl: 'Gebruiksgegevens' },
    
    // Common descriptions
    enableNotifications: { 
      en: 'Enable notifications to stay updated', 
      nl: 'Schakel notificaties in om op de hoogte te blijven' 
    },
    dataPrivacy: { 
      en: 'Control how your data is used', 
      nl: 'Beheer hoe je gegevens worden gebruikt' 
    },
  };
  
  return translations[key]?.[language] || key;
};

// Validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

export const validateRequired = (value: string): boolean => {
  return value.trim().length > 0;
};

// Settings state management utilities
export const createSettingsState = <T extends Record<string, unknown>>(
  initialState: T,
  profileSettings?: Partial<T>
): T => {
  return { ...initialState, ...profileSettings };
};

export const createToggleHandler = <T extends Record<string, boolean>>(
  setState: React.Dispatch<React.SetStateAction<T>>
) => {
  return (key: keyof T, value: boolean) => {
    triggerHapticFeedback('selection');
    setState(prev => ({ ...prev, [key]: value }));
  };
};

// Modal animation configuration
export const getModalConfig = (type: 'standard' | 'fullscreen' | 'sheet' = 'standard') => {
  switch (type) {
    case 'fullscreen':
      return {
        animationType: 'slide' as const,
        presentationStyle: 'fullScreen' as const,
        useAnimation: true,
        useGradient: true,
        maxHeight: '100%' as const,
        borderRadius: 0,
      };
    case 'sheet':
      return {
        animationType: 'slide' as const,
        presentationStyle: 'pageSheet' as const,
        useAnimation: false,
        useGradient: true,
        maxHeight: '90%' as const,
        borderRadius: 24,
      };
    default:
      return {
        animationType: 'slide' as const,
        presentationStyle: 'overFullScreen' as const,
        useAnimation: false,
        useGradient: false,
        maxHeight: '80%' as const,
        borderRadius: 24,
      };
  }
}; 