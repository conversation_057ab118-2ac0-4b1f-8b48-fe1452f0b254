import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import * as ImageManipulator from 'expo-image-manipulator';
/**
 * Gets the file extension from a filename or path
 * @param filename The filename or path
 * @returns The file extension (without the dot)
 */
export const getFileExtension = (filename: string): string => {
  if (!filename) return '';
  return filename.split('.').pop()?.toLowerCase() || '';
};
/**
 * Checks if a file is a PDF based on its filename or MIME type
 * @param fileNameOrType The filename or MIME type
 * @returns True if the file is a PDF
 */
export const isPdfFile = (fileNameOrType: string): boolean => {
  if (!fileNameOrType) return false;
  return fileNameOrType.toLowerCase().includes('pdf');
};
/**
 * Checks if a file is a document (PDF, DOC, DOCX, TXT) based on its filename or MIME type
 * @param fileNameOrType The filename or MIME type
 * @returns True if the file is a document
 */
export const isDocumentFile = (fileNameOrType: string): boolean => {
  if (!fileNameOrType) return false;
  const lowerCase = fileNameOrType.toLowerCase();
  return (
    lowerCase.includes('pdf') ||
    lowerCase.includes('doc') ||
    lowerCase.includes('txt') ||
    lowerCase.includes('text/') ||
    lowerCase.includes('application/msword') ||
    lowerCase.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document')
  );
};
/**
 * Checks if a file is an image based on its filename or MIME type
 * @param fileNameOrType The filename or MIME type
 * @returns True if the file is an image
 */
export const isImageFile = (fileNameOrType: string): boolean => {
  if (!fileNameOrType) return false;
  const lowerCase = fileNameOrType.toLowerCase();
  return (
    lowerCase.includes('image/') ||
    lowerCase.endsWith('.jpg') ||
    lowerCase.endsWith('.jpeg') ||
    lowerCase.endsWith('.png') ||
    lowerCase.endsWith('.gif') ||
    lowerCase.endsWith('.webp')
  );
};
/**
 * Checks if a file is an audio file based on its filename or MIME type
 * @param fileNameOrType The filename or MIME type
 * @returns True if the file is an audio file
 */
export const isAudioFile = (fileNameOrType: string): boolean => {
  if (!fileNameOrType) return false;
  const lowerCase = fileNameOrType.toLowerCase();
  return (
    lowerCase.includes('audio/') ||
    lowerCase.endsWith('.mp3') ||
    lowerCase.endsWith('.wav') ||
    lowerCase.endsWith('.ogg') ||
    lowerCase.endsWith('.m4a')
  );
};
/**
 * Formats a file size in bytes to a human-readable string
 * @param bytes The file size in bytes
 * @returns A formatted string (e.g., "1.5 MB")
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
/**
 * Deletes a file from the file system
 * @param uri The URI of the file to delete
 * @returns A promise that resolves when the file is deleted
 */
export const deleteFile = async (uri: string): Promise<void> => {
  if (Platform.OS === 'web') {
    console.log('File deletion not supported on web');
    return;
  }
  
  try {
    const fileInfo = await FileSystem.getInfoAsync(uri);
    if (fileInfo.exists) {
      await FileSystem.deleteAsync(uri);
    }
  } catch (_error) {
    console.error('Error deleting file:', _error);
    throw _error;
  }
};
/**
 * Compresses an image to reduce its file size
 * @param uri The URI of the image to compress
 * @param quality The quality of the compressed image (0-1)
 * @returns A promise that resolves with the URI of the compressed image
 */
export const compressImage = async (uri: string, quality: number = 0.7): Promise<string> => {
  if (Platform.OS === 'web') {
    // For web, we need to handle base64 images differently
    if (uri.startsWith('data:image')) {
      try {
        // Create an image element to load the data URI
        const img = new Image();
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
          img.src = uri;
        });
        
        // Create a canvas to draw and compress the image
        const canvas = document.createElement('canvas');
        
        // Calculate new dimensions while maintaining aspect ratio
        let width = img.width;
        let height = img.height;
        
        // Limit max dimension to 800px for large images (reduced from 1000px)
        const maxDimension = 800;
        if (width > maxDimension || height > maxDimension) {
          if (width > height) {
            height = Math.round((height * maxDimension) / width);
            width = maxDimension;
          } else {
            width = Math.round((width * maxDimension) / height);
            height = maxDimension;
          }
        }
        
        canvas.width = width;
        canvas.height = height;
        
        // Draw image on canvas
        const ctx = canvas.getContext('2d');
        if (!ctx) throw new Error('Could not get canvas context');
        
        ctx.drawImage(img, 0, 0, width, height);
        
        // Convert to compressed data URL - use lower quality for web
        return canvas.toDataURL('image/jpeg', quality * 0.8); // Further reduce quality
      } catch (_error) {
        console.error('Error compressing image on web:', _error);
        return uri; // Return original if compression fails
      }
    }
    return uri; // Return original for non-data URIs
  }
  
  try {
    const result = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: { width: 1000 } }], // Resize to max width of 1000px
      { compress: quality, format: ImageManipulator.SaveFormat.JPEG }
    );
    
    return result.uri;
  } catch (_error) {
    console.error('Error compressing image:', _error);
    throw _error;
  }
};
/**
 * Gets the maximum file size limit based on the platform
 * @param platform The current platform
 * @returns The maximum file size in bytes
 */
export const getFileSizeLimit = (platform: string): number => {
  // Set higher limits for all platforms to accommodate larger audio files
  if (platform === 'web') {
    return 100 * 1024 * 1024; // 100MB for web (increased from 50MB)
  } else if (platform === 'ios') {
    return 500 * 1024 * 1024; // 500MB for iOS (increased from 200MB)
  } else {
    return 300 * 1024 * 1024; // 300MB for Android and others (increased from 150MB)
  }
};
/**
 * Estimates the size of a data URI string
 * @param dataUri The data URI string
 * @returns The estimated size in bytes
 */
export const estimateDataUriSize = (dataUri: string): number => {
  if (!dataUri) return 0;
  
  // For base64 data URIs, the actual binary size is about 3/4 of the string length
  // after removing the header
  if (dataUri.startsWith('data:')) {
    const base64Data = dataUri.split(',')[1];
    if (base64Data) {
      return Math.ceil(base64Data.length * 0.75);
    }
  }
  
  // For regular strings, just return the string length as an approximation
  return dataUri.length;
};
/**
 * Chunks a large string into smaller pieces
 * @param str The string to chunk
 * @param size The maximum size of each chunk
 * @returns An array of string chunks
 */
export const chunkString = (str: string, size: number): string[] => {
  const chunks = [];
  for (let i = 0; i < str.length; i += size) {
    chunks.push(str.substring(i, i + size));
  }
  return chunks;
};
/**
 * Creates a thumbnail from an image URI
 * @param imageUri The URI of the image
 * @returns A promise that resolves with the URI of the thumbnail
 */
export const createThumbnail = async (imageUri: string): Promise<string> => {
  if (Platform.OS === 'web') {
    if (imageUri.startsWith('data:image')) {
      try {
        // Create an image element to load the data URI
        const img = new Image();
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
          img.src = imageUri;
        });
        
        // Create a canvas to draw and compress the image
        const canvas = document.createElement('canvas');
        
        // Set thumbnail size
        const thumbnailSize = 150;
        
        // Calculate dimensions while maintaining aspect ratio
        let width = img.width;
        let height = img.height;
        
        if (width > height) {
          height = Math.round((height * thumbnailSize) / width);
          width = thumbnailSize;
        } else {
          width = Math.round((width * thumbnailSize) / height);
          height = thumbnailSize;
        }
        
        canvas.width = width;
        canvas.height = height;
        
        // Draw image on canvas
        const ctx = canvas.getContext('2d');
        if (!ctx) throw new Error('Could not get canvas context');
        
        ctx.drawImage(img, 0, 0, width, height);
        
        // Convert to compressed data URL with very low quality for thumbnails
        return canvas.toDataURL('image/jpeg', 0.3);
      } catch (_error) {
        console.error('Error creating thumbnail on web:', _error);
        return imageUri; // Return original if thumbnail creation fails
      }
    }
    return imageUri; // Return original for non-data URIs
  }
  
  try {
    const result = await ImageManipulator.manipulateAsync(
      imageUri,
      [{ resize: { width: 150 } }], // Small thumbnail
      { compress: 0.3, format: ImageManipulator.SaveFormat.JPEG }
    );
    
    return result.uri;
  } catch (_error) {
    console.error('Error creating thumbnail:', _error);
    throw _error;
  }
};