import * as Speech from 'expo-speech';
// Define types for speech options
export interface SpeechOptions {
  language?: string;
  pitch?: number;
  rate?: number;
  voice?: string;
  volume?: number;
  onStart?: () => void;
  onDone?: () => void;
  onStopped?: () => void;
  onError?: (error: unknown) => void;
}
// Default speech options
const defaultOptions: SpeechOptions = {
  language: 'en-US',
  pitch: 1.0,
  rate: 0.9,
  volume: 1.0,
};
/**
 * Speaks the provided text with the given options
 * @param text Text to speak
 * @param options Speech options
 * @returns Promise that resolves when speech starts
 */
export const speak = async (text: string, options: SpeechOptions = {}): Promise<void> => {
  try {
    // Merge default options with provided options
    const mergedOptions = { ...defaultOptions, ...options };
    
    // Start speaking
    await Speech.speak(text, mergedOptions);
  } catch (_error) {
    console.error('Error speaking text:', _error);
    options.onError?.(_error);
  }
};
/**
 * Stops any ongoing speech
 */
export const stop = async (): Promise<void> => {
  try {
    await Speech.stop();
  } catch (_error) {
    console.error('Error stopping speech:', _error);
  }
};
/**
 * Checks if speech is currently in progress
 * @returns Promise that resolves to a boolean indicating if speech is in progress
 */
export const isSpeaking = async (): Promise<boolean> => {
  try {
    return await Speech.isSpeakingAsync();
  } catch (_error) {
    console.error('Error checking if speaking:', _error);
    return false;
  }
};
/**
 * Checks if speech synthesis is available on the device
 * @returns Promise that resolves to a boolean indicating if speech is available
 */
export const isSpeechAvailable = async (): Promise<boolean> => {
  try {
    // There's no direct method to check availability in expo-speech
    // We'll assume it's available if the module loaded successfully
    // In a real app, you might want to try a test speech and catch errors
    return true;
  } catch (_error) {
    console.error('Error checking speech availability:', _error);
    return false;
  }
};
/**
 * Gets available voices for speech synthesis
 * @param language Optional language code to filter voices
 * @returns Promise that resolves to an array of available voices
 */
export const getVoices = async (language?: string): Promise<Speech.Voice[]> => {
  try {
    const voices = await Speech.getAvailableVoicesAsync();
    
    if (language) {
      return voices.filter(voice => voice.language.startsWith(language));
    }
    
    return voices;
  } catch (_error) {
    console.error('Error getting available voices:', _error);
    return [];
  }
};
/**
 * Utility to create a speech instance with consistent options
 * @param defaultOpts Default options for this instance
 * @returns Object with speak, stop, and isSpeaking methods
 */
export const createSpeechInstance = (defaultOpts: SpeechOptions = {}) => {
  const instanceDefaults = { ...defaultOptions, ...defaultOpts };
  
  return {
    speak: (text: string, options: SpeechOptions = {}) => 
      speak(text, { ...instanceDefaults, ...options }),
    stop,
    isSpeaking,
    getVoices,
  };
};