// Common UI component prop types
export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: "primary" | "secondary" | "outline" | "ghost";
  size?: "small" | "medium" | "large";
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ComponentType;
}

export interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: "small" | "medium" | "large" | "fullscreen";
}

export interface InputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  disabled?: boolean;
  multiline?: boolean;
  secureTextEntry?: boolean;
}

export interface SelectOption {
  label: string;
  value: string;
  icon?: React.ComponentType;
}

export interface SelectorProps {
  options: SelectOption[];
  selectedValue?: string;
  onSelect: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

// Animation and transition types
export interface AnimationConfig {
  duration?: number;
  delay?: number;
  easing?: string;
}

export interface TransitionProps {
  visible: boolean;
  children: React.ReactNode;
  config?: AnimationConfig;
} 