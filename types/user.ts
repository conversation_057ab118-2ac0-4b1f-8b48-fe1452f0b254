import { Document, MediaFile } from "./media";
import { MindfulnessExercise, CompletedExercise } from "./mindfulness";
import { HealthMetric, HealthGoal } from "./health";

export type ContactCategory = "emergency" | "family" | "friends" | "work";

export interface UserProfile {
  name?: string;
  sobrietyDate?: string | null;
  addiction?: string;
  substanceType?: string;
  recoveryGoalType?: "Abstinence" | "Harm Reduction";
  language?: "en" | "nl";
  usageAmount?: string;
  usageQuantity?: number;
  usageUnit?: string;
  usageCost?: number;
  costFrequency?: string;
  currency?: "USD" | "EUR";
  dashboardView?: "cards" | "circular";
  hasCompletedOnboarding?: boolean;
  profileImage?: string | null;
  email?: string;
  phone?: string;
  country?: string;
  birthday?: string;
  showHealthMetrics?: boolean;
  sleepGoal?: number;
  // Related data arrays
  emergencyContacts?: EmergencyContact[];
  relapses?: Relapse[];
  moodEntries?: MoodEntry[];
  documents?: Document[];
  mediaFiles?: MediaFile[];
  customExercises?: MindfulnessExercise[];
  favoriteExercises?: string[];
  completedExercises?: CompletedExercise[];
  notificationSettings?: NotificationSettings;
  privacySettings?: PrivacySettings;
  healthMetrics?: HealthMetric[];
  healthGoals?: HealthGoal[];
}

export interface NotificationSettings {
  dailyReminders: boolean;
  milestoneAlerts: boolean;
  emergencyAlerts: boolean;
  journalReminders: boolean;
  mindfulnessReminders: boolean;
  weeklyReports: boolean;
  communityMessages: boolean;
}

export interface PrivacySettings {
  dataCollection: boolean;
  anonymousAnalytics: boolean;
  crashReporting: boolean;
  locationTracking: boolean;
  biometricAuth: boolean;
  appLock: boolean;
}

export interface EmergencyContact {
  id: string;
  name: string;
  phone: string;
  email?: string;
  relationship: string;
  category?: ContactCategory;
  date: string;
  notes?: string;
}

export interface Relapse {
  id: string;
  date: string;
  triggers: string[];
  notes: string;
}

export interface MoodEntry {
  id: string;
  date: string;
  mood: 1 | 2 | 3 | 4 | 5;
  cravingIntensity: 1 | 2 | 3 | 4 | 5;
  notes: string;
} 