export interface Document {
  id: string;
  title: string;
  content: string;
  date: string;
  category: string;
  fileUri?: string;
  fileName?: string;
  fileType?: string;
  fileSize?: number;
}

export interface MediaFile {
  id: string;
  title: string;
  description: string;
  date: string;
  type: "image" | "audio" | "video" | "document";
  uri: string;
  fileName: string;
  fileType: string;
  fileSize?: number;
  width?: number;
  height?: number;
  duration?: number;
  thumbnailUri?: string;
  category?: string;
}

export interface Track {
  id: string;
  title: string;
  artist?: string;
  album?: string;
  uri: string;
  duration?: number;
  imageUri?: string;
} 