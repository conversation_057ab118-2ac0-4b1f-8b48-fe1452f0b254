import { LucideIcon } from "lucide-react-native";

export interface HealthMetric {
  id: string;
  name: string;
  value: number | string;
  unit?: string;
  date: string;
  icon?: LucideIcon;
  metric: string;
  notes?: string;
}

export interface HealthGoal {
  id: string;
  label: string;
  labelNL: string;
  icon: LucideIcon;
  unit: string;
  unitNL: string;
  target: number | null;
  enabled: boolean;
  currentValue?: number;
  history?: { date: string; value: number }[];
} 