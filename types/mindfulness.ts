export interface MindfulnessExercise {
  id: string;
  title: string;
  description: string;
  type:
    | "breathing"
    | "meditation"
    | "grounding"
    | "bodyscan"
    | "visualization"
    | "gratitude"
    | "muscle-relaxation"
    | "custom";
  steps?: string[];
  duration?: number; // in minutes
  customInstructions?: string;
  isCustom: boolean;
  createdAt: string;
}

export interface CompletedExercise {
  id: string;
  exerciseId: string; // ID of the exercise that was completed
  date: string;
  duration: number; // in seconds
  notes?: string;
} 