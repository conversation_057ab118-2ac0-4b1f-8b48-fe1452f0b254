import { StyleSheet } from 'react-native';

/**
 * Global Modal Styles
 * Reusable modal components and layouts for consistent UI across the app
 */
export const modalStyles = StyleSheet.create({


  // Modal Container & Overlay
  closeButton: {
    padding: 4,
  },
  modalBody: {
    maxHeight: 500,
    padding: 20,
  },
  modalBodyLarge: {
    maxHeight: 600,
    padding: 20,
  },
  modalBodyScrollable: {
    flex: 1,
    padding: 20,
  },
  modalContent: {
    borderRadius: 24,
    elevation: 8,
    maxHeight: '85%',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    width: '100%',
  },
  modalContentLarge: {
    borderRadius: 24,
    elevation: 8,
    maxHeight: '90%',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    width: '100%',
  },
  modalFooter: {
    borderTopWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  modalFooterSingle: {
    borderTopWidth: 1,
    padding: 20,
  },
  modalHeader: {
    alignItems: 'center',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
  },
  modalOverlay: {
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
  },

  // Modal Sections
  section: {
    marginBottom: 32,
  },
  sectionDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 20,
  },
  sectionLast: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },

  // Summary Section
  summarySection: {
    borderRadius: 16,
    borderWidth: 1,
    marginTop: 16,
    padding: 16,
  },
  summaryText: {
    fontSize: 14,
    lineHeight: 20,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 8,
  },


});

/**
 * Modal Button Styles
 * Consistent button styling for modal actions
 */
export const modalButtonStyles = StyleSheet.create({


  // Icon Buttons
  iconButton: {
    alignItems: 'center',
    borderRadius: 12,
    height: 48,
    justifyContent: 'center',
    width: 48,
  },

  // Primary Buttons
  primaryButton: {
    alignItems: 'center',
    borderRadius: 12,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    marginLeft: 8,
    padding: 16,
  },
  primaryButtonSingle: {
    alignItems: 'center',
    borderRadius: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 16,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },

  // Secondary Buttons
  secondaryButton: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flex: 1,
    justifyContent: 'center',
    marginRight: 8,
    padding: 16,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },

  // Selected State
  selectedText: {
    color: '#fff',
  },


});

/**
 * Modal Animation Styles
 * Consistent animations for modal transitions
 */
export const modalAnimationStyles = {
  slideUp: 'slide' as const,
  fade: 'fade' as const,
  none: 'none' as const,
};

/**
 * Modal Size Variants
 * Different modal sizes for various use cases
 */
export const modalSizes = {
  small: { maxHeight: '60%' },
  medium: { maxHeight: '75%' },
  large: { maxHeight: '85%' },
  fullScreen: { maxHeight: '95%' },
}; 