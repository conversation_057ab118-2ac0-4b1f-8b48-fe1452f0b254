import { StyleSheet } from 'react-native';

/**
 * Global Form Styles
 * Reusable form components and input styling for consistent UI across the app
 */
export const formStyles = StyleSheet.create({


  // Specialized Inputs
  amountInput: {
    borderRadius: 12,
    borderWidth: 1,
    flex: 1,
    fontSize: 16,
    minHeight: 48,
    padding: 16,
  },
  amountInputContainer: {
    alignItems: 'center',
    flexDirection: 'row',
  },

  // Icon Containers
  currencyIconContainer: {
    alignItems: 'center',
    borderRadius: 10,
    height: 36,
    justifyContent: 'center',
    marginRight: 10,
    width: 36,
  },

  // Error States
  errorText: {
    fontSize: 14,
    marginTop: 4,
  },

  // Form Containers
  fieldContainer: {
    marginBottom: 20,
  },
  fieldContainerLast: {
    marginBottom: 0,
  },

  // Labels
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  formContainer: {
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  iconContainer: {
    alignItems: 'center',
    borderRadius: 12,
    height: 40,
    justifyContent: 'center',
    marginRight: 16,
    width: 40,
  },
  inputError: {
    borderWidth: 2,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  quantityInput: {
    borderRadius: 12,
    borderWidth: 1,
    fontSize: 16,
    minHeight: 48,
    padding: 16,
  },

  // Text Inputs
  textInput: {
    borderRadius: 12,
    borderWidth: 1,
    fontSize: 16,
    minHeight: 48,
    padding: 16,
  },
  textInputLarge: {
    borderRadius: 12,
    borderWidth: 1,
    fontSize: 16,
    minHeight: 120,
    padding: 16,
    textAlignVertical: 'top',
  },
  textInputSmall: {
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 14,
    minHeight: 40,
    padding: 12,
  },


});

/**
 * Selection Grid Styles
 * Reusable grid components for buttons, options, and selections
 */
export const selectionStyles = StyleSheet.create({


  // Currency Selection
  currencyButton: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    minHeight: 48,
    padding: 12,
  },
  currencyButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  currencyGrid: {
    flexDirection: 'row',
    gap: 12,
  },

  // Option Selection
  optionButton: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    justifyContent: 'center',
    minHeight: 48,
    padding: 16,
  },
  optionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  optionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },

  // Selection States
  selectedButton: {
    borderWidth: 2,
  },
  selectedText: {
    color: '#fff',
  },

  // Toggle Buttons
  toggleButton: {
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 12,
  },
  toggleButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },

  // Unit Selection
  unitButton: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flex: 1,
    justifyContent: 'center',
    minHeight: 48,
    padding: 12,
  },
  unitButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  unitGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },


});

/**
 * Card Styles
 * Reusable card components for forms and content
 */
export const cardStyles = StyleSheet.create({


  card: {
    borderRadius: 16,
    borderWidth: 1,
    elevation: 4,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },

  // Card Content
  cardContent: {
    flex: 1,
  },
  cardDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  cardHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  cardLarge: {
    borderRadius: 20,
    borderWidth: 1,
    elevation: 6,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  cardSmall: {
    borderRadius: 12,
    borderWidth: 1,
    elevation: 2,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
  },


});

/**
 * Layout Utilities
 * Common layout patterns and spacing
 */
export const layoutStyles = StyleSheet.create({


  // Spacing
  marginBottom16: {
    marginBottom: 16,
  },
  marginBottom20: {
    marginBottom: 20,
  },
  marginBottom24: {
    marginBottom: 24,
  },
  marginBottom32: {
    marginBottom: 32,
  },

  // Padding
  padding16: {
    padding: 16,
  },
  padding20: {
    padding: 20,
  },
  paddingHorizontal16: {
    paddingHorizontal: 16,
  },
  paddingHorizontal20: {
    paddingHorizontal: 20,
  },

  // Flex Layouts
  row: {
    flexDirection: 'row',
  },
  rowCenter: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  rowSpaceBetween: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },


}); 