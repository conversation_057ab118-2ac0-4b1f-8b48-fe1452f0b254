/**
 * Global Style System
 * Centralized styling for consistent UI across the SobrixHealth app
 */

// Modal Styles
export {
  modalStyles,
  modalButtonStyles,
  modalAnimationStyles,
  modalSizes,
} from './modal';

// Form Styles
export {
  formStyles,
  selectionStyles,
  cardStyles,
  layoutStyles,
} from './forms';

// Re-export commonly used style combinations
export const globalStyles = {
  // Common modal configurations
  modal: {
    standard: 'modalContent',
    large: 'modalContentLarge',
    overlay: 'modalOverlay',
  },
  
  // Common form field patterns
  field: {
    container: 'fieldContainer',
    label: 'fieldLabel',
    input: 'textInput',
    error: 'errorText',
  },
  
  // Common button patterns
  button: {
    primary: 'primaryButton',
    secondary: 'secondaryButton',
    selection: 'unitButton',
    currency: 'currencyButton',
  },
  
  // Common layout patterns
  layout: {
    row: 'row',
    rowCenter: 'rowCenter',
    spaceBetween: 'rowSpaceBetween',
    section: 'section',
  },
} as const;

/**
 * Style Utilities
 * Helper functions for dynamic styling
 */
export const styleUtils = {
  /**
   * Get selection button style based on selected state
   */
  getSelectionStyle: (isSelected: boolean, baseStyle: object, selectedStyle: object) => [
    baseStyle,
    isSelected && selectedStyle,
  ],
  
  /**
   * Get text style based on selected state
   */
  getSelectionTextStyle: (isSelected: boolean, baseStyle: object, selectedTextStyle: object) => [
    baseStyle,
    isSelected && selectedTextStyle,
  ],
  
  /**
   * Combine multiple style objects
   */
  combineStyles: (...styles: (object | false | null | undefined)[]) => styles.filter(Boolean),
};

/**
 * Common Color Patterns
 * Reusable color combinations for consistent theming
 */
export const colorPatterns = {
  selection: {
    selected: {
      background: 'primary',
      text: '#fff',
      border: 'primary',
    },
    unselected: {
      background: 'background',
      text: 'text',
      border: 'border',
    },
  },
  
  input: {
    normal: {
      background: 'background',
      text: 'text',
      border: 'border',
      placeholder: 'muted',
    },
    error: {
      background: 'background',
      text: 'text',
      border: 'error',
      placeholder: 'muted',
    },
  },
  
  modal: {
    overlay: 'rgba(0,0,0,0.5)',
    background: 'card',
    border: 'border',
  },
} as const; 