{"expo": {"name": "SobrixHealth", "slug": "sobrixhealth", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.sobrixhealth.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "app.rork.sobrixhealth"}, "web": {"favicon": "./assets/images/favicon.png", "bundler": "metro", "output": "static", "name": "SobrixHealth - Recovery Companion", "description": "A comprehensive addiction recovery platform", "themeColor": "#4F46E5", "backgroundColor": "#ffffff"}, "plugins": [["expo-router", {"origin": "https://rork.app/"}], "expo-sqlite", "expo-audio", "expo-video"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": "https://rork.app/"}, "eas": {"projectId": "7b113f00-3708-45d8-b011-299748b5674d"}}, "owner": "sti<PERSON><PERSON>", "runtimeVersion": "1.0.0", "updates": {"url": "https://u.expo.dev/7b113f00-3708-45d8-b011-299748b5674d"}}}