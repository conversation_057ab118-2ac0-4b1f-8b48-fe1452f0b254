# SobrixHealth - Addiction Recovery Companion

SobrixHealth is a comprehensive mobile application designed to support individuals on their journey to recovery from addiction. Built with modern React Native technologies, the app provides tools for tracking sobriety, managing cravings, accessing emergency resources, and practicing mindfulness techniques.

<div align="center">
  <img src="./assets/images/icon.png" alt="SobrixHealth Logo" width="120" height="120">
</div>

## 📱 App Screenshots

<div align="center">
  <table>
    <tr>
      <td align="center">
        <img src="./screenshots/dashboard.png" alt="Dashboard" width="250">
        <br><b>Recovery Dashboard</b>
      </td>
      <td align="center">
        <img src="./screenshots/progress.png" alt="Progress Tracking" width="250">
        <br><b>Progress Tracking</b>
      </td>
      <td align="center">
        <img src="./screenshots/mindfulness.png" alt="Mindfulness" width="250">
        <br><b>Mindfulness Exercises</b>
      </td>
    </tr>
    <tr>
      <td align="center">
        <img src="./screenshots/health.png" alt="Health Metrics" width="250">
        <br><b>Health Metrics</b>
      </td>
      <td align="center">
        <img src="./screenshots/contacts.png" alt="Emergency Contacts" width="250">
        <br><b>Emergency Contacts</b>
      </td>
      <td align="center">
        <img src="./screenshots/settings.png" alt="Settings" width="250">
        <br><b>Settings & Profile</b>
      </td>
    </tr>
  </table>
</div>

## ✨ Key Features

### 🏆 Personalized Recovery Dashboard

- **Sobriety Tracker**: Track days, weeks, months, and years of sobriety with beautiful visualizations
- **Financial Savings Calculator**: See exactly how much money you've saved by avoiding substance use
- **Health Metrics**: Monitor improvements in sleep quality, energy levels, stress, and overall wellbeing
- **Achievement System**: Celebrate recovery milestones with meaningful rewards and recognition

### 📊 Comprehensive Progress Monitoring

- **Mood Tracking**: Log daily mood states and craving intensity with intuitive charts
- **Relapse Prevention**: Record triggers and circumstances to identify patterns and prevent future relapses
- **Milestone Tracking**: Visualize upcoming goals and celebrate achieved recovery milestones
- **Usage Statistics**: Analyze historical substance usage patterns and associated costs
- **Health Progress**: Track physical and mental health improvements over time

### 🧘 Mindfulness & Coping Tools

- **Breathing Exercises**: Guided breathing techniques with visual cues for stress reduction
- **Meditation Sessions**: Curated mindfulness practices specifically designed for recovery support
- **Grounding Techniques**: Immediate tools to manage cravings and anxiety in real-time
- **Body Scan Exercises**: Progressive muscle relaxation for physical and mental wellness
- **Visualization Practices**: Guided imagery sessions to strengthen recovery motivation

### 👥 Contact Management & Emergency Support

- **Emergency Contacts**: One-tap access to your personal support network
- **Contact Organization**: Manage your recovery support team with detailed contact information
- **Quick Communication**: Instant calling and messaging capabilities
- **Support Network**: Build and maintain connections with sponsors, therapists, and support groups

### ⚙️ Personalization & Settings

- **Profile Management**: Customize your personal information and recovery details
- **Notification Settings**: Configure reminders and alerts for your recovery routine
- **Privacy Controls**: Manage data sharing and privacy preferences
- **App Customization**: Personalize the app experience to fit your needs

## 🚀 Modern Technology Stack

- **Framework**: React Native 0.79.2 with React 19.0.0
- **Navigation**: Expo Router v5.0.7 for file-based routing
- **Styling**: NativeWind v4.1.23 (Tailwind CSS for React Native)
- **State Management**: Zustand v5.0.4 with AsyncStorage for persistent data
- **Development**: Expo SDK 53.0.9 with development client
- **Language**: TypeScript 5.8.3 with strict type checking
- **Package Manager**: Yarn (enforced via custom script)
- **Code Quality**: ESLint 9.27.0 with React Native specific rules
- **Icons**: Lucide React Native v0.475.0 for modern iconography
- **Charts**: React Native Chart Kit v6.12.0 for data visualization

## 📦 Installation & Setup

### Prerequisites

- **Node.js**: v18 or higher (enforced in package.json)
- **Yarn**: v1.22.0 or higher (required, npm is not supported)
- **Expo CLI**: Latest version
- **Development Environment**: 
  - iOS: Xcode and iOS Simulator
  - Android: Android Studio and Android Emulator

### Quick Start

1. **Clone the repository:**

```bash
git clone https://github.com/Stijnus/sobrixhealth.git
cd sobrixhealth
```

2. **Install dependencies:**

```bash
yarn install
```

3. **Start the development server:**

```bash
yarn start
```

4. **Run on your preferred platform:**

```bash
# For iOS
yarn ios

# For Android
yarn android

# For web development
yarn start-web

# For web with debugging
yarn start-web-dev
```

## 🏗️ Project Architecture

### File Structure

```
sobrixhealth/
├── app/                          # Expo Router file-based routing
│   ├── (tabs)/                  # Tab navigation group
│   │   ├── dashboard/           # Dashboard tab with nested routes
│   │   ├── progress.tsx         # Progress tracking tab
│   │   ├── mindfulness.tsx      # Mindfulness exercises tab
│   │   ├── contacts.tsx         # Emergency contacts tab
│   │   ├── settings.tsx         # Settings and profile tab
│   │   └── _layout.tsx          # Tab layout configuration
│   ├── onboarding.tsx           # First-time user setup
│   ├── _layout.tsx              # Root layout
│   └── +not-found.tsx           # 404 error page
├── components/                   # Feature-organized components
│   ├── dashboard/               # Dashboard-specific components
│   ├── health/                  # Health tracking components
│   ├── mindfulness/             # Mindfulness exercise components
│   ├── progress/                # Progress visualization components
│   ├── settings/                # Settings and configuration components
│   ├── contacts/                # Contact management components
│   ├── onboarding/              # Onboarding flow components
│   └── shared/                  # Reusable components and utilities
├── style/                       # Global styling system
│   ├── index.ts                 # Main style exports
│   ├── modal.ts                 # Modal-specific styles
│   ├── forms.ts                 # Form and input styles
│   └── icons/                   # Icon assets
├── store/                       # Zustand state management
├── types/                       # TypeScript type definitions
├── hooks/                       # Custom React hooks
├── utils/                       # Utility functions
├── constants/                   # App constants and configuration
└── context/                     # React context providers
```

### Configuration Files

- **[tailwind.config.js](tailwind.config.js)**: NativeWind v4 configuration
- **[postcss.config.js](postcss.config.js)**: PostCSS processing for Tailwind
- **[eslint.config.js](eslint.config.js)**: Comprehensive ESLint rules
- **[tsconfig.json](tsconfig.json)**: TypeScript configuration
- **[babel.config.cjs](babel.config.cjs)**: Babel transformation settings
- **[app.json](app.json)**: Expo project configuration
- **[eas.json](eas.json)**: Expo Application Services configuration

## 🎨 Styling System

### NativeWind Integration

The app uses NativeWind v4, bringing Tailwind CSS to React Native:

- **Global Styles**: Defined in `global.css` with Tailwind directives
- **Component Styles**: Organized in the `style/` directory for reusability
- **Type Safety**: Full TypeScript support for style definitions
- **Performance**: Optimized for React Native with minimal runtime overhead

### Style Organization

- **Global System**: Centralized styling in `style/index.ts`
- **Modal Styles**: Consistent modal patterns in `style/modal.ts`
- **Form Styles**: Reusable form components in `style/forms.ts`
- **No Inline Styles**: ESLint rules enforce StyleSheet usage
- **Alphabetical Ordering**: Style properties must be alphabetically ordered

## 📱 Getting Started

### First-Time Onboarding

When you first open SobrixHealth, you'll be guided through a personalized setup:

1. **Welcome Screen**: Introduction to the app's purpose and features
2. **Personal Information**: Enter your name and basic recovery details
3. **Addiction Configuration**: Set up your specific recovery parameters
4. **Dashboard Setup**: Configure your preferred tracking metrics
5. **Notification Preferences**: Set up reminders and alerts

### Daily Usage Workflow

- **Dashboard Check**: Review your progress and current metrics
- **Progress Logging**: Update your daily recovery data
- **Mindfulness Practice**: Access guided exercises when needed
- **Contact Support**: Quick access to your support network
- **Settings Management**: Adjust preferences and privacy settings

## 🚀 Expo Deployment & Updates

### Expo Project Information

The SobrixHealth app is hosted on Expo and can be accessed at:
**[https://expo.dev/accounts/stijnus/projects/sobrixhealth](https://expo.dev/accounts/stijnus/projects/sobrixhealth)**

- **Project ID**: `7b113f00-3708-45d8-b011-299748b5674d`
- **Owner**: `stijnus`
- **Slug**: `sobrixhealth`

### EAS (Expo Application Services) Setup

The project is configured with EAS for building and deploying:

#### Build Profiles

```bash
# Development build (with development client)
eas build --profile development

# Preview build (internal distribution)
eas build --profile preview

# Production build (app store ready)
eas build --profile production
```

#### Update Deployment

```bash
# Install EAS CLI globally
npm install -g @expo/eas-cli

# Login to your Expo account
eas login

# Publish an update to the development channel
eas update --branch development

# Publish an update to the production channel
eas update --branch production

# Publish with a specific message
eas update --branch production --message "Fix critical bug in dashboard"
```

#### Build Commands

```bash
# Build for both platforms (production)
eas build --platform all --profile production

# Build for iOS only
eas build --platform ios --profile production

# Build for Android only
eas build --platform android --profile production

# Build development version for testing
eas build --platform all --profile development
```

#### Submit to App Stores

```bash
# Submit iOS build to App Store
eas submit --platform ios

# Submit Android build to Google Play
eas submit --platform android

# Submit both platforms
eas submit --platform all
```

### Development Workflow

1. **Local Development**: Use `yarn start` for local development
2. **Testing Builds**: Create development builds with `eas build --profile development`
3. **Preview Releases**: Use preview profile for internal testing
4. **Production Updates**: Deploy OTA updates with `eas update`
5. **Store Releases**: Submit production builds to app stores

### Configuration Files

- **[eas.json](eas.json)**: EAS build and submit configuration
- **[app.json](app.json)**: Expo project configuration with EAS project ID
- **Build Profiles**: Development, preview, and production configurations
- **Credentials**: Managed remotely for iOS, locally for Android

### Over-the-Air (OTA) Updates

The app supports OTA updates for JavaScript and asset changes:

```bash
# Quick update to production
eas update --auto

# Update with custom branch
eas update --branch feature-branch

# Update with rollback capability
eas update --branch production --message "Version 1.0.1 - Bug fixes"
```

## 🔒 Privacy & Security

SobrixHealth prioritizes your privacy and data security:

- **Local Storage**: All personal data is stored securely on your device
- **No Data Sharing**: Your information is never shared without explicit consent
- **Privacy Controls**: Granular settings to control what data is tracked
- **Secure Communication**: Encrypted connections for any network requests
- **Data Ownership**: Full control over your data with export capabilities

## 🤝 Contributing

We welcome contributions from the community! Here's how you can help:

### Development Setup

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Follow the coding standards (ESLint will help enforce these)
4. Test your changes thoroughly
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to your branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

### Code Quality Standards

- **TypeScript**: Strict typing required, no `any` types allowed
- **ESLint**: All rules must pass, including React Native specific rules
- **Style Guidelines**: Follow the established styling patterns
- **Component Organization**: Use feature-based directory structure
- **Testing**: Ensure new features include appropriate tests

### Areas for Contribution

- 🌐 **Internationalization**: Help us support more languages
- 🎨 **UI/UX**: Improve the user interface and accessibility
- 📊 **Analytics**: Enhance data visualization and insights
- 🧘 **Content**: Add new mindfulness exercises and resources
- 🐛 **Bug Fixes**: Help us identify and fix issues
- 📱 **Platform Features**: Leverage platform-specific capabilities

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Recovery Communities**: Inspiration from recovery communities worldwide
- **Mental Health Professionals**: Guidance from addiction recovery specialists
- **Open Source Contributors**: Amazing developers who make this possible
- **Beta Testers**: Users who provided valuable feedback during development
- **Expo Team**: For providing excellent React Native tooling
- **NativeWind**: For bringing Tailwind CSS to React Native

## 📞 Support & Contact

- **Issues**: Report bugs or request features on [GitHub Issues](https://github.com/Stijnus/sobrixhealth/issues)
- **Discussions**: Join community discussions on [GitHub Discussions](https://github.com/Stijnus/sobrixhealth/discussions)
- **Email**: For sensitive matters, contact <NAME_EMAIL>

---

<div align="center">
  <p><strong>⚠️ Important Disclaimer</strong></p>
  <p><em>SobrixHealth is not a substitute for professional medical advice, diagnosis, or treatment. Always seek the advice of your physician or other qualified health provider with any questions you may have regarding a medical condition or recovery plan.</em></p>
</div>

---

<div align="center">
  <p>Made with ❤️ for the recovery community</p>
  <p>
    <a href="https://github.com/Stijnus/sobrixhealth">⭐ Star us on GitHub</a> •
    <a href="https://github.com/Stijnus/sobrixhealth/issues">🐛 Report Bug</a> •
    <a href="https://github.com/Stijnus/sobrixhealth/discussions">�� Join Discussion</a>
  </p>
</div>
