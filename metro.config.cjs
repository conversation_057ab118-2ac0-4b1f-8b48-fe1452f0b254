// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Ensure path aliases work
config.resolver.extraNodeModules = {
  '@': path.resolve(__dirname),
};

// Add alias for better module resolution
config.resolver.alias = {
  '@': path.resolve(__dirname),
};

// Platform-specific file extensions
config.resolver.platforms = ['web', 'native', 'ios', 'android'];

module.exports = config;