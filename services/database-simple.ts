import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Conditional SQLite import only for native platforms
let SQLite: any = null;

// Use dynamic import to avoid bundling expo-sqlite on web
const loadSQLite = async () => {
  if (Platform.OS !== 'web' && !SQLite) {
    try {
      // Dynamic import to avoid bundling on web
      const module = await import('expo-sqlite');
      SQLite = module;
      console.log('SQLite loaded successfully');
    } catch (error) {
      console.warn('expo-sqlite not available, falling back to AsyncStorage');
    }
  }
};

// Database instance
let db: any = null;

// Initialize database
export const initDatabase = async (): Promise<void> => {
  try {
    if (Platform.OS !== 'web') {
      // Load SQLite dynamically to avoid bundling on web
      await loadSQLite();

      if (SQLite) {
        db = await SQLite.openDatabaseAsync('sobrix_health.db');
        await createTables();
        console.log('SQLite database initialized successfully');
      } else {
        console.log('SQLite not available, using AsyncStorage fallback');
      }
    } else {
      console.log('Using AsyncStorage as database fallback for web platform');
    }
  } catch (error) {
    console.error('Error initializing database:', error);
    console.log('Falling back to AsyncStorage');
    db = null;
  }
};

// Create essential tables (only for SQLite)
const createTables = async (): Promise<void> => {
  if (!db || Platform.OS === 'web') return;

  const tables = [
    // User profile table
    `CREATE TABLE IF NOT EXISTS user_profile (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      data TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    // Health data table for daily metrics
    `CREATE TABLE IF NOT EXISTS health_data (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      date TEXT NOT NULL UNIQUE,
      sleep REAL,
      pills REAL,
      hydration REAL,
      exercise REAL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    // Mood entries table
    `CREATE TABLE IF NOT EXISTS mood_entries (
      id TEXT PRIMARY KEY,
      date TEXT NOT NULL,
      mood INTEGER NOT NULL,
      craving_intensity INTEGER NOT NULL,
      notes TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    // Emergency contacts table
    `CREATE TABLE IF NOT EXISTS emergency_contacts (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      phone TEXT NOT NULL,
      email TEXT,
      relationship TEXT NOT NULL,
      notes TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    // Documents table
    `CREATE TABLE IF NOT EXISTS documents (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      content TEXT,
      category TEXT,
      date TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    // Media files metadata table
    `CREATE TABLE IF NOT EXISTS media_files (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      description TEXT,
      type TEXT NOT NULL,
      category TEXT,
      file_name TEXT,
      file_size INTEGER,
      date TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    // Generic key-value storage tables for Zustand stores
    `CREATE TABLE IF NOT EXISTS zustand_user_storage (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    `CREATE TABLE IF NOT EXISTS zustand_quotes_storage (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    `CREATE TABLE IF NOT EXISTS zustand_library_storage (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    `CREATE TABLE IF NOT EXISTS zustand_profile_storage (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    `CREATE TABLE IF NOT EXISTS zustand_audio_storage (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`
  ];

  for (const tableSQL of tables) {
    await db.execAsync(tableSQL);
  }
};

// Simple database service
export class SimpleDatabaseService {
  private static instance: SimpleDatabaseService;

  static getInstance(): SimpleDatabaseService {
    if (!SimpleDatabaseService.instance) {
      SimpleDatabaseService.instance = new SimpleDatabaseService();
    }
    return SimpleDatabaseService.instance;
  }

  private ensureDatabase(): any {
    if (Platform.OS === 'web') {
      // On web, we don't use a database instance, fallback to AsyncStorage
      return null;
    }
    if (!db) {
      throw new Error('Database not initialized. Call initDatabase() first.');
    }
    return db;
  }

  private async getFromAsyncStorage(key: string): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(key);
    } catch (error) {
      console.error('Error getting from AsyncStorage:', error);
      return null;
    }
  }

  private async setToAsyncStorage(key: string, value: string): Promise<void> {
    try {
      await AsyncStorage.setItem(key, value);
    } catch (error) {
      console.error('Error setting to AsyncStorage:', error);
      throw error;
    }
  }

  // User Profile operations (stored as JSON for simplicity)
  async saveUserProfile(profileData: Record<string, unknown>): Promise<void> {
    if (Platform.OS === 'web') {
      // Use AsyncStorage on web
      await this.setToAsyncStorage('user_profile', JSON.stringify(profileData));
      return;
    }

    const database = this.ensureDatabase();

    try {
      await database.runAsync(`
        INSERT OR REPLACE INTO user_profile (id, data, updated_at)
        VALUES (1, ?, CURRENT_TIMESTAMP)
      `, [JSON.stringify(profileData)]);
    } catch (error) {
      console.error('Error saving user profile:', error);
      throw error;
    }
  }

  async getUserProfile(): Promise<Record<string, unknown> | null> {
    if (Platform.OS === 'web') {
      // Use AsyncStorage on web
      const data = await this.getFromAsyncStorage('user_profile');
      return data ? JSON.parse(data) : null;
    }

    const database = this.ensureDatabase();

    try {
      const result = await database.getFirstAsync<{ data: string }>(
        'SELECT data FROM user_profile WHERE id = 1'
      );

      return result ? JSON.parse(result.data) : null;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  }

  // Health data operations
  async saveHealthData(date: string, data: { sleep?: number; pills?: number; hydration?: number; exercise?: number }): Promise<void> {
    const database = this.ensureDatabase();

    try {
      await database.runAsync(`
        INSERT OR REPLACE INTO health_data (date, sleep, pills, hydration, exercise, updated_at)
        VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `, [
        date,
        data.sleep || null,
        data.pills || null,
        data.hydration || null,
        data.exercise || null
      ]);
    } catch (error) {
      console.error('Error saving health data:', error);
      throw error;
    }
  }

  async getHealthData(date: string): Promise<{ sleep?: number; pills?: number; hydration?: number; exercise?: number } | null> {
    const database = this.ensureDatabase();

    try {
      const result = await database.getFirstAsync<{ sleep?: number; pills?: number; hydration?: number; exercise?: number }>(
        'SELECT * FROM health_data WHERE date = ?',
        [date]
      );

      return result ? {
        sleep: result.sleep,
        pills: result.pills,
        hydration: result.hydration,
        exercise: result.exercise
      } : null;
    } catch (error) {
      console.error('Error getting health data:', error);
      return null;
    }
  }

  async getHealthDataRange(startDate: string, endDate: string): Promise<any[]> {
    const database = this.ensureDatabase();

    try {
      const results = await database.getAllAsync<any>(
        'SELECT * FROM health_data WHERE date BETWEEN ? AND ? ORDER BY date ASC',
        [startDate, endDate]
      );

      return results.map(row => ({
        date: row.date,
        sleep: row.sleep,
        pills: row.pills,
        hydration: row.hydration,
        exercise: row.exercise
      }));
    } catch (error) {
      console.error('Error getting health data range:', error);
      return [];
    }
  }

  // Mood entries operations
  async addMoodEntry(entry: { id: string; date: string; mood: number; cravingIntensity: number; notes?: string }): Promise<void> {
    const database = this.ensureDatabase();

    try {
      await database.runAsync(`
        INSERT OR REPLACE INTO mood_entries (id, date, mood, craving_intensity, notes)
        VALUES (?, ?, ?, ?, ?)
      `, [
        entry.id,
        entry.date,
        entry.mood,
        entry.cravingIntensity,
        entry.notes || null
      ]);
    } catch (error) {
      console.error('Error adding mood entry:', error);
      throw error;
    }
  }

  async getMoodEntries(limit: number = 50): Promise<any[]> {
    const database = this.ensureDatabase();

    try {
      const results = await database.getAllAsync<any>(
        'SELECT * FROM mood_entries ORDER BY date DESC LIMIT ?',
        [limit]
      );

      return results.map(row => ({
        id: row.id,
        date: row.date,
        mood: row.mood,
        cravingIntensity: row.craving_intensity,
        notes: row.notes
      }));
    } catch (error) {
      console.error('Error getting mood entries:', error);
      return [];
    }
  }

  // Emergency contacts operations
  async addEmergencyContact(contact: { id: string; name: string; phone: string; email?: string; relationship: string; notes?: string }): Promise<void> {
    const database = this.ensureDatabase();

    try {
      await database.runAsync(`
        INSERT OR REPLACE INTO emergency_contacts (id, name, phone, email, relationship, notes)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        contact.id,
        contact.name,
        contact.phone,
        contact.email || null,
        contact.relationship,
        contact.notes || null
      ]);
    } catch (error) {
      console.error('Error adding emergency contact:', error);
      throw error;
    }
  }

  async getEmergencyContacts(): Promise<any[]> {
    const database = this.ensureDatabase();

    try {
      const results = await database.getAllAsync<any>(
        'SELECT * FROM emergency_contacts ORDER BY created_at DESC'
      );

      return results.map(row => ({
        id: row.id,
        name: row.name,
        phone: row.phone,
        email: row.email,
        relationship: row.relationship,
        notes: row.notes
      }));
    } catch (error) {
      console.error('Error getting emergency contacts:', error);
      return [];
    }
  }

  async deleteEmergencyContact(id: string): Promise<void> {
    const database = this.ensureDatabase();

    try {
      await database.runAsync('DELETE FROM emergency_contacts WHERE id = ?', [id]);
    } catch (error) {
      console.error('Error deleting emergency contact:', error);
      throw error;
    }
  }

  // Documents operations
  async addDocument(doc: { id: string; title: string; content?: string; category?: string; date: string }): Promise<void> {
    const database = this.ensureDatabase();

    try {
      await database.runAsync(`
        INSERT OR REPLACE INTO documents (id, title, content, category, date)
        VALUES (?, ?, ?, ?, ?)
      `, [
        doc.id,
        doc.title,
        doc.content || null,
        doc.category || null,
        doc.date
      ]);
    } catch (error) {
      console.error('Error adding document:', error);
      throw error;
    }
  }

  async getDocuments(): Promise<any[]> {
    const database = this.ensureDatabase();

    try {
      const results = await database.getAllAsync<any>(
        'SELECT * FROM documents ORDER BY created_at DESC'
      );

      return results.map(row => ({
        id: row.id,
        title: row.title,
        content: row.content,
        category: row.category,
        date: row.date
      }));
    } catch (error) {
      console.error('Error getting documents:', error);
      return [];
    }
  }

  // Media files operations
  async addMediaFile(file: { id: string; title: string; description?: string; type: string; category?: string; fileName?: string; fileSize?: number; date: string }): Promise<void> {
    const database = this.ensureDatabase();

    try {
      await database.runAsync(`
        INSERT OR REPLACE INTO media_files (id, title, description, type, category, file_name, file_size, date)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        file.id,
        file.title,
        file.description || null,
        file.type,
        file.category || null,
        file.fileName || null,
        file.fileSize || null,
        file.date
      ]);
    } catch (error) {
      console.error('Error adding media file:', error);
      throw error;
    }
  }

  async getMediaFiles(): Promise<any[]> {
    const database = this.ensureDatabase();

    try {
      const results = await database.getAllAsync<any>(
        'SELECT * FROM media_files ORDER BY created_at DESC'
      );

      return results.map(row => ({
        id: row.id,
        title: row.title,
        description: row.description,
        type: row.type,
        category: row.category,
        fileName: row.file_name,
        fileSize: row.file_size,
        date: row.date
      }));
    } catch (error) {
      console.error('Error getting media files:', error);
      return [];
    }
  }

  // Generic data operations for Zustand storage
  async getGenericData(tableName: string, keyColumn: string, key: string): Promise<Record<string, unknown> | null> {
    const database = this.ensureDatabase();

    try {
      const result = await database.getFirstAsync<Record<string, unknown>>(
        `SELECT * FROM ${tableName} WHERE ${keyColumn} = ?`,
        [key]
      );

      return result || null;
    } catch (error) {
      console.error(`Error getting generic data from ${tableName}:`, error);
      return null;
    }
  }

  async setGenericData(tableName: string, data: Record<string, unknown>): Promise<void> {
    const database = this.ensureDatabase();

    try {
      const columns = Object.keys(data);
      const values = Object.values(data).map(value => 
        value === null || value === undefined ? null : String(value)
      );
      const placeholders = columns.map(() => '?').join(', ');
      
      await database.runAsync(`
        INSERT OR REPLACE INTO ${tableName} (${columns.join(', ')})
        VALUES (${placeholders})
      `, values);
    } catch (error) {
      console.error(`Error setting generic data in ${tableName}:`, error);
      throw error;
    }
  }

  async deleteGenericData(tableName: string, keyColumn: string, key: string): Promise<void> {
    const database = this.ensureDatabase();

    try {
      await database.runAsync(`DELETE FROM ${tableName} WHERE ${keyColumn} = ?`, [key]);
    } catch (error) {
      console.error(`Error deleting generic data from ${tableName}:`, error);
      throw error;
    }
  }

  // Migration helper
  async migrateFromAsyncStorage(): Promise<void> {
    try {
      console.log('Starting migration from AsyncStorage to SQLite...');

      // Get all AsyncStorage keys
      const keys = await AsyncStorage.getAllKeys();
      
      // Find user profile data
      const userProfileKey = keys.find(key => key.includes('user-profile-storage'));
      if (userProfileKey) {
        const userProfileData = await AsyncStorage.getItem(userProfileKey);
        if (userProfileData) {
          const parsedData = JSON.parse(userProfileData);
          if (parsedData.state && parsedData.state.profile) {
            await this.saveUserProfile(parsedData.state.profile);
            console.log('Migrated user profile data');
          }
        }
      }

      // Find health data
      const healthDataKeys = keys.filter(key => key.startsWith('healthData_'));
      for (const key of healthDataKeys) {
        const healthData = await AsyncStorage.getItem(key);
        if (healthData) {
          const date = key.replace('healthData_', '');
          const parsedHealthData = JSON.parse(healthData);
          await this.saveHealthData(date, parsedHealthData);
        }
      }

      console.log('Migration completed successfully');
    } catch (error) {
      console.error('Error during migration:', error);
      throw error;
    }
  }

  // Utility methods
  async getDatabaseStats(): Promise<{ tables: number; totalRecords: number }> {
    const database = this.ensureDatabase();

    try {
      const tables = await database.getAllAsync<any>(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      );

      let totalRecords = 0;
      for (const table of tables) {
        const result = await database.getFirstAsync<any>(
          `SELECT COUNT(*) as count FROM ${table.name}`
        );
        totalRecords += result?.count || 0;
      }

      return {
        tables: tables.length,
        totalRecords
      };
    } catch (error) {
      console.error('Error getting database stats:', error);
      return { tables: 0, totalRecords: 0 };
    }
  }

  async clearAllData(): Promise<void> {
    const database = this.ensureDatabase();

    try {
      const tables = ['user_profile', 'health_data', 'mood_entries', 'emergency_contacts', 'documents', 'media_files'];
      
      for (const table of tables) {
        await database.runAsync(`DELETE FROM ${table}`);
      }

      console.log('All data cleared from database');
    } catch (error) {
      console.error('Error clearing database:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const simpleDatabaseService = SimpleDatabaseService.getInstance(); 