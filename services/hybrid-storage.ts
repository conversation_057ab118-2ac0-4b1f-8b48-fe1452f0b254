import { StateStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createSQLiteStorage, SQLiteStorageOptions } from './sqlite-storage';

/**
 * Hybrid storage that tries SQLite first, falls back to AsyncStorage
 * This ensures the app continues to work even if SQLite initialization fails
 */
export const createHybridStorage = (options: SQLiteStorageOptions): StateStorage => {
  const sqliteStorage = createSQLiteStorage(options);
  const fallbackKey = `fallback_${options.tableName}`;

  return {
    getItem: async (name: string): Promise<string | null> => {
      try {
        // Try SQLite first
        const result = await sqliteStorage.getItem(name);
        if (result !== null) {
          return result;
        }

        // If SQLite returns null, check AsyncStorage as fallback
        const fallbackResult = await AsyncStorage.getItem(`${fallbackKey}_${name}`);
        return fallbackResult;
      } catch (error) {
        console.warn(`Error in hybrid storage getItem for ${options.tableName}:`, error);
        // Final fallback to AsyncStorage
        try {
          return await AsyncStorage.getItem(`${fallbackKey}_${name}`);
        } catch (fallbackError) {
          console.error(`Fallback storage also failed:`, fallbackError);
          return null;
        }
      }
    },

    setItem: async (name: string, value: string): Promise<void> => {
      try {
        // Try SQLite first
        await sqliteStorage.setItem(name, value);
        
        // If successful, also store in AsyncStorage as backup
        try {
          await AsyncStorage.setItem(`${fallbackKey}_${name}`, value);
        } catch (backupError) {
          console.warn(`Failed to backup to AsyncStorage:`, backupError);
          // Don't throw, SQLite succeeded
        }
      } catch (error) {
        console.warn(`SQLite setItem failed for ${options.tableName}, using AsyncStorage:`, error);
        // Fallback to AsyncStorage
        try {
          await AsyncStorage.setItem(`${fallbackKey}_${name}`, value);
        } catch (fallbackError) {
          console.error(`Fallback storage setItem also failed:`, fallbackError);
          // Don't throw to prevent app crashes
        }
      }
    },

    removeItem: async (name: string): Promise<void> => {
      try {
        // Try SQLite first
        await sqliteStorage.removeItem(name);
        
        // Also remove from AsyncStorage backup
        try {
          await AsyncStorage.removeItem(`${fallbackKey}_${name}`);
        } catch (backupError) {
          console.warn(`Failed to remove from AsyncStorage backup:`, backupError);
          // Don't throw, SQLite succeeded
        }
      } catch (error) {
        console.warn(`SQLite removeItem failed for ${options.tableName}, using AsyncStorage:`, error);
        // Fallback to AsyncStorage
        try {
          await AsyncStorage.removeItem(`${fallbackKey}_${name}`);
        } catch (fallbackError) {
          console.error(`Fallback storage removeItem also failed:`, fallbackError);
          // Don't throw to prevent app crashes
        }
      }
    }
  };
};

/**
 * Pre-configured hybrid storage adapters for common store types
 */
export const hybridStorageAdapters = {
  userProfile: createHybridStorage({ tableName: 'zustand_user_storage' }),
  quotes: createHybridStorage({ tableName: 'zustand_quotes_storage' }),
  library: createHybridStorage({ tableName: 'zustand_library_storage' }),
  profile: createHybridStorage({ tableName: 'zustand_profile_storage' }),
  audioPlayer: createHybridStorage({ tableName: 'zustand_audio_storage' })
}; 