import enTranslations from '../locales/en.json';
import nlTranslations from '../locales/nl.json';
// Force TypeScript to re-evaluate types

export type SupportedLanguage = 'en' | 'nl';
export type TranslationKey = keyof typeof enTranslations;

// Type for nested translation keys (e.g., 'common.save', 'onboarding.welcome.title')
export type NestedTranslationKey = 
  | `common.${keyof typeof enTranslations.common}`
  | `navigation.${keyof typeof enTranslations.navigation}`
  | `dashboard.${keyof typeof enTranslations.dashboard}`
  | `dashboard.greetings.${keyof typeof enTranslations.dashboard.greetings}`
  | `dashboard.hero.${keyof typeof enTranslations.dashboard.hero}`
  | `dashboard.stats.${keyof typeof enTranslations.dashboard.stats}`
  | `dashboard.weeklyProgress.${keyof typeof enTranslations.dashboard.weeklyProgress}`
  | `health.modal.${keyof typeof enTranslations.health.modal}`
  | `onboarding.welcome.${keyof typeof enTranslations.onboarding.welcome}`
  | `onboarding.language.${keyof typeof enTranslations.onboarding.language}`
  | `onboarding.name.${keyof typeof enTranslations.onboarding.name}`
  | `onboarding.addiction.${keyof typeof enTranslations.onboarding.addiction}`
  | `onboarding.sobriety.${keyof typeof enTranslations.onboarding.sobriety}`
  | `onboarding.healthGoals.${keyof typeof enTranslations.onboarding.healthGoals}`
  | `onboarding.recoveryGoal.${keyof typeof enTranslations.onboarding.recoveryGoal}`
  | `onboarding.customize.${keyof typeof enTranslations.onboarding.customize}`
  | `onboarding.customize.frequency.${keyof typeof enTranslations.onboarding.customize.frequency}`
  | `onboarding.customize.features.${keyof typeof enTranslations.onboarding.customize.features}`
  | `settings.${keyof typeof enTranslations.settings}`
  | `settings.aboutModal.${keyof typeof enTranslations.settings.aboutModal}`
  | `settings.helpModal.${keyof typeof enTranslations.settings.helpModal}`
  | `settings.privacyModal.${keyof typeof enTranslations.settings.privacyModal}`
  | `settings.notificationsModal.${keyof typeof enTranslations.settings.notificationsModal}`
  | `progress.viewModes.${keyof typeof enTranslations.progress.viewModes}`
  | `progress.mood.${keyof typeof enTranslations.progress.mood}`
  | `progress.health.${keyof typeof enTranslations.progress.health}`
  | `progress.emptyStates.${keyof typeof enTranslations.progress.emptyStates}`
  | `contacts.categories.${keyof typeof enTranslations.contacts.categories}`
  | `crisis.emergencyPlans.${keyof typeof enTranslations.crisis.emergencyPlans}`
  | `crisis.relapsePlans.${keyof typeof enTranslations.crisis.relapsePlans}`
  | `crisis.emergencyCards.${keyof typeof enTranslations.crisis.emergencyCards}`
  | `errors.${keyof typeof enTranslations.errors}`
  | `common.next`; // Add common.next to NestedTranslationKey

// Translation resources
const translations = {
  en: enTranslations,
  nl: nlTranslations,
} as const;

// Interpolation interface for variables in translations
export interface InterpolationValues {
  [key: string]: string | number;
}

/**
 * Translation service class
 */
export class TranslationService {
  private currentLanguage: SupportedLanguage = 'en';

  constructor(language: SupportedLanguage = 'en') {
    this.currentLanguage = language;
  }

  /**
   * Set the current language
   */
  setLanguage(language: SupportedLanguage): void {
    this.currentLanguage = language;
  }

  /**
   * Get the current language
   */
  getLanguage(): SupportedLanguage {
    return this.currentLanguage;
  }

  /**
   * Get available languages
   */
  getAvailableLanguages(): SupportedLanguage[] {
    return Object.keys(translations) as SupportedLanguage[];
  }

  /**
   * Get translation by nested key (e.g., 'common.save')
   */
  translate(key: NestedTranslationKey, interpolation?: InterpolationValues): string {
    const keys = key.split('.');
    let value: unknown = translations[this.currentLanguage];

    // Navigate through nested object
    for (const k of keys) {
      if (value && typeof value === 'object' && value !== null && k in value) {
        value = (value as Record<string, unknown>)[k];
      } else {
        // Fallback to English if key not found
        value = this.getFallbackTranslation(key);
        break;
      }
    }

    // Handle arrays (like commonTriggers) - return as JSON string for now
    if (Array.isArray(value)) {
      return JSON.stringify(value);
    }

    // Ensure we have a string
    const translation = typeof value === 'string' ? value : key;

    // Apply interpolation if provided
    if (interpolation) {
      return this.interpolate(translation, interpolation);
    }

    return translation;
  }

  /**
   * Get translation with fallback to English
   */
  private getFallbackTranslation(key: NestedTranslationKey): string {
    if (this.currentLanguage === 'en') {
      return key; // Return key if already English and not found
    }

    const keys = key.split('.');
    let value: unknown = translations.en;

    for (const k of keys) {
      if (value && typeof value === 'object' && value !== null && k in value) {
        value = (value as Record<string, unknown>)[k];
      } else {
        return key; // Return key if not found in fallback either
      }
    }

    return typeof value === 'string' ? value : key;
  }

  /**
   * Interpolate variables in translation strings
   * Example: "Hello {{name}}" with {name: "John"} becomes "Hello John"
   */
  private interpolate(template: string, values: InterpolationValues): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return values[key]?.toString() || match;
    });
  }

  /**
   * Get translation for mood labels
   */
  getMoodLabel(value: number): string {
    switch (value) {
      case 1: return this.translate('progress.mood.veryBad');
      case 2: return this.translate('progress.mood.bad');
      case 3: return this.translate('progress.mood.neutral');
      case 4: return this.translate('progress.mood.good');
      case 5: return this.translate('progress.mood.veryGood');
      default: return this.translate('progress.mood.neutral');
    }
  }

  /**
   * Get translation for craving labels
   */
  getCravingLabel(value: number): string {
    switch (value) {
      case 1: return this.translate('progress.mood.none');
      case 2: return this.translate('progress.mood.mild');
      case 3: return this.translate('progress.mood.moderate');
      case 4: return this.translate('progress.mood.strong');
      case 5: return this.translate('progress.mood.veryStrong');
      default: return this.translate('progress.mood.none');
    }
  }

  /**
   * Get translation for health metrics
   */
  getHealthMetricLabel(metric: string, value: number): string {
    const key = `progress.health.${metric}` as NestedTranslationKey;
    return this.translate(key, { value: value.toString() });
  }

  /**
   * Get contact category name
   */
  getContactCategoryName(category: string): string {
    const key = `contacts.categories.${category}` as NestedTranslationKey;
    return this.translate(key);
  }

  /**
   * Get common triggers array
   */
  getCommonTriggers(): string[] {
    const triggers = translations[this.currentLanguage].progress.commonTriggers;
    return Array.isArray(triggers) ? triggers : [];
  }
}

// Create singleton instance
export const translationService = new TranslationService();

// Export convenience function
export const t = (key: NestedTranslationKey, interpolation?: InterpolationValues): string => {
  return translationService.translate(key, interpolation);
}; 