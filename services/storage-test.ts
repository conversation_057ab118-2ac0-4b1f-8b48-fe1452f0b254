import { hybridStorageAdapters } from './hybrid-storage';

/**
 * Test utility to verify storage system is working
 */
export const testStorageSystem = async (): Promise<{
  success: boolean;
  errors: string[];
  results: Record<string, boolean>;
}> => {
  const results: Record<string, boolean> = {};
  const errors: string[] = [];
  let success = true;

  console.log('Testing storage system...');

  // Test each storage adapter
  const adapters = {
    userProfile: hybridStorageAdapters.userProfile,
    quotes: hybridStorageAdapters.quotes,
    library: hybridStorageAdapters.library,
    profile: hybridStorageAdapters.profile,
  };

  for (const [name, adapter] of Object.entries(adapters)) {
    try {
      console.log(`Testing ${name} storage...`);
      
      const testKey = `test_${name}_${Date.now()}`;
      const testValue = JSON.stringify({ test: true, timestamp: Date.now() });

      // Test setItem
      await adapter.setItem(testKey, testValue);
      
      // Test getItem
      const retrieved = await adapter.getItem(testKey);
      
      if (retrieved === testValue) {
        console.log(`✅ ${name} storage working correctly`);
        results[name] = true;
      } else {
        console.log(`❌ ${name} storage failed: retrieved value doesn't match`);
        results[name] = false;
        success = false;
        errors.push(`${name}: Retrieved value doesn't match stored value`);
      }

      // Test removeItem
      await adapter.removeItem(testKey);
      const afterRemove = await adapter.getItem(testKey);
      
      if (afterRemove === null) {
        console.log(`✅ ${name} storage removal working correctly`);
      } else {
        console.log(`⚠️ ${name} storage removal may have issues`);
        errors.push(`${name}: Item not properly removed`);
      }

    } catch (error) {
      console.error(`❌ ${name} storage failed with error:`, error);
      results[name] = false;
      success = false;
      errors.push(`${name}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  console.log('Storage system test completed');
  return { success, errors, results };
};

/**
 * Quick storage health check
 */
export const quickStorageCheck = async (): Promise<boolean> => {
  try {
    const testResult = await testStorageSystem();
    return testResult.success;
  } catch (error) {
    console.error('Storage health check failed:', error);
    return false;
  }
}; 