import AsyncStorage, { type AsyncStorageStatic } from '@react-native-async-storage/async-storage';
import { simpleDatabaseService } from './database-simple';

export interface MigrationResult {
  success: boolean;
  migratedStores: string[];
  errors: string[];
  totalRecords: number;
}

/**
 * Migration service to move data from AsyncStorage to SQLite
 * This handles migration for all Zustand stores that were previously using AsyncStorage
 */
export class MigrationService {
  private static instance: MigrationService;

  static getInstance(): MigrationService {
    if (!MigrationService.instance) {
      MigrationService.instance = new MigrationService();
    }
    return MigrationService.instance;
  }

  /**
   * Migrate all Zustand store data from AsyncStorage to SQLite
   */
  async migrateAllStores(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      migratedStores: [],
      errors: [],
      totalRecords: 0
    };

    try {
      console.log('Starting migration from AsyncStorage to SQLite...');

      // Get all AsyncStorage keys
      const keys = await AsyncStorage.getAllKeys();
      console.log(`Found ${keys.length} AsyncStorage keys`);

      // Define store mappings
      const storeMappings = [
        {
          keyPattern: 'user-profile-storage',
          tableName: 'zustand_user_storage',
          storeName: 'User Profile Store'
        },
        {
          keyPattern: 'quotes-storage',
          tableName: 'zustand_quotes_storage',
          storeName: 'Quotes Store'
        },
        {
          keyPattern: 'library-storage',
          tableName: 'zustand_library_storage',
          storeName: 'Library Store'
        },
        {
          keyPattern: 'profile-storage',
          tableName: 'zustand_profile_storage',
          storeName: 'Profile Store'
        }
      ];

      // Migrate each store
      for (const mapping of storeMappings) {
        try {
          const migrated = await this.migrateStore(keys, mapping);
          if (migrated) {
            result.migratedStores.push(mapping.storeName);
            result.totalRecords++;
          }
        } catch (error) {
          const errorMsg = `Failed to migrate ${mapping.storeName}: ${error instanceof Error ? error.message : String(error)}`;
          result.errors.push(errorMsg);
          console.error(errorMsg);
          result.success = false;
        }
      }

      // Also migrate any health data that was stored separately
      await this.migrateHealthData(keys, result);

      console.log(`Migration completed. Migrated ${result.migratedStores.length} stores with ${result.totalRecords} total records`);
      
      if (result.success && result.migratedStores.length > 0) {
        // Optionally clear AsyncStorage after successful migration
        await this.cleanupAsyncStorage(keys, storeMappings);
      }

    } catch (error) {
      result.success = false;
      result.errors.push(`Migration failed: ${error instanceof Error ? error.message : String(error)}`);
      console.error('Migration failed:', error);
    }

    return result;
  }

  /**
   * Migrate a specific store from AsyncStorage to SQLite
   */
  private async migrateStore(
    keys: readonly string[], 
    mapping: { keyPattern: string; tableName: string; storeName: string }
  ): Promise<boolean> {
    const storeKey = keys.find((key: string) => key.includes(mapping.keyPattern));
    
    if (!storeKey) {
      console.log(`No data found for ${mapping.storeName}`);
      return false;
    }

    const storeData = await AsyncStorage.getItem(storeKey);
    if (!storeData) {
      console.log(`Empty data for ${mapping.storeName}`);
      return false;
    }

    // Store the data in SQLite
    await simpleDatabaseService.setGenericData(mapping.tableName, {
      key: mapping.keyPattern,
      value: storeData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

    console.log(`Successfully migrated ${mapping.storeName}`);
    return true;
  }

  /**
   * Migrate health data that was stored with date-based keys
   */
  private async migrateHealthData(keys: readonly string[], result: MigrationResult): Promise<void> {
    const healthDataKeys = keys.filter((key: string) => key.startsWith('healthData_'));
    
    for (const key of healthDataKeys) {
      try {
        const healthData = await AsyncStorage.getItem(key);
        if (healthData) {
          const date = key.replace('healthData_', '');
          const parsedHealthData = JSON.parse(healthData);
          await simpleDatabaseService.saveHealthData(date, parsedHealthData);
          result.totalRecords++;
        }
      } catch (error) {
        result.errors.push(`Failed to migrate health data for ${key}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    if (healthDataKeys.length > 0) {
      result.migratedStores.push('Health Data');
      console.log(`Migrated ${healthDataKeys.length} health data records`);
    }
  }

  /**
   * Clean up AsyncStorage after successful migration
   */
  private async cleanupAsyncStorage(
    keys: readonly string[], 
    storeMappings: { keyPattern: string; tableName: string; storeName: string }[]
  ): Promise<void> {
    try {
      const keysToRemove: string[] = [];

      // Add store keys
      for (const mapping of storeMappings) {
        const storeKey = keys.find((key: string) => key.includes(mapping.keyPattern));
        if (storeKey) {
          keysToRemove.push(storeKey);
        }
      }

      // Add health data keys
      const healthDataKeys = keys.filter((key: string) => key.startsWith('healthData_'));
      keysToRemove.push(...healthDataKeys);

      if (keysToRemove.length > 0) {
        await AsyncStorage.multiRemove(keysToRemove);
        console.log(`Cleaned up ${keysToRemove.length} AsyncStorage keys`);
      }
    } catch (error) {
      console.warn('Failed to cleanup AsyncStorage:', error);
      // Don't throw here as migration was successful
    }
  }

  /**
   * Check if migration is needed
   */
  async isMigrationNeeded(): Promise<boolean> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const storeKeys = [
        'user-profile-storage',
        'quotes-storage', 
        'library-storage',
        'profile-storage'
      ];

      return keys.some((key: string) => 
        storeKeys.some((storeKey: string) => key.includes(storeKey)) ||
        key.startsWith('healthData_')
      );
    } catch (error) {
      console.error('Error checking migration status:', error);
      return false;
    }
  }

  /**
   * Get migration status
   */
  async getMigrationStatus(): Promise<{
    asyncStorageKeys: number;
    sqliteRecords: number;
    needsMigration: boolean;
  }> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const stats = await simpleDatabaseService.getDatabaseStats();
      const needsMigration = await this.isMigrationNeeded();

      return {
        asyncStorageKeys: keys.length,
        sqliteRecords: stats.totalRecords,
        needsMigration
      };
    } catch (error) {
      console.error('Error getting migration status:', error);
      return {
        asyncStorageKeys: 0,
        sqliteRecords: 0,
        needsMigration: false
      };
    }
  }
}

// Export singleton instance
export const migrationService = MigrationService.getInstance(); 