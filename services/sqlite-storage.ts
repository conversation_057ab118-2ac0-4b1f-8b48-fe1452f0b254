import { StateStorage } from 'zustand/middleware';
import { simpleDatabaseService, initDatabase } from './database-simple';

export interface SQLiteStorageOptions {
  tableName: string;
  keyColumn?: string;
  valueColumn?: string;
}

/**
 * Custom SQLite storage adapter for Zustand persist middleware
 * This replaces AsyncStorage with SQLite for better performance and reliability
 */
// Database initialization promise to ensure it's only initialized once
let initPromise: Promise<void> | null = null;

const ensureDatabaseInitialized = async (): Promise<void> => {
  if (!initPromise) {
    initPromise = initDatabase();
  }
  await initPromise;
};

export const createSQLiteStorage = (options: SQLiteStorageOptions): StateStorage => {
  const { tableName, keyColumn = 'key', valueColumn = 'value' } = options;

  return {
    getItem: async (name: string): Promise<string | null> => {
      try {
        // Ensure database is initialized before any operation
        await ensureDatabaseInitialized();
        const result = await simpleDatabaseService.getGenericData(tableName, keyColumn, name);
        return result ? String(result[valueColumn] || '') : null;
      } catch (error) {
        console.warn(`Error getting item from SQLite storage (${tableName}):`, error);
        // Return null instead of throwing to allow graceful fallback
        return null;
      }
    },

    setItem: async (name: string, value: string): Promise<void> => {
      try {
        // Ensure database is initialized before any operation
        await ensureDatabaseInitialized();
        await simpleDatabaseService.setGenericData(tableName, {
          [keyColumn]: name,
          [valueColumn]: value,
          updated_at: new Date().toISOString()
        });
      } catch (error) {
        console.warn(`Error setting item in SQLite storage (${tableName}):`, error);
        // Don't throw error to prevent app crashes during initialization
        // The data will be lost but app will continue to function
      }
    },

    removeItem: async (name: string): Promise<void> => {
      try {
        // Ensure database is initialized before any operation
        await ensureDatabaseInitialized();
        await simpleDatabaseService.deleteGenericData(tableName, keyColumn, name);
      } catch (error) {
        console.warn(`Error removing item from SQLite storage (${tableName}):`, error);
        // Don't throw error to prevent app crashes
      }
    }
  };
};

/**
 * Pre-configured storage adapters for common store types
 */
export const sqliteStorageAdapters = {
  userProfile: createSQLiteStorage({ tableName: 'zustand_user_storage' }),
  quotes: createSQLiteStorage({ tableName: 'zustand_quotes_storage' }),
  library: createSQLiteStorage({ tableName: 'zustand_library_storage' }),
  profile: createSQLiteStorage({ tableName: 'zustand_profile_storage' }),
  audioPlayer: createSQLiteStorage({ tableName: 'zustand_audio_storage' })
}; 