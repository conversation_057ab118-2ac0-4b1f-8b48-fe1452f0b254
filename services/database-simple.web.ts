import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Web-specific database service that only uses AsyncStorage
// This avoids any SQLite imports that cause bundling issues on web

// Initialize database (no-op for web)
export const initDatabase = async (): Promise<void> => {
  console.log('Using AsyncStorage as database for web platform');
};

// Simple database service for web
export class SimpleDatabaseService {
  private static instance: SimpleDatabaseService;

  static getInstance(): SimpleDatabaseService {
    if (!SimpleDatabaseService.instance) {
      SimpleDatabaseService.instance = new SimpleDatabaseService();
    }
    return SimpleDatabaseService.instance;
  }

  private async getFromAsyncStorage(key: string): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(key);
    } catch (error) {
      console.error('Error getting from AsyncStorage:', error);
      return null;
    }
  }

  private async setToAsyncStorage(key: string, value: string): Promise<void> {
    try {
      await AsyncStorage.setItem(key, value);
    } catch (error) {
      console.error('Error setting to AsyncStorage:', error);
      throw error;
    }
  }

  // User Profile operations
  async saveUserProfile(profileData: Record<string, unknown>): Promise<void> {
    await this.setToAsyncStorage('user_profile', JSON.stringify(profileData));
  }

  async getUserProfile(): Promise<Record<string, unknown> | null> {
    const data = await this.getFromAsyncStorage('user_profile');
    return data ? JSON.parse(data) : null;
  }

  // Health data operations (simplified for web)
  async saveHealthData(date: string, data: { sleep?: number; pills?: number; hydration?: number; exercise?: number }): Promise<void> {
    const key = `health_data_${date}`;
    await this.setToAsyncStorage(key, JSON.stringify(data));
  }

  async getHealthData(date: string): Promise<{ sleep?: number; pills?: number; hydration?: number; exercise?: number } | null> {
    const key = `health_data_${date}`;
    const data = await this.getFromAsyncStorage(key);
    return data ? JSON.parse(data) : null;
  }

  async getHealthDataRange(startDate: string, endDate: string): Promise<any[]> {
    // Simplified implementation for web
    const results: any[] = [];
    // This would need a more sophisticated implementation for production
    return results;
  }

  // Mood entries operations (simplified for web)
  async addMoodEntry(entry: { id: string; date: string; mood: number; cravingIntensity: number; notes?: string }): Promise<void> {
    const key = `mood_entry_${entry.id}`;
    await this.setToAsyncStorage(key, JSON.stringify(entry));
  }

  async getMoodEntries(limit: number = 50): Promise<any[]> {
    // Simplified implementation for web
    const results: any[] = [];
    return results;
  }

  // Emergency contacts operations (simplified for web)
  async addEmergencyContact(contact: { id: string; name: string; phone: string; email?: string; relationship: string; notes?: string }): Promise<void> {
    const key = `emergency_contact_${contact.id}`;
    await this.setToAsyncStorage(key, JSON.stringify(contact));
  }

  async getEmergencyContacts(): Promise<any[]> {
    // Simplified implementation for web
    const results: any[] = [];
    return results;
  }

  async deleteEmergencyContact(id: string): Promise<void> {
    const key = `emergency_contact_${id}`;
    await AsyncStorage.removeItem(key);
  }

  // Documents operations (simplified for web)
  async addDocument(doc: { id: string; title: string; content?: string; category?: string; date: string }): Promise<void> {
    const key = `document_${doc.id}`;
    await this.setToAsyncStorage(key, JSON.stringify(doc));
  }

  async getDocuments(): Promise<any[]> {
    // Simplified implementation for web
    const results: any[] = [];
    return results;
  }

  // Media files operations (simplified for web)
  async addMediaFile(file: { id: string; title: string; description?: string; type: string; category?: string; fileName?: string; fileSize?: number; date: string }): Promise<void> {
    const key = `media_file_${file.id}`;
    await this.setToAsyncStorage(key, JSON.stringify(file));
  }

  async getMediaFiles(): Promise<any[]> {
    // Simplified implementation for web
    const results: any[] = [];
    return results;
  }

  // Generic data operations for Zustand storage
  async getGenericData(tableName: string, keyColumn: string, key: string): Promise<Record<string, unknown> | null> {
    const storageKey = `${tableName}_${key}`;
    const data = await this.getFromAsyncStorage(storageKey);
    return data ? JSON.parse(data) : null;
  }

  async setGenericData(tableName: string, data: Record<string, unknown>): Promise<void> {
    const key = data.key as string;
    const storageKey = `${tableName}_${key}`;
    await this.setToAsyncStorage(storageKey, JSON.stringify(data));
  }

  async deleteGenericData(tableName: string, keyColumn: string, key: string): Promise<void> {
    const storageKey = `${tableName}_${key}`;
    await AsyncStorage.removeItem(storageKey);
  }

  // Migration operations (simplified for web)
  async migrateFromAsyncStorage(): Promise<void> {
    console.log('Migration not needed on web platform');
  }

  // Database stats (simplified for web)
  async getDatabaseStats(): Promise<Record<string, unknown>> {
    return {
      platform: 'web',
      storage: 'AsyncStorage',
      initialized: true
    };
  }
}

// Export singleton instance
export const simpleDatabaseService = SimpleDatabaseService.getInstance();
