stijn@Mac sobrixhealth % npx tsc --noEmit
stijn@Mac sobrixhealth % npx eslint      

/Users/<USER>/WEB DEV/sobrixhealth/babel.config.cjs
  2:1  error  'module' is not defined  no-undef

/Users/<USER>/WEB DEV/sobrixhealth/components/contacts/ContactImport.tsx
   63:6   warning  React Hook useEffect has a missing dependency: 'loadContacts'. Either include it or remove the dependency array                                                                                      react-hooks/exhaustive-deps
   68:6   warning  React Hook useEffect has a missing dependency: 'filterContacts'. Either include it or remove the dependency array                                                                                    react-hooks/exhaustive-deps
  260:13  warning  Inline style: { backgroundColor: 'isSelected ? colors.primary : "transparent"' }                                                                                                                     react-native/no-inline-styles
  288:24  warning  Inline style: { width: 40 }                                                                                                                                                                          react-native/no-inline-styles
  385:21  warning  Inline style: {
  backgroundColor: 'selectedContacts.length === filteredContacts.length\n' +
    '                          ? colors.primary\n' +
    '                          : "transparent"'
}  react-native/no-inline-styles
  424:65  warning  Inline style: { opacity: 0.5 }                                                                                                                                                                       react-native/no-inline-styles

/Users/<USER>/WEB DEV/sobrixhealth/components/contacts/ContactItem.tsx
  132:14  warning  '_e' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WEB DEV/sobrixhealth/components/dashboard/AchievementCard.tsx
   6:3   warning  'Animated' is defined but never used                           @typescript-eslint/no-unused-vars
  10:10  warning  'LinearGradient' is defined but never used                     @typescript-eslint/no-unused-vars
  24:11  warning  'Colors' is defined but never used                             @typescript-eslint/no-unused-vars
  46:11  warning  'animatedStyle' is assigned a value but never used             @typescript-eslint/no-unused-vars
  50:9   warning  'getIcon' is assigned a value but never used                   @typescript-eslint/no-unused-vars
  69:9   warning  'getRarityColors' is assigned a value but never used           @typescript-eslint/no-unused-vars
  88:9   warning  Inline style: { opacity: 'achievement.isUnlocked ? 1 : 0.6' }  react-native/no-inline-styles

/Users/<USER>/WEB DEV/sobrixhealth/components/dashboard/CircularView.tsx
   46:3  warning  'costFrequency' is defined but never used. Allowed unused args must match /^_/u                                                                @typescript-eslint/no-unused-vars
  122:6  warning  React Hook useEffect has missing dependencies: 'progressAnimation' and 'savingsAnimation'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/WEB DEV/sobrixhealth/components/dashboard/HabitsCard.tsx
  54:6  warning  React Hook useEffect has missing dependencies: 'fadeAnim' and 'scaleAnim'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/WEB DEV/sobrixhealth/components/dashboard/HealthMetricsCard.tsx
   16:3   warning  'Zap' is defined but never used                                                                                                @typescript-eslint/no-unused-vars
   66:6   warning  React Hook useEffect has missing dependencies: 'fadeAnim' and 'scaleAnim'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  159:17  warning  Inline style: { borderRadius: 16, padding: 16 }                                                                                react-native/no-inline-styles

/Users/<USER>/WEB DEV/sobrixhealth/components/dashboard/SobrietyCard.tsx
  57:6  warning  React Hook useEffect has missing dependencies: 'countAnim', 'fadeAnim', and 'scaleAnim'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/WEB DEV/sobrixhealth/components/dashboard/WeeklyProgressCard.tsx
  64:6  warning  React Hook useEffect has missing dependencies: 'barAnimations', 'fadeAnim', and 'scaleAnim'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/WEB DEV/sobrixhealth/components/health/LogHealthModal.tsx
  99:6  warning  React Hook useEffect has a missing dependency: 'loadDataForDate'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/WEB DEV/sobrixhealth/components/mindfulness/BreathingExercise.tsx
   15:89  warning  'activeExercise' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars
  244:15  warning  Inline style: { borderRadius: 100 }                                               react-native/no-inline-styles

/Users/<USER>/WEB DEV/sobrixhealth/components/mindfulness/ExerciseForm.tsx
  229:21  warning  Inline style: { color: "type === exerciseType.value ? '#fff' : colors.text" }  react-native/no-inline-styles
  342:46  warning  Inline style: { color: '#fff' }                                                react-native/no-inline-styles

/Users/<USER>/WEB DEV/sobrixhealth/components/mindfulness/ExerciseList.tsx
   14:3   warning  'Play' is defined but never used                                                    @typescript-eslint/no-unused-vars
   38:3   warning  'onEditExercise' is defined but never used. Allowed unused args must match /^_/u    @typescript-eslint/no-unused-vars
   39:3   warning  'onDeleteExercise' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars
  198:43  warning  Inline style: { width: '75%' }                                                      react-native/no-inline-styles

/Users/<USER>/WEB DEV/sobrixhealth/components/mindfulness/GratitudeExercise.tsx
   37:89   warning  'activeExercise' is defined but never used. Allowed unused args must match /^_/u                                                                                                                               @typescript-eslint/no-unused-vars
   37:105  warning  'onDurationUpdate' is defined but never used. Allowed unused args must match /^_/u                                                                                                                             @typescript-eslint/no-unused-vars
  107:9    warning  The 'gratitudePrompts' conditional could make the dependencies of useCallback Hook (at line 135) change on every render. To fix this, wrap the initialization of 'gratitudePrompts' in its own useMemo() Hook  react-hooks/exhaustive-deps

/Users/<USER>/WEB DEV/sobrixhealth/components/mindfulness/MuscleRelaxationExercise.tsx
  24:103  warning  'activeExercise' is defined but never used. Allowed unused args must match /^_/u                                                                                                                       @typescript-eslint/no-unused-vars
  36:9    warning  The 'muscleGroups' conditional could make the dependencies of useCallback Hook (at line 158) change on every render. To fix this, wrap the initialization of 'muscleGroups' in its own useMemo() Hook  react-hooks/exhaustive-deps

/Users/<USER>/WEB DEV/sobrixhealth/components/mindfulness/VoiceControl.tsx
  140:13  warning  Inline style: { color: 'isEnabled ? "#fff" : colors.text' }  react-native/no-inline-styles

/Users/<USER>/WEB DEV/sobrixhealth/components/progress/shared/LineChart.tsx
   86:18  warning  Inline style: { position: 'absolute', left: 10, width: 30 }  react-native/no-inline-styles
   93:43  warning  Inline style: { textAlign: 'right' }                         react-native/no-inline-styles
  142:18  warning  Inline style: { position: 'absolute', width: 40 }            react-native/no-inline-styles
  149:43  warning  Inline style: { textAlign: 'center' }                        react-native/no-inline-styles

/Users/<USER>/WEB DEV/sobrixhealth/components/settings/ProfileModal.tsx
   22:3   warning  'ModalWrapper' is defined but never used  @typescript-eslint/no-unused-vars
   24:3   warning  'ModalFooter' is defined but never used   @typescript-eslint/no-unused-vars
  242:32  warning  Inline style: { paddingBottom: 20 }       react-native/no-inline-styles

/Users/<USER>/WEB DEV/sobrixhealth/components/shared/ModalFooter.tsx
  66:13  warning  Inline style: { opacity: 'submitDisabled ? 0.6 : 1' }  react-native/no-inline-styles

/Users/<USER>/WEB DEV/sobrixhealth/components/shared/MoodSelector.tsx
  99:17  warning  Inline style: { fontSize: 'size === "small" ? 10 : size === "large" ? 14 : 12' }  react-native/no-inline-styles

/Users/<USER>/WEB DEV/sobrixhealth/components/shared/OptionSelector.tsx
  85:15  warning  Inline style: { color: "isSelected(option.value) ? '#fff' : colors.text" }  react-native/no-inline-styles

/Users/<USER>/WEB DEV/sobrixhealth/hooks/animation/useBreathingAnimation.ts
  136:6  warning  React Hook useEffect has missing dependencies: 'auraOpacity' and 'circleSize'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/WEB DEV/sobrixhealth/hooks/animation/useFadeScaleAnimation.ts
  68:6  warning  React Hook useEffect has a missing dependency: 'startAnimation'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/WEB DEV/sobrixhealth/hooks/animation/useStaggeredAnimation.ts
  75:6  warning  React Hook useEffect has a missing dependency: 'startAnimation'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/WEB DEV/sobrixhealth/hooks/media/use-speech.ts
   3:13  warning  'Speech' is defined but never used                                                                             @typescript-eslint/no-unused-vars
  40:6   warning  React Hook useEffect has a missing dependency: 'isSpeaking'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/WEB DEV/sobrixhealth/scripts/auto-fix-issues.cjs
    3:12  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    3:12  error  'require' is not defined                 no-undef
    4:14  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    4:14  error  'require' is not defined                 no-undef
    5:22  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    5:22  error  'require' is not defined                 no-undef
   36:5   error  'console' is not defined                 no-undef
   77:20  error  'error' is defined but never used        @typescript-eslint/no-unused-vars
   81:16  error  'error' is defined but never used        @typescript-eslint/no-unused-vars
  111:62  error  'process' is not defined                 no-undef
  150:68  error  'process' is not defined                 no-undef
  222:66  error  'process' is not defined                 no-undef
  252:68  error  'process' is not defined                 no-undef
  291:64  error  'process' is not defined                 no-undef
  313:67  error  'process' is not defined                 no-undef
  352:14  error  'error' is defined but never used        @typescript-eslint/no-unused-vars
  368:14  error  'error' is defined but never used        @typescript-eslint/no-unused-vars
  383:34  error  'process' is not defined                 no-undef
  412:23  error  'console' is not defined                 no-undef

/Users/<USER>/WEB DEV/sobrixhealth/scripts/check-codebase-health.cjs
    3:12  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    3:12  error  'require' is not defined                 no-undef
    4:14  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    4:14  error  'require' is not defined                 no-undef
    5:22  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    5:22  error  'require' is not defined                 no-undef
   54:5   error  'console' is not defined                 no-undef
  108:20  error  'error' is defined but never used        @typescript-eslint/no-unused-vars
  113:16  error  'error' is defined but never used        @typescript-eslint/no-unused-vars
  203:18  error  'parseError' is defined but never used   @typescript-eslint/no-unused-vars
  266:14  error  'error' is defined but never used        @typescript-eslint/no-unused-vars
  294:44  error  'process' is not defined                 no-undef
  400:7   error  'process' is not defined                 no-undef
  403:7   error  'process' is not defined                 no-undef
  406:7   error  'process' is not defined                 no-undef
  412:5   error  'require' is not defined                 no-undef
  412:22  error  'module' is not defined                  no-undef
  415:5   error  'console' is not defined                 no-undef
  416:5   error  'process' is not defined                 no-undef
  420:1   error  'module' is not defined                  no-undef

/Users/<USER>/WEB DEV/sobrixhealth/scripts/comprehensive-color-fix.cjs
    3:12  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    3:12  error  'require' is not defined                 no-undef
    4:14  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    4:14  error  'require' is not defined                 no-undef
    5:22  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    5:22  error  'require' is not defined                 no-undef
   36:5   error  'console' is not defined                 no-undef
   77:20  error  '_error' is defined but never used       @typescript-eslint/no-unused-vars
   81:16  error  '_error' is defined but never used       @typescript-eslint/no-unused-vars
  121:64  error  'process' is not defined                 no-undef
  150:66  error  'process' is not defined                 no-undef
  186:64  error  'process' is not defined                 no-undef
  222:63  error  'process' is not defined                 no-undef
  249:62  error  'process' is not defined                 no-undef
  275:68  error  'process' is not defined                 no-undef
  298:53  error  'process' is not defined                 no-undef
  315:14  error  '_error' is defined but never used       @typescript-eslint/no-unused-vars
  324:34  error  'process' is not defined                 no-undef
  352:19  error  'console' is not defined                 no-undef

/Users/<USER>/WEB DEV/sobrixhealth/scripts/comprehensive-fix.cjs
    3:12  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    3:12  error  'require' is not defined                 no-undef
    4:14  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    4:14  error  'require' is not defined                 no-undef
    5:22  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    5:22  error  'require' is not defined                 no-undef
   26:5   error  'console' is not defined                 no-undef
  337:14  error  'error' is defined but never used        @typescript-eslint/no-unused-vars
  368:16  error  'error' is defined but never used        @typescript-eslint/no-unused-vars
  405:14  error  'error' is defined but never used        @typescript-eslint/no-unused-vars
  414:5   error  'require' is not defined                 no-undef
  414:22  error  'module' is not defined                  no-undef
  417:5   error  'console' is not defined                 no-undef
  418:5   error  'process' is not defined                 no-undef
  422:1   error  'module' is not defined                  no-undef

/Users/<USER>/WEB DEV/sobrixhealth/scripts/critical-fixes.cjs
    1:12  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    1:12  error  'require' is not defined                 no-undef
    2:22  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    2:22  error  'require' is not defined                 no-undef
    4:1   error  'console' is not defined                 no-undef
   75:7   error  'console' is not defined                 no-undef
   79:5   error  'console' is not defined                 no-undef
   83:1   error  'console' is not defined                 no-undef
   86:1   error  'console' is not defined                 no-undef
   97:3   error  'console' is not defined                 no-undef
   99:3   error  'console' is not defined                 no-undef
  102:1   error  'console' is not defined                 no-undef
  105:3   error  'console' is not defined                 no-undef
  109:3   error  'console' is not defined                 no-undef
  112:1   error  'console' is not defined                 no-undef

/Users/<USER>/WEB DEV/sobrixhealth/scripts/final-fix.cjs
    1:12  error  A `require()` style import is forbidden      @typescript-eslint/no-require-imports
    1:12  error  'require' is not defined                     no-undef
    2:7   error  'path' is assigned a value but never used    @typescript-eslint/no-unused-vars
    2:14  error  A `require()` style import is forbidden      @typescript-eslint/no-require-imports
    2:14  error  'require' is not defined                     no-undef
    3:22  error  A `require()` style import is forbidden      @typescript-eslint/no-require-imports
    3:22  error  'require' is not defined                     no-undef
    5:1   error  'console' is not defined                     no-undef
   94:7   error  'console' is not defined                     no-undef
   98:5   error  'console' is not defined                     no-undef
  102:1   error  'console' is not defined                     no-undef
  105:1   error  'console' is not defined                     no-undef
  117:3   error  'console' is not defined                     no-undef
  119:3   error  'console' is not defined                     no-undef
  123:1   error  'console' is not defined                     no-undef
  131:3   error  'console' is not defined                     no-undef
  133:3   error  'console' is not defined                     no-undef
  137:1   error  'console' is not defined                     no-undef
  146:3   error  'console' is not defined                     no-undef
  148:3   error  'console' is not defined                     no-undef
  151:1   error  'console' is not defined                     no-undef
  152:1   error  'console' is not defined                     no-undef
  154:1   error  'console' is not defined                     no-undef
  156:9   error  'result' is assigned a value but never used  @typescript-eslint/no-unused-vars
  157:3   error  'console' is not defined                     no-undef
  161:3   error  'console' is not defined                     no-undef
  164:1   error  'console' is not defined                     no-undef
  167:5   error  'require' is not defined                     no-undef
  167:22  error  'module' is not defined                      no-undef
  168:3   error  'console' is not defined                     no-undef
  169:3   error  'process' is not defined                     no-undef
  172:1   error  'module' is not defined                      no-undef

/Users/<USER>/WEB DEV/sobrixhealth/scripts/fix-app.js
    9:8   warning  'path' is defined but never used   @typescript-eslint/no-unused-vars
  103:12  warning  'error' is defined but never used  @typescript-eslint/no-unused-vars
  120:12  warning  'error' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WEB DEV/sobrixhealth/scripts/fix-specific-issues.cjs
    3:12  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    3:12  error  'require' is not defined                 no-undef
    4:14  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    4:14  error  'require' is not defined                 no-undef
   24:5   error  'console' is not defined                 no-undef
  207:63  error  'process' is not defined                 no-undef
  252:39  error  'process' is not defined                 no-undef
  262:19  error  'console' is not defined                 no-undef

/Users/<USER>/WEB DEV/sobrixhealth/scripts/fix-unused-imports.cjs
    3:12  error  A `require()` style import is forbidden                   @typescript-eslint/no-require-imports
    3:12  error  'require' is not defined                                  no-undef
    4:14  error  A `require()` style import is forbidden                   @typescript-eslint/no-require-imports
    4:14  error  'require' is not defined                                  no-undef
    5:22  error  A `require()` style import is forbidden                   @typescript-eslint/no-require-imports
    5:22  error  'require' is not defined                                  no-undef
    8:7   error  'commonUnusedImports' is assigned a value but never used  @typescript-eslint/no-unused-vars
   68:18  error  'error' is defined but never used                         @typescript-eslint/no-unused-vars
   73:14  error  'error' is defined but never used                         @typescript-eslint/no-unused-vars
  103:12  error  'error' is defined but never used                         @typescript-eslint/no-unused-vars
  154:5   error  'console' is not defined                                  no-undef
  162:3   error  'console' is not defined                                  no-undef
  164:3   error  'console' is not defined                                  no-undef
  172:7   error  'console' is not defined                                  no-undef
  173:7   error  'console' is not defined                                  no-undef
  183:3   error  'console' is not defined                                  no-undef
  186:5   error  'console' is not defined                                  no-undef
  189:14  error  'error' is defined but never used                         @typescript-eslint/no-unused-vars
  190:7   error  'console' is not defined                                  no-undef
  197:5   error  'require' is not defined                                  no-undef
  197:22  error  'module' is not defined                                   no-undef

/Users/<USER>/WEB DEV/sobrixhealth/scripts/targeted-fix.cjs
    3:12  error  A `require()` style import is forbidden      @typescript-eslint/no-require-imports
    3:12  error  'require' is not defined                     no-undef
    4:7   error  'path' is assigned a value but never used    @typescript-eslint/no-unused-vars
    4:14  error  A `require()` style import is forbidden      @typescript-eslint/no-require-imports
    4:14  error  'require' is not defined                     no-undef
    5:22  error  A `require()` style import is forbidden      @typescript-eslint/no-require-imports
    5:22  error  'require' is not defined                     no-undef
   24:5   error  'console' is not defined                     no-undef
  225:14  error  'error' is defined but never used            @typescript-eslint/no-unused-vars
  255:13  error  'result' is assigned a value but never used  @typescript-eslint/no-unused-vars
  272:5   error  'require' is not defined                     no-undef
  272:22  error  'module' is not defined                      no-undef
  275:5   error  'console' is not defined                     no-undef
  276:5   error  'process' is not defined                     no-undef
  280:1   error  'module' is not defined                      no-undef

/Users/<USER>/WEB DEV/sobrixhealth/utils/data/contactUtils.ts
  1:8  warning  'React' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WEB DEV/sobrixhealth/utils/system/notification-utils.ts
  38:3  error  'token' is never reassigned. Use 'const' instead  prefer-const

✖ 222 problems (166 errors, 56 warnings)

stijn@Mac sobrixhealth % 