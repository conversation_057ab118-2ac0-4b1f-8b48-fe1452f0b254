// @ts-check

import eslint from '@eslint/js';
import tseslint from 'typescript-eslint';
import reactPlugin from 'eslint-plugin-react';
import reactHooksPlugin from 'eslint-plugin-react-hooks';
import reactNativePlugin from 'eslint-plugin-react-native';
import jsxA11yPlugin from 'eslint-plugin-jsx-a11y';
import globals from 'globals';

export default tseslint.config(
  {
    // Global ignores
    ignores: [
      'node_modules/',
      '.expo/',
      'dist/',
      'build/',
      'babel.config.js',
      'metro.config.js', // if you have one
      'tailwind.config.js'
    ],
  },
  // Base ESLint recommended rules for all files initially
  eslint.configs.recommended,
  // TypeScript recommended configurations (applies broadly, will be specialized by subsequent configs)
  ...tseslint.configs.recommended,
  {
    files: ['**/*.{ts,tsx}'], // This config will specifically apply to TS/TSX files
    // It inherits from the global and JS configs, then overrides/adds more specific TS/React rules
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
        ecmaVersion: 'latest',
        sourceType: 'module',
        project: './tsconfig.json', // Path to your tsconfig.json
      },
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2021,
        ...reactNativePlugin.environments['react-native'].globals, // React Native globals
      },
    },
    plugins: {
      react: reactPlugin,
      'react-hooks': reactHooksPlugin,
      'react-native': reactNativePlugin,
      'jsx-a11y': jsxA11yPlugin,
    },
    rules: {
      ...reactPlugin.configs.recommended.rules,
      ...reactPlugin.configs['jsx-runtime'].rules, // For new JSX transform
      ...reactHooksPlugin.configs.recommended.rules,
      ...reactNativePlugin.configs.all.rules, // Or .recommended
      ...jsxA11yPlugin.configs.recommended.rules,

      // Custom rules can be added or overridden here
      'react-native/no-unused-styles': 2,
      'react-native/split-platform-components': 2,
      'react-native/no-inline-styles': 1, // Warn for inline styles
      'react-native/no-color-literals': 0, // Allow color literals for now
      'react-native/no-raw-text': ['error', { skip: ['Text.Text']}], // Adjust as needed for custom components
      
      '@typescript-eslint/no-unused-vars': ['warn', { 'argsIgnorePattern': '^_' }],
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      'react/prop-types': 'off', // Not needed with TypeScript
      'react/react-in-jsx-scope': 'off', // Not needed with new JSX transform
    },
    settings: {
      react: {
        version: 'detect', // Automatically detect the React version
      },
    },
  },
  // Specific configuration for JavaScript files
  {
    files: ['**/*.js'],
    // Disable type-aware linting rules for JS files
    ...tseslint.configs.disableTypeChecked,
    languageOptions: {
      globals: {
        ...globals.node, // Enable Node.js global variables
      },
      sourceType: 'commonjs', // Allow CommonJS modules (e.g., require)
    },
    rules: {
      // Ensure TypeScript-specific rules that don't make sense for JS are off
      '@typescript-eslint/no-require-imports': 'off',
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'warn', // Warn instead of off, or 'off' if preferred
      // Keep or adjust other JS-specific rules as needed
      '@typescript-eslint/no-unused-vars': ['warn', { 'argsIgnorePattern': '^_' }],
      // Add any other JS specific rules here
    }
  },
  // Specific configuration for CommonJS script files
  {
    files: ['**/*.cjs', 'babel.config.cjs'],
    // Disable type-aware linting rules for CJS files
    ...tseslint.configs.disableTypeChecked,
    languageOptions: {
      globals: {
        ...globals.node, // Enable Node.js global variables
        console: 'readonly',
        process: 'readonly',
        module: 'readonly',
        require: 'readonly',
      },
      sourceType: 'commonjs', // Allow CommonJS modules (e.g., require)
    },
    rules: {
      // Disable TypeScript-specific rules for CJS files
      '@typescript-eslint/no-require-imports': 'off',
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-vars': ['warn', { 'argsIgnorePattern': '^_' }],
      'no-undef': 'off', // Turn off no-undef since we're defining globals above
    }
  }
);
