{"common": {"save": "Save", "cancel": "Cancel", "close": "Close", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "error": "Error", "success": "Success", "loading": "Loading...", "required": "Required", "optional": "Optional", "add": "Add", "back": "Back", "continue": "Continue", "skip": "<PERSON><PERSON>", "yes": "Yes", "no": "No"}, "navigation": {"dashboard": "Dashboard", "progress": "Progress", "mindfulness": "Mindfulness", "library": "Library", "contacts": "Contacts", "crisis": "Crisis", "settings": "Settings"}, "dashboard": {"quickActions": "Quick Actions", "emergency": "Emergency", "mindfulness": "Mindfulness", "progress": "Progress", "greetings": {"morning": "Good morning", "afternoon": "Good afternoon", "evening": "Good evening"}, "hero": {"daysSober": "days sober", "saved": "saved", "toGoal": "to goal", "nextMilestone": "Next Milestone", "daysToGo": "days to go"}, "todaysFocus": "Today's Focus", "progressInsights": "Progress Insights", "todaysHealth": "Today's Health", "goalsTracked": "goals tracked", "tapToGetStarted": "Tap to get started", "startTracking": "Start tracking your daily health metrics", "stats": {"days": "Days", "saved": "Saved", "relapses": "Relapses"}, "weeklyProgress": {"thisWeek": "This Week", "tapToExpand": "Tap to expand", "days": "Days", "complete": "Complete"}, "emergencyPlan": "Emergency Plan", "relapsePlan": "Relapse Plan", "emergencyCard": "Emergency Card", "emergencyContacts": "Emergency Contacts"}, "health": {"modal": {"title": "Health Metrics", "sleep": "Sleep", "pills": "Pills", "hydration": "Hydration", "exercise": "Exercise", "saveSuccess": "Health data saved!", "saveSuccessMessage": "Health data saved!"}}, "onboarding": {"welcome": {"title": "Welcome to CrisisBox", "subtitle": "Your personal companion on the journey to recovery", "description": "We'll help you track your sobriety, manage cravings, and provide tools to support your recovery journey.", "getStarted": "Get Started", "returningUser": "Returning User? Import Data", "importSuccess": "Data imported successfully!", "importError": "Error importing data. Please try again.", "invalidFile": "Invalid file format. Please select a valid CrisisBox data file.", "selectFile": "Select Data File", "trackProgress": "Track Progress", "mindfulness": "Mindfulness", "support": "Support"}, "language": {"title": "Choose Your Language", "subtitle": "Select the language you prefer to use"}, "name": {"title": "What's your name?", "subtitle": "We'll personalize your experience", "placeholder": "Enter your name", "error": "Please enter your name"}, "addiction": {"title": "What are you recovering from?", "subtitle": "Select the addiction you're working to overcome", "specifyPlaceholder": "Specify your addiction", "error": "Please select an addiction", "customError": "Please specify your addiction"}, "recoveryGoal": {"title": "What's your recovery goal?", "description": "Choose the approach that best fits your journey.", "abstinence": "Abstinence", "harmReduction": "Harm Reduction", "error": "Please select a recovery goal"}, "sobriety": {"title": "When did you start your sobriety?", "subtitle": "This will be your sobriety date", "usageLabel": "How much did you use per day?", "usagePlaceholder": "Enter amount", "usageUnitLabel": "Unit"}, "healthGoals": {"title": "Set Your Health Goals", "subtitle": "Define initial targets for a healthier lifestyle", "goal": "Goal", "target": "Target", "enableGoal": "Enable Goal", "sleep": "Sleep", "exercise": "Exercise", "pills": "Pills", "waterIntake": "Water Intake", "hours": "hours/day", "minutes": "minutes/day", "pillsUnit": "pills/day", "glasses": "glasses/day", "liters": "liters/day"}, "customize": {"title": "Customize Display", "subtitle": "Choose how your dashboard looks", "costLabel": "How much did your addiction cost?", "frequencyLabel": "Frequency", "dashboardLabel": "Dashboard View", "cardsLabel": "Cards", "circularLabel": "Circular", "cardsDescription": "Shows your progress in cards with details", "circularDescription": "Shows your progress in a circular design", "settingsNote": "You can always change this later in settings", "completeSetup": "Complete Setup", "frequency": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly"}, "features": {"trackProgress": "Track Progress", "mindfulness": "Mindfulness", "support": "Support"}}}, "settings": {"notifications": "Notifications", "privacy": "Privacy", "about": "About", "help": "Help & Support", "profile": "Profile", "usage": "Usage Details", "enableNotifications": "Enable notifications to stay updated", "dataPrivacy": "Control how your data is used", "faq": "Frequently Asked Questions", "resetPassword": "How do I reset my password?", "resetPasswordAnswer": "Go to settings and select \"Change Password\".", "exportData": "How can I export my data?", "exportDataAnswer": "Contact us via email for data export.", "dataSafety": "Is my data safe?", "dataSafetyAnswer": "Yes, we use end-to-end encryption for all your data.", "visitWebsite": "Visit Website", "features": "Features", "sobrietyTracker": "Sobriety Tracker", "sobrietyTrackerDesc": "Track your progress and celebrate milestones", "mindfulness": "Mindfulness", "mindfulnessDesc": "Guided meditations and breathing exercises", "crisisSupport": "Crisis Support", "crisisSupportDesc": "Direct access to helplines and support", "aboutModal": {"title": "About", "appDescription": "SobrixHealth is an app designed to help people in their recovery from addiction. It provides tools for tracking sobriety, mindfulness exercises, and access to resources.", "features": "Features", "sobrietyTracker": "Sobriety Tracker", "sobrietyTrackerDesc": "Track your progress and celebrate milestones", "mindfulness": "Mindfulness", "mindfulnessDesc": "Guided meditations and breathing exercises", "crisisSupport": "Crisis Support", "crisisSupportDesc": "Direct access to helplines and support", "helpSupport": "Help & Support", "helpDescription": "Need help? Contact us through one of the options below.", "email": "Email", "phone": "Phone", "liveChat": "Live Chat", "available247": "Available 24/7", "visitWebsite": "Visit Website", "faq": "Frequently Asked Questions", "copyright": "All rights reserved.", "madeWith": "Made with ❤️ for recovery and wellness"}, "helpModal": {"title": "Help & Support", "description": "Need help? Contact us through one of the options below.", "email": "Email", "emailDescription": "Send us an email", "phone": "Phone", "phoneDescription": "Call us directly", "liveChat": "Live Chat", "liveChatDescription": "Chat with our support team", "available247": "Available 24/7"}, "privacyModal": {"title": "Privacy Settings", "dataCollection": "Data Collection", "dataCollectionSection": "Data Collection", "dataCollectionDesc": "Allow us to collect anonymous usage data to improve the app", "anonymousAnalytics": "Anonymous Analytics", "anonymousAnalyticsDesc": "Help us improve the app by sharing anonymous analytics data", "crashReporting": "Crash Reports", "crashReportingDesc": "Automatically send crash reports to help fix bugs", "locationSecurity": "Location & Security", "locationTracking": "Location Tracking", "locationTrackingDesc": "Allow the app to use your location for features", "biometricAuth": "Biometric Authentication", "biometricAuthDesc": "Use fingerprint or face recognition to unlock the app", "appLock": "App Lock", "appLockDesc": "Lock the app when not in use"}, "notificationsModal": {"title": "Notification Settings", "dailyReminders": "Daily Reminders", "dailyCheckIn": "Daily Check-in", "dailyCheckInDesc": "Receive daily reminders to check in with your progress", "milestoneAlerts": "Milestone Alerts", "milestoneAlertsDesc": "Get alerted when you reach an important milestone", "emergencyAlerts": "Emergency Alerts", "emergencyAlertsDesc": "Receive alerts in emergency situations", "activities": "Activities", "journalReminders": "Journal Reminders", "journalRemindersDesc": "Reminders to update your journal", "mindfulnessReminders": "Mindfulness Reminders", "mindfulnessRemindersDesc": "Reminders for daily mindfulness exercises", "other": "Other", "weeklyReports": "Weekly Reports", "weeklyReportsDesc": "Receive weekly progress reports", "communityMessages": "Community Messages", "communityMessagesDesc": "Receive messages from the community"}}, "progress": {"viewModes": {"list": "List", "calendar": "Calendar", "chart": "Chart"}, "mood": {"mood": "<PERSON><PERSON>", "craving": "Craving", "triggers": "Triggers", "notes": "Notes", "date": "Date", "veryBad": "Very Bad", "bad": "Bad", "neutral": "Neutral", "good": "Good", "veryGood": "Very Good", "none": "None", "mild": "Mild", "moderate": "Moderate", "strong": "Strong", "veryStrong": "Very Strong", "chartExplanationTitle": "What does this mean?", "chartExplanationText": "These charts show how your mood and cravings change over time. Higher values for mood are better, while lower values for cravings are better."}, "health": {"sleep": "{{value}} hours of sleep", "water": "{{value}} ml water", "exercise": "{{value}} minutes of exercise", "mindfulness": "{{value}} minutes of mindfulness"}, "emptyStates": {"noMoodEntries": "No mood entries yet. Add your first one!", "noRelapses": "No relapses recorded. Stay strong!", "noHealthData": "No health data. Add your first health metric entry."}, "commonTriggers": ["Stress", "Negative emotions", "Social pressure", "Celebration", "Boredom", "Availability", "Conflict", "Cravings"]}, "contacts": {"categories": {"emergency": "Emergency", "family": "Family", "friends": "Friends", "work": "Work"}}, "crisis": {"emergencyPlans": {"emergencyPlansTitle": "Emergency Plans", "about": "About Emergency Plans", "aboutDescription": "Emergency plans help you prepare for crisis situations by outlining specific steps to take when you're struggling. Having a plan ready can make all the difference in maintaining your recovery.", "createNew": "Create New Plan", "importDocument": "Import Document", "myPlans": "My Emergency Plans", "noPlans": "No Emergency Plans", "noPlanDescription": "You haven't created any emergency plans yet. Create your first plan to be prepared for challenging moments.", "newPlan": "New Emergency Plan", "editPlan": "Edit Plan", "planTitle": "Title", "titlePlaceholder": "Enter title...", "content": "Content", "contentPlaceholder": "Enter plan content...", "enterTitle": "Please enter a title", "planSaved": "Emergency plan saved successfully!", "planDeleted": "Emergency plan deleted successfully!", "confirmDelete": "Are you sure you want to delete this emergency plan? This action cannot be undone.", "importedDocument": "Imported document - view file for details", "importError": "Could not import document", "defaultPlan": "# EMERGENCY PLAN\n\n## When I feel overwhelmed or have strong urges:\n\n1. **Stop and breathe**: Take 5 deep breaths\n\n2. **Call my support person**: \n   - [Name]: [Phone Number]\n   - Therapist: [Phone Number]\n\n3. **Remove myself from the situation**:\n   - Go to a safe place\n   - Leave the environment if there are triggers\n\n4. **Use distraction techniques**:\n   - Go for a walk\n   - Listen to calming music\n   - Do a mindfulness exercise\n\n5. **Remind myself of my reasons**:\n   - My health\n   - My relationships\n   - My future goals\n\n## Emergency Contacts:\n\n- **Crisis Hotline**: 988 or **************\n- **Sponsor**: [Name] - [Phone Number]\n- **Local Support Group**: [Phone Number]\n\n## Safe places to go:\n\n- Local library\n- Nearby park\n- [Other safe place]\n\n*Remember: This feeling is temporary. You are stronger than your urges.*"}, "relapsePlans": {"relapsePlansTitle": "Relapse Plans", "about": "About Relapse Prevention", "aboutDescription": "Relapse prevention plans help you identify triggers and develop coping strategies. These plans are essential tools in maintaining long-term recovery and building resilience.", "createNew": "Create New Plan", "importDocument": "Import Document", "myPlans": "My Relapse Plans", "noPlans": "No Relapse Plans", "noPlanDescription": "You haven't created any relapse prevention plans yet. Create your first plan to strengthen your recovery toolkit.", "newPlan": "New Relapse Plan", "editPlan": "Edit Plan", "planTitle": "Title", "titlePlaceholder": "Enter title...", "content": "Content", "contentPlaceholder": "Enter plan content...", "enterTitle": "Please enter a title", "planSaved": "Relapse plan saved successfully!", "planDeleted": "Relapse plan deleted successfully!", "confirmDelete": "Are you sure you want to delete this relapse plan? This action cannot be undone."}, "emergencyCards": {"newCard": "New Emergency Card", "newCardDescription": "Create a new emergency card with important information and contacts.", "editCard": "Edit Card", "editCardDescription": "Update your emergency card information.", "replaceCard": "Replace Card", "replaceCardDescription": "Choose how you want to replace this emergency card.", "view": "View", "replace": "Replace", "camera": "Camera", "photoLibrary": "Photo Library", "permissionRequired": "Permission Required", "permissionDescription": "We need access to your photo library to select images for your emergency cards.", "cameraPermissionRequired": "Camera Permission Required", "cameraPermissionDescription": "We need camera access to take photos for your emergency cards.", "couldNotTakePhoto": "Could not take photo", "couldNotSelectImage": "Could not select image", "confirmDelete": "Are you sure you want to delete this emergency card? This action cannot be undone."}}, "errors": {"generic": "An error occurred. Please try again.", "network": "Network error. Please check your connection.", "validation": "Please check your input and try again."}}