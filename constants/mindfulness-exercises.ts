import { MindfulnessExercise } from "@/types/mindfulness";

// Renamed from defaultMindfulnessExercises to BUILT_IN_EXERCISES to match import in mindfulness.tsx
export const BUILT_IN_EXERCISES: MindfulnessExercise[] = [
  {
    id: 'breathing-1',
    title: 'Box Breathing',
    description: 'A simple breathing technique to reduce stress and improve focus',
    type: 'breathing',
    steps: [
      'Find a comfortable seated position',
      'Inhale through your nose for 4 counts',
      'Hold your breath for 4 counts',
      'Exhale through your mouth for 4 counts',
      'Hold your breath for 4 counts',
      'Repeat for 5-10 minutes'
    ],
    duration: 5,
    isCustom: false,
    createdAt: new Date().toISOString()
  },
  {
    id: 'meditation-1',
    title: 'Mindful Awareness',
    description: 'A simple meditation to bring awareness to the present moment',
    type: 'meditation',
    steps: [
      'Find a comfortable seated position',
      'Close your eyes and take a few deep breaths',
      'Notice the sensations in your body',
      'When your mind wanders, gently bring it back to your breath',
      'Continue for 5-10 minutes'
    ],
    duration: 10,
    isCustom: false,
    createdAt: new Date().toISOString()
  },
  {
    id: 'bodyscan-1',
    title: 'Full Body Scan',
    description: 'A relaxing practice to release tension throughout your body',
    type: 'bodyscan',
    steps: [
      'Lie down in a comfortable position.',
      'Close your eyes... and take a few deep breaths.',
      'Now, bring your attention to your feet. Notice any sensations you feel there.',
      'Gently, begin to move your attention upwards... first, through your legs.',
      'Then, continue this awareness up through your torso.',
      'Now, bring your attention to your arms... all the way to your fingertips.',
      'And finally, scan upwards to your neck... and head.',
      'As you scan each part, notice any areas of tension. Consciously release them.',
      "If your mind wanders, that's perfectly okay. Gently guide your focus back... to the part of your body you are currently with.",
      'After scanning your entire body, take a moment. Feel your body as a whole... resting here.',
      "When you feel ready... slowly open your eyes."
    ],
    duration: 15,
    isCustom: false,
    createdAt: new Date().toISOString()
  },
  {
    id: 'grounding-1',
    title: '5-4-3-2-1 Grounding',
    description: 'A quick exercise to ground yourself during moments of anxiety',
    type: 'grounding',
    steps: [
      'Look around and name 5 things you can see',
      'Notice 4 things you can touch or feel',
      'Acknowledge 3 things you can hear',
      'Identify 2 things you can smell',
      'Recognize 1 thing you can taste',
      'Take a deep breath and notice how you feel'
    ],
    duration: 3,
    isCustom: false,
    createdAt: new Date().toISOString()
  },
  {
    id: 'visualization-1',
    title: 'Safe Place Visualization',
    description: "Visualize a safe, peaceful place to reduce stress and anxiety",
    type: 'visualization',
    steps: [
      'Find a comfortable position and close your eyes',
      'Take a few deep breaths to relax',
      'Imagine a place where you feel completely safe and at peace',
      'Notice the details - what you see, hear, smell, and feel',
      'Spend time exploring this place in your mind',
      'When ready, slowly bring your awareness back to the present'
    ],
    duration: 10,
    isCustom: false,
    createdAt: new Date().toISOString()
  },
  {
    id: 'gratitude-1',
    title: 'Gratitude Practice',
    description: 'Focus on things you are grateful for to shift perspective and improve mood',
    type: 'gratitude',
    steps: [
      'Find a quiet place to sit or lie down',
      'Take a few deep breaths to center yourself',
      'Think of 3 things you are grateful for today',
      'For each item, spend a moment really feeling the gratitude',
      'Notice how your body feels as you focus on gratitude',
      'Carry this feeling with you as you continue your day'
    ],
    duration: 5,
    isCustom: false,
    createdAt: new Date().toISOString()
  },
  {
    id: 'muscle-relaxation-1',
    title: 'Progressive Muscle Relaxation',
    description: 'Systematically tense and relax muscle groups to reduce physical tension',
    type: 'muscle-relaxation',
    steps: [
      'Lie down in a comfortable position',
      'Start with your feet - tense the muscles for 5 seconds, then release',
      'Move up to your calves, thighs, abdomen, chest, arms, and face',
      'For each muscle group, tense for 5 seconds, then release for 10 seconds',
      'Notice the difference between tension and relaxation',
      'After completing all muscle groups, notice how your body feels'
    ],
    duration: 15,
    isCustom: false,
    createdAt: new Date().toISOString()
  }
];

// Add the missing utility functions that are imported in mindfulness.tsx
export const getAllExercises = (customExercises: MindfulnessExercise[]): MindfulnessExercise[] => {
  return [...BUILT_IN_EXERCISES, ...customExercises];
};

export const getFavoriteExercises = (
  favoriteIds: string[], 
  customExercises: MindfulnessExercise[]
): MindfulnessExercise[] => {
  const allExercises = getAllExercises(customExercises);
  return allExercises.filter(exercise => favoriteIds.includes(exercise.id));
};

export const getExercisesByType = (
  type: string, 
  customExercises: MindfulnessExercise[]
): MindfulnessExercise[] => {
  const allExercises = getAllExercises(customExercises);
  return allExercises.filter(exercise => exercise.type === type);
};