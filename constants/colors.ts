const Colors = {
  light: {
    // Primary brand colors
    primary: '#6366F1',
    primaryLight: '#A5B4FC',
    primaryDark: '#4338CA',
    
    // Secondary colors
    secondary: '#EC4899',
    secondaryLight: '#F9A8D4',
    secondaryDark: '#BE185D',
    
    // Accent colors
    accent: '#06B6D4',
    accentLight: '#67E8F9',
    accentDark: '#0891B2',
    
    // Background colors
    background: '#FAFBFC',
    backgroundSecondary: '#F1F5F9',
    card: '#FFFFFF',
    cardSecondary: '#F8FAFC',
    cardGradient: '#FEFEFE',
    
    // Text colors
    text: '#0F172A',
    textSecondary: '#475569',
    textTertiary: '#64748B',
    
    // Border colors
    border: '#E2E8F0',
    borderLight: '#F1F5F9',
    borderDark: '#CBD5E1',
    
    // Status colors
    success: '#10B981',
    successLight: '#6EE7B7',
    successDark: '#047857',
    
    warning: '#F59E0B',
    warningLight: '#FCD34D',
    warningDark: '#D97706',
    
    danger: '#EF4444',
    dangerLight: '#FCA5A5',
    dangerDark: '#DC2626',
    
    info: '#3B82F6',
    infoLight: '#93C5FD',
    infoDark: '#1D4ED8',
    
    // Utility colors
    muted: '#94A3B8',
    mutedLight: '#CBD5E1',
    mutedDark: '#64748B',
    
    notification: '#EF4444',
    
    // Glassmorphism
    glass: 'rgba(255, 255, 255, 0.8)',
    glassSecondary: 'rgba(255, 255, 255, 0.6)',
    
    // Shadows
    shadow: 'rgba(0, 0, 0, 0.1)',
    shadowLight: 'rgba(0, 0, 0, 0.05)',
    shadowDark: 'rgba(0, 0, 0, 0.15)',
    
    gray: {
      50: '#F8FAFC',
      100: '#F1F5F9',
      200: '#E2E8F0',
      300: '#CBD5E1',
      400: '#94A3B8',
      500: '#64748B',
      600: '#475569',
      700: '#334155',
      800: '#1E293B',
      900: '#0F172A',
    },
  },
  dark: {
    // Primary brand colors
    primary: '#818CF8',
    primaryLight: '#C7D2FE',
    primaryDark: '#6366F1',
    
    // Secondary colors
    secondary: '#F472B6',
    secondaryLight: '#FBCFE8',
    secondaryDark: '#EC4899',
    
    // Accent colors
    accent: '#22D3EE',
    accentLight: '#A7F3D0',
    accentDark: '#06B6D4',
    
    // Background colors
    background: '#0F172A',
    backgroundSecondary: '#1E293B',
    card: '#1E293B',
    cardSecondary: '#334155',
    cardGradient: '#2D3748',
    
    // Text colors
    text: '#F8FAFC',
    textSecondary: '#CBD5E1',
    textTertiary: '#94A3B8',
    
    // Border colors
    border: '#334155',
    borderLight: '#475569',
    borderDark: '#1E293B',
    
    // Status colors
    success: '#34D399',
    successLight: '#6EE7B7',
    successDark: '#10B981',
    
    warning: '#FBBF24',
    warningLight: '#FCD34D',
    warningDark: '#F59E0B',
    
    danger: '#F87171',
    dangerLight: '#FCA5A5',
    dangerDark: '#EF4444',
    
    info: '#60A5FA',
    infoLight: '#93C5FD',
    infoDark: '#3B82F6',
    
    // Utility colors
    muted: '#64748B',
    mutedLight: '#94A3B8',
    mutedDark: '#475569',
    
    notification: '#F87171',
    
    // Glassmorphism
    glass: 'rgba(30, 41, 59, 0.8)',
    glassSecondary: 'rgba(30, 41, 59, 0.6)',
    
    // Shadows
    shadow: 'rgba(0, 0, 0, 0.3)',
    shadowLight: 'rgba(0, 0, 0, 0.2)',
    shadowDark: 'rgba(0, 0, 0, 0.4)',
    
    gray: {
      50: '#0F172A',
      100: '#1E293B',
      200: '#334155',
      300: '#475569',
      400: '#64748B',
      500: '#94A3B8',
      600: '#CBD5E1',
      700: '#E2E8F0',
      800: '#F1F5F9',
      900: '#F8FAFC',
    },
  },
};

export default Colors;