import { AlertCircle } from "lucide-react-native";

export const healthGoalOptions = [
  {
    id: "sleep",
    label: "Sleep",
    labelNL: "Slaap",
    icon: AlertCircle,
    unit: "hours",
    unitNL: "uren",
    inputType: "number" as const,
    placeholder: "e.g., 8",
    color: "#5B21B6",
  },
  {
    id: "exercise",
    label: "Exercise",
    labelNL: "Beweging",
    icon: AlertCircle,
    unit: "minutes",
    unitNL: "minuten",
    inputType: "number" as const,
    placeholder: "e.g., 30",
    color: "#16A34A",
  },
  {
    id: "pills",
    label: "Pills",
    labelNL: "Pillen",
    icon: AlertCircle,
    unit: "pills",
    unitNL: "pillen",
    inputType: "number" as const,
    placeholder: "e.g., 2",
    color: "#0EA5E9",
  },
  {
    id: "waterIntake",
    label: "Water Intake",
    labelNL: "Waterinname",
    icon: AlertCircle,
    unit: "glasses",
    unitNL: "glazen",
    inputType: "number" as const,
    placeholder: "e.g., 8",
    color: "#2563EB",
  },
];
