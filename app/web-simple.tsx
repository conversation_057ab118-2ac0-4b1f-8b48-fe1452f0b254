import React from 'react';
import { View, Text, StyleSheet, ScrollView, Pressable } from 'react-native';
import { useColorScheme } from '@/hooks';

export default function WebSimple() {
  const colors = useColorScheme();

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            SobrixHealth
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            Your comprehensive addiction recovery companion
          </Text>
        </View>

        {/* Hero Section */}
        <View style={styles.hero}>
          <Text style={[styles.heroTitle, { color: colors.text }]}>
            Take Control of Your Recovery Journey
          </Text>
          <Text style={[styles.heroDescription, { color: colors.textSecondary }]}>
            SobrixHealth provides the tools and support you need for successful addiction recovery.
            Track your progress, connect with support networks, and build healthy habits.
          </Text>
        </View>

        {/* Features Grid */}
        <View style={styles.featuresSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Key Features
          </Text>
          
          <View style={styles.featuresGrid}>
            <View style={[styles.featureCard, { backgroundColor: colors.surface }]}>
              <Text style={[styles.featureTitle, { color: colors.text }]}>
                Recovery Dashboard
              </Text>
              <Text style={[styles.featureDescription, { color: colors.textSecondary }]}>
                Track your progress and celebrate milestones in your recovery journey.
              </Text>
            </View>

            <View style={[styles.featureCard, { backgroundColor: colors.surface }]}>
              <Text style={[styles.featureTitle, { color: colors.text }]}>
                Health Metrics
              </Text>
              <Text style={[styles.featureDescription, { color: colors.textSecondary }]}>
                Monitor sleep, hydration, exercise, and medication adherence.
              </Text>
            </View>

            <View style={[styles.featureCard, { backgroundColor: colors.surface }]}>
              <Text style={[styles.featureTitle, { color: colors.text }]}>
                Crisis Support
              </Text>
              <Text style={[styles.featureDescription, { color: colors.textSecondary }]}>
                Access emergency contacts and crisis intervention resources.
              </Text>
            </View>

            <View style={[styles.featureCard, { backgroundColor: colors.surface }]}>
              <Text style={[styles.featureTitle, { color: colors.text }]}>
                Mindfulness Tools
              </Text>
              <Text style={[styles.featureDescription, { color: colors.textSecondary }]}>
                Practice meditation and mindfulness exercises for mental wellness.
              </Text>
            </View>
          </View>
        </View>

        {/* CTA Section */}
        <View style={styles.ctaSection}>
          <Text style={[styles.ctaTitle, { color: colors.text }]}>
            Ready to Start Your Recovery Journey?
          </Text>
          <Text style={[styles.ctaDescription, { color: colors.textSecondary }]}>
            Download SobrixHealth on your mobile device to get started.
          </Text>
          
          <View style={styles.downloadButtons}>
            <Pressable 
              style={[styles.downloadButton, { backgroundColor: colors.primary }]}
              onPress={() => console.log('Download for iOS')}
            >
              <Text style={[styles.downloadButtonText, { color: colors.background }]}>
                Download for iOS
              </Text>
            </Pressable>
            
            <Pressable 
              style={[styles.downloadButton, { backgroundColor: colors.primary }]}
              onPress={() => console.log('Download for Android')}
            >
              <Text style={[styles.downloadButtonText, { color: colors.background }]}>
                Download for Android
              </Text>
            </Pressable>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: colors.textSecondary }]}>
            © 2024 SobrixHealth. Supporting your recovery journey.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    maxWidth: 1200,
    marginHorizontal: 'auto',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 20,
    textAlign: 'center',
  },
  hero: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  heroTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  heroDescription: {
    fontSize: 18,
    textAlign: 'center',
    lineHeight: 28,
    maxWidth: 800,
  },
  featuresSection: {
    paddingVertical: 60,
  },
  sectionTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 40,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 20,
  },
  featureCard: {
    flex: 1,
    minWidth: 280,
    padding: 30,
    borderRadius: 12,
    marginBottom: 20,
  },
  featureTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  featureDescription: {
    fontSize: 16,
    lineHeight: 24,
  },
  ctaSection: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  ctaTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  ctaDescription: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 40,
    maxWidth: 600,
  },
  downloadButtons: {
    flexDirection: 'row',
    gap: 20,
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  downloadButton: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    minWidth: 180,
  },
  downloadButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 40,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    marginTop: 60,
  },
  footerText: {
    fontSize: 14,
  },
});
