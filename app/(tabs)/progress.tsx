import React from "react";
import { useState, useEffect, useMemo } from "react";
import { StyleSheet, View, Text, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useUserStore } from "@/store/user/user-store";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { useTabBarPadding } from "@/hooks/ui/useTabBarPadding";
import { useTranslation } from "@/hooks/useTranslation";
import { AlertTriangle } from "lucide-react-native";
import type { NestedTranslationKey, InterpolationValues } from "@/services/translation";

// Import components
import { ProgressTabs } from "@/components/progress/ProgressTabs";

// Error Boundary Component
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  colors: {
    background: string;
    danger: string;
    text: string;
    textSecondary: string;
  };
  t: (key: NestedTranslationKey, interpolation?: InterpolationValues) => string;
}

class ProgressErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Progress page error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={[styles.errorContainer, { backgroundColor: this.props.colors.background }]}>
          <AlertTriangle size={48} color={this.props.colors.danger} />
          <Text style={[styles.errorTitle, { color: this.props.colors.text }]}>
            Something went wrong
          </Text>
          <Text style={[styles.errorMessage, { color: this.props.colors.textSecondary }]}>
            Please try again or restart the app
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}

export default function ProgressScreen() {
  const { profile, isLoading } = useUserStore();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { paddingBottom } = useTabBarPadding();
  const { t } = useTranslation();
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize component state
  useEffect(() => {
    // Add a small delay to ensure smooth transition
    const timer = setTimeout(() => setIsInitialized(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Memoize colors and language to prevent unnecessary re-renders
  const memoizedColors = useMemo(() => colors, [currentTheme]);
  const language = useMemo(() => profile?.language || "en", [profile?.language]);

  // Loading state
  if (isLoading || !isInitialized) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          styles.centerContent,
          { backgroundColor: colors.background, paddingBottom },
        ]}
        edges={["top", "bottom"]}
      >
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading...
        </Text>
      </SafeAreaView>
    );
  }

  // No profile state
  if (!profile) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          styles.centerContent,
          { backgroundColor: colors.background, paddingBottom },
        ]}
        edges={["top", "bottom"]}
      >
        <AlertTriangle size={48} color={colors.warning} />
        <Text style={[styles.noProfileTitle, { color: colors.text }]}>
          No Profile Found
        </Text>
        <Text style={[styles.noProfileMessage, { color: colors.textSecondary }]}>
          Please complete onboarding first
        </Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor: colors.background, paddingBottom },
      ]}
      edges={["top", "bottom"]}
    >
      <ProgressErrorBoundary colors={memoizedColors} t={t}>
        <ProgressTabs colors={memoizedColors} language={language} />
      </ProgressErrorBoundary>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  centerContent: {
    alignItems: "center",
    justifyContent: "center",
  },
  container: {
    flex: 1,
  },
  errorContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    paddingHorizontal: 24,
  },
  errorMessage: {
    fontSize: 16,
    lineHeight: 24,
    marginTop: 8,
    textAlign: "center",
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginTop: 16,
    textAlign: "center",
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  noProfileMessage: {
    fontSize: 16,
    lineHeight: 24,
    marginTop: 8,
    textAlign: "center",
  },
  noProfileTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginTop: 16,
    textAlign: "center",
  },
});
