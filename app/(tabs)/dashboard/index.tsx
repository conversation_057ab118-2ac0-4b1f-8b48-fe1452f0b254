import React from "react";
import { useEffect, useState, useRef } from "react";
import {
  StyleSheet,
  Text,
  View,
  RefreshControl,
  TouchableOpacity,
  Animated,
  Pressable,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { useUserStore } from "@/store/user/user-store";
import { useTheme } from "@/context/theme-context";
import { Quote, useQuotesStore } from "@/store/content/quotes-store";
import Colors from "@/constants/colors";
import { Stack } from "expo-router";
import { 
  Menu, 
  Heart, 
  Target,
  ChevronRight,
  Sparkles,
  Moon,
  Activity,
  Droplets,
  Pill,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  RefreshCw,
  CreditCard,
  Users,
} from "lucide-react-native";
import { format } from "date-fns";
import { nl, enUS } from "date-fns/locale";
// Import components
import LogHealthModal from "@/components/health/LogHealthModal";
import { CircularView } from "@/components/dashboard/CircularView";
import { ProgressInsights } from "@/components/progress/shared/ProgressInsights";
import { useTranslation } from "@/hooks/useTranslation";
// Import dashboard utilities
import {
  generateHealthMetrics,
  type HealthMetric,
} from "@/utils/dashboard/dashboard-utils";
import { useRouter } from "expo-router";

export default function DashboardScreen() {
  const { profile } = useUserStore();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { quotes, fetchQuotes } = useQuotesStore();
  const { t } = useTranslation();
  const router = useRouter();
  const [refreshing, setRefreshing] = useState(false);
  const [randomQuote, setRandomQuote] = useState<Quote | null>(null);
  const [isLogHealthModalVisible, setIsLogHealthModalVisible] = useState(false);
  const [healthMetricsData, setHealthMetricsData] = useState<HealthMetric[]>([]);
  
  // Animation values
  const scrollY = useRef(new Animated.Value(0)).current;
  const heroScale = useRef(new Animated.Value(0.95)).current;
  const heroOpacity = useRef(new Animated.Value(0)).current;
  const cardAnimations = useRef([...Array(4)].map(() => ({
    opacity: new Animated.Value(0),
    translateY: new Animated.Value(30),
  }))).current;

  // Calculate sobriety data and savings
  const calculateSobrietyData = () => {
    if (!profile || !profile.sobrietyDate) {
      return { diffDays: 0, savedAmount: 0 };
    }
    const sobrietyDate = new Date(profile.sobrietyDate);
    const currentDate = new Date();
    const diffTime = currentDate.getTime() - sobrietyDate.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    let savedAmount = 0;
    if (profile.usageCost && profile.usageCost > 0) {
      if (profile.costFrequency === "daily") {
        savedAmount = profile.usageCost * diffDays;
      } else if (profile.costFrequency === "weekly") {
        savedAmount = profile.usageCost * (diffDays / 7);
      } else if (profile.costFrequency === "monthly") {
        savedAmount = profile.usageCost * (diffDays / 30);
      }
    }
    return { diffDays, savedAmount };
  };

  const { diffDays, savedAmount } = calculateSobrietyData();

  // Get next milestone
  const getNextMilestone = (days: number) => {
    const milestones = [
      { days: 1, name: profile?.language === "nl" ? "1 Dag" : "1 Day" },
      { days: 7, name: profile?.language === "nl" ? "1 Week" : "1 Week" },
      { days: 30, name: profile?.language === "nl" ? "1 Maand" : "1 Month" },
      { days: 90, name: profile?.language === "nl" ? "3 Maanden" : "3 Months" },
      { days: 180, name: profile?.language === "nl" ? "6 Maanden" : "6 Months" },
      { days: 365, name: profile?.language === "nl" ? "1 Jaar" : "1 Year" },
      { days: 730, name: profile?.language === "nl" ? "2 Jaar" : "2 Years" },
      { days: 1095, name: profile?.language === "nl" ? "3 Jaar" : "3 Years" },
    ];
    return (
      milestones.find((milestone) => days < milestone.days) || {
        days: milestones[milestones.length - 1].days + 365,
        name: profile?.language === "nl" ? "Volgende Mijlpaal" : "Next Milestone",
      }
    );
  };

  const nextMilestone = getNextMilestone(diffDays);
  const milestoneProgress = Math.min((diffDays / nextMilestone.days) * 100, 100);

  // Get time-based greeting
  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    
    if (hour < 12) {
      return t("dashboard.greetings.morning");
    } else if (hour < 17) {
      return t("dashboard.greetings.afternoon");
    } else {
      return t("dashboard.greetings.evening");
    }
  };

  // Get formatted date and time
  const getCurrentDateTime = () => {
    const now = new Date();
    const locale = profile?.language === "nl" ? nl : enUS;
    
    const time = format(now, "HH:mm", { locale });
    const date = format(now, "EEEE, d MMMM", { locale });
    
    return { time, date };
  };

  const timeBasedGreeting = getTimeBasedGreeting();
  const { time, date } = getCurrentDateTime();

  // Animate on mount
  useEffect(() => {
    // Hero animation
    Animated.parallel([
      Animated.timing(heroOpacity, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(heroScale, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();

    // Stagger card animations
    const staggerDelay = 100;
    cardAnimations.forEach((anim, index) => {
      setTimeout(() => {
        Animated.parallel([
          Animated.timing(anim.opacity, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.spring(anim.translateY, {
            toValue: 0,
            friction: 8,
            tension: 40,
            useNativeDriver: true,
          }),
        ]).start();
      }, index * staggerDelay);
    });
  }, [cardAnimations, heroOpacity, heroScale]);

  // Get a random quote
  useEffect(() => {
    if (quotes.length > 0) {
      const randomIndex = Math.floor(Math.random() * quotes.length);
      setRandomQuote(quotes[randomIndex]);
    }
  }, [quotes]);

  // Function to fetch health data
  const fetchHealthData = React.useCallback(async () => {
    if (profile) {
      const todayString = format(new Date(), "yyyy-MM-dd");
      try {
        const metrics = await generateHealthMetrics(profile, todayString);
        setHealthMetricsData(metrics);
      } catch (_error) {
        console.error("Failed to fetch health metrics:", _error);
        setHealthMetricsData([]);
      }
    }
  }, [profile]);

  useEffect(() => {
    fetchHealthData();
  }, [fetchHealthData, isLogHealthModalVisible]);

  const handleHealthDataSaved = () => {
    fetchHealthData();
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchQuotes();
    await fetchHealthData();
    if (quotes.length > 0) {
      const randomIndex = Math.floor(Math.random() * quotes.length);
      setRandomQuote(quotes[randomIndex]);
    }
    setRefreshing(false);
  };

  if (!profile) {
    return null;
  }

  const formatCurrency = (amount: number) => {
    const currencySymbol = profile.currency === "EUR" ? "€" : "$";
    return `${currencySymbol}${amount.toFixed(2)}`;
  };

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const quickActions = [
    {
      icon: 'alert-triangle',
      label: t('dashboard.emergency'),
      onPress: () => router.push('/(tabs)/crisis'),
      color: '#FF6B6B',
    },
    {
      icon: 'heart',
      label: t('dashboard.emergencyPlan'),
      onPress: () => router.push('/emergency-plan'),
      color: '#4ECDC4',
    },
    {
      icon: 'refresh-cw',
      label: t('dashboard.relapsePlan'),
      onPress: () => router.push('/relapse-plan'),
      color: '#45B7D1',
    },
    {
      icon: 'credit-card',
      label: t('dashboard.emergencyCard'),
      onPress: () => router.push('/emergency-card'),
      color: '#96CEB4',
    },
    {
      icon: 'users',
      label: t('dashboard.emergencyContacts'),
      onPress: () => router.push('/(tabs)/contacts'),
      color: '#FFEAA7',
    },
  ];

  // Helper function to render icon components
  const renderIcon = (iconName: string, color: string, size = 24) => {
    const iconProps = { size, color, strokeWidth: 2.5 };
    
    switch (iconName) {
      case 'alert-triangle':
        return <AlertTriangle {...iconProps} />;
      case 'heart':
        return <Heart {...iconProps} />;
      case 'refresh-cw':
        return <RefreshCw {...iconProps} />;
      case 'credit-card':
        return <CreditCard {...iconProps} />;
      case 'users':
        return <Users {...iconProps} />;
      default:
        return <AlertTriangle {...iconProps} />;
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={["top", "bottom"]}>
      <Stack.Screen
        options={{
          title: "",
          headerTransparent: true,
          headerBackground: () => (
            <Animated.View style={[styles.headerBackground, { opacity: headerOpacity }]}>
              <BlurView intensity={80} tint={currentTheme} style={StyleSheet.absoluteFillObject} />
            </Animated.View>
          ),
          headerLeft: () => (
            <TouchableOpacity style={styles.headerButton}>
              <Menu size={24} color={colors.text} />
            </TouchableOpacity>
          ),
        }}
      />

      <Animated.ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor={colors.primary} />
        }
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: true }
        )}
        scrollEventThrottle={16}
      >
        {/* Hero Section - Conditional rendering based on user preference */}
        {profile.dashboardView === "circular" ? (
          <CircularView
            diffDays={diffDays}
            sobrietyDate={profile.sobrietyDate ? new Date(profile.sobrietyDate) : new Date()}
            nextMilestone={nextMilestone}
            savedAmount={savedAmount}
            usageCost={profile.usageCost || 0}
            _costFrequency={profile.costFrequency || "daily"}
            language={profile.language || "en"}
            currency={profile.currency || "USD"}
            colors={colors}
            usageAmount={profile.usageAmount}
            _substanceType={profile.substanceType}
          />
        ) : (
          <Animated.View
            style={[
              styles.heroContainer,
              {
                opacity: heroOpacity,
                transform: [{ scale: heroScale }],
              },
            ]}
          >
            <LinearGradient
              colors={[colors.primary, colors.primaryDark, colors.primary + '90']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.heroGradient}
            >
              {/* Background Pattern */}
              <View style={styles.heroPattern}>
                <View style={[styles.patternCircle, styles.patternCircle1]} />
                <View style={[styles.patternCircle, styles.patternCircle2]} />
                <View style={[styles.patternCircle, styles.patternCircle3]} />
              </View>

              <View style={styles.heroContent}>
                {/* Greeting Section */}
                <View style={styles.heroGreetingSection}>
                  <View style={styles.heroGreetingHeader}>
                    <Text style={styles.heroGreeting}>
                      {timeBasedGreeting}
                    </Text>
                    <Text style={styles.heroTime}>{time}</Text>
                  </View>
                  <Text style={styles.heroName}>{profile.name}</Text>
                  <Text style={styles.heroDate}>{date}</Text>
                </View>

                {/* Main Stats Grid */}
                <View style={styles.heroStatsGrid}>
                  {/* Days Sober - Main Focus */}
                  <View style={styles.heroMainStat}>
                    <View style={styles.heroMainStatIcon}>
                      <Target size={28} color="#fff" />
                    </View>
                    <View style={styles.heroMainStatContent}>
                      <Text style={styles.heroMainStatNumber}>{diffDays}</Text>
                      <Text style={styles.heroMainStatLabel}>
                        {t("dashboard.hero.daysSober")}
                      </Text>
                    </View>
                  </View>

                  {/* Secondary Stats */}
                  <View style={styles.heroSecondaryStats}>
                    {savedAmount > 0 && (
                      <View style={styles.heroSecondaryStat}>
                        <View style={styles.heroSecondaryStatIcon}>
                          <DollarSign size={20} color="#fff" />
                        </View>
                        <View style={styles.heroSecondaryStatContent}>
                          <Text style={styles.heroSecondaryStatNumber}>
                            {formatCurrency(savedAmount)}
                          </Text>
                          <Text style={styles.heroSecondaryStatLabel}>
                            {t("dashboard.hero.saved")}
                          </Text>
                        </View>
                      </View>
                    )}
                    
                    <View style={styles.heroSecondaryStat}>
                      <View style={styles.heroSecondaryStatIcon}>
                        <TrendingUp size={20} color="#fff" />
                      </View>
                      <View style={styles.heroSecondaryStatContent}>
                        <Text style={styles.heroSecondaryStatNumber}>
                          {milestoneProgress.toFixed(0)}%
                        </Text>
                        <Text style={styles.heroSecondaryStatLabel}>
                          {t("dashboard.hero.toGoal")}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
                
                {/* Enhanced Milestone Progress */}
                <View style={styles.milestoneContainer}>
                  <View style={styles.milestoneHeader}>
                    <View style={styles.milestoneIconContainer}>
                      <Target size={18} color="#fff" />
                    </View>
                    <View style={styles.milestoneTextContainer}>
                      <Text style={styles.milestoneTitle}>
                        {t("dashboard.hero.nextMilestone")}
                      </Text>
                      <Text style={styles.milestoneSubtitle}>
                        {nextMilestone.name}
                      </Text>
                    </View>
                    <View style={styles.milestonePercentage}>
                      <Text style={styles.milestonePercentageText}>
                        {milestoneProgress.toFixed(0)}%
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.progressBarContainer}>
                    <View style={styles.progressBarBackground} />
                    <Animated.View
                      style={[
                        styles.progressBarFill,
                        { width: `${milestoneProgress}%` },
                      ]}
                    />
                    <View style={styles.progressBarGlow} />
                  </View>
                  
                  {/* Days remaining */}
                  <View style={styles.milestoneFooter}>
                    <Text style={styles.milestoneDaysRemaining}>
                      {nextMilestone.days - diffDays} {t("dashboard.hero.daysToGo")}
                    </Text>
                  </View>
                </View>
              </View>
            </LinearGradient>
          </Animated.View>
        )}

        {/* Progress Insights */}
        <Animated.View
          style={{
            opacity: cardAnimations[0].opacity,
            transform: [{ translateY: cardAnimations[0].translateY }],
          }}
        >
          <ProgressInsights
            profile={profile}
            colors={colors}
            language={profile.language || "en"}
          />
        </Animated.View>

        {/* Quick Actions */}
        <Animated.View
          style={[
            styles.sectionContainer,
            {
              opacity: cardAnimations[1].opacity,
              transform: [{ translateY: cardAnimations[1].translateY }],
            },
          ]}
        >
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {t('dashboard.quickActions')}
            </Text>
          </View>
          
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.quickActionButton,
                  { backgroundColor: colors.card, borderColor: colors.border }
                ]}
                onPress={action.onPress}
                activeOpacity={0.7}
              >
                <View style={[
                  styles.quickActionIcon,
                  { backgroundColor: action.color + '20' }
                ]}>
                  {renderIcon(action.icon, action.color, 28)}
                </View>
                <Text style={[styles.quickActionLabel, { color: colors.text }]} numberOfLines={2}>
                  {action.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Today's Focus */}
        <Animated.View
          style={[
            styles.sectionContainer,
            {
              opacity: cardAnimations[1].opacity,
              transform: [{ translateY: cardAnimations[1].translateY }],
            },
          ]}
        >
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {t("dashboard.todaysFocus")}
            </Text>
          </View>

          {/* Health Card */}
          {profile.showHealthMetrics && (
            <Pressable
              style={({ pressed }) => [
                styles.card,
                { 
                  backgroundColor: colors.card,
                  transform: [{ scale: pressed ? 0.98 : 1 }],
                },
              ]}
              onPress={() => setIsLogHealthModalVisible(true)}
            >
              <LinearGradient
                colors={[colors.success + '10', colors.success + '05']}
                style={styles.cardGradient}
              >
                <View style={styles.cardHeader}>
                  <View style={[styles.cardIcon, { backgroundColor: colors.success + '20' }]}>
                    <Heart size={24} color={colors.success} />
                  </View>
                  <View style={styles.cardHeaderText}>
                    <Text style={[styles.cardTitle, { color: colors.text }]}>
                      {t("dashboard.todaysHealth")}
                    </Text>
                    <Text style={[styles.cardSubtitle, { color: colors.textSecondary }]}>
                      {healthMetricsData.length > 0
                        ? `${healthMetricsData.length} ${t("dashboard.goalsTracked")}`
                        : t("dashboard.tapToGetStarted")
                      }
                    </Text>
                  </View>
                  <ChevronRight size={20} color={colors.textSecondary} />
                </View>
                
                {/* Health Metrics Preview */}
                {healthMetricsData.length > 0 && (
                  <View style={styles.healthMetricsGrid}>
                    {healthMetricsData.map((metric) => {
                      const progress = metric.maxValue ? (metric.value / metric.maxValue) * 100 : 0;
                      const getMetricIcon = () => {
                        switch (metric.type) {
                          case 'sleep': return <Moon size={16} color={colors.primary} />;
                          case 'exercise': return <Activity size={16} color={colors.warning} />;
                          case 'hydration': return <Droplets size={16} color={colors.info} />;
                          case 'pills': return <Pill size={16} color={colors.secondary} />;
                          default: return <Activity size={16} color={colors.muted} />;
                        }
                      };

                      const getMetricColor = () => {
                        switch (metric.type) {
                          case 'sleep': return colors.primary;
                          case 'exercise': return colors.warning;
                          case 'hydration': return colors.info;
                          case 'pills': return colors.secondary;
                          default: return colors.muted;
                        }
                      };
                      
                      return (
                        <View key={metric.id} style={[styles.metricCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
                          <View style={styles.metricCardHeader}>
                            <View style={[styles.metricCardIcon, { backgroundColor: getMetricColor() + '20' }]}>
                              {getMetricIcon()}
                            </View>
                            <Text style={[styles.metricCardValue, { color: colors.text }]}>
                              {metric.value}
                            </Text>
                          </View>
                          <Text style={[styles.metricCardUnit, { color: colors.textSecondary }]}>
                            {metric.unit}
                          </Text>
                          <View style={styles.metricCardProgressContainer}>
                            <View style={[styles.metricCardProgressBackground, { backgroundColor: colors.border }]} />
                            <View 
                              style={[
                                styles.metricCardProgressFill, 
                                { 
                                  backgroundColor: getMetricColor(),
                                  width: `${Math.min(progress, 100)}%`
                                }
                              ]} 
                            />
                          </View>
                        </View>
                      );
                    })}
                  </View>
                )}
                
                {/* Empty State */}
                {healthMetricsData.length === 0 && (
                  <View style={styles.healthEmptyState}>
                    <View style={[styles.healthEmptyIconContainer, { backgroundColor: colors.primary + '20' }]}>
                      <Activity size={24} color={colors.primary} />
                    </View>
                    <Text style={[styles.healthEmptyText, { color: colors.textSecondary }]}>
                      {t("dashboard.startTracking")}
                    </Text>
                  </View>
                )}
              </LinearGradient>
            </Pressable>
          )}

          {/* Quote Card */}
          {randomQuote && (
            <Animated.View
              style={[
                styles.quoteCard,
                { backgroundColor: colors.card },
                {
                  opacity: cardAnimations[2].opacity,
                  transform: [{ translateY: cardAnimations[2].translateY }],
                },
              ]}
            >
              <Sparkles size={20} color={colors.primary} style={styles.quoteIcon} />
              <Text style={[styles.quoteText, { color: colors.text }]}>
                {randomQuote.text}
              </Text>
              <Text style={[styles.quoteAuthor, { color: colors.textSecondary }]}>
                — {randomQuote.author}
              </Text>
            </Animated.View>
          )}
        </Animated.View>
      </Animated.ScrollView>

      <LogHealthModal
        visible={isLogHealthModalVisible}
        onClose={() => setIsLogHealthModalVisible(false)}
        onDataSaved={handleHealthDataSaved}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 20,
    elevation: 3,
    marginBottom: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  cardGradient: {
    padding: 20,
  },
  cardHeader: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  cardHeaderText: {
    flex: 1,
    marginLeft: 16,
  },
  cardIcon: {
    alignItems: 'center',
    borderRadius: 16,
    height: 48,
    justifyContent: 'center',
    width: 48,
  },
  cardSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 100,
  },
  headerBackground: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'transparent',
  },
  headerButton: {
    padding: 8,
  },
  // Health Metrics Styles
  healthEmptyIconContainer: {
    alignItems: 'center',
    borderRadius: 16,
    height: 48,
    justifyContent: 'center',
    marginBottom: 12,
    width: 48,
  },
  healthEmptyState: {
    alignItems: 'center',
    marginTop: 16,
    paddingVertical: 20,
  },
  healthEmptyText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
  healthMetricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 16,
  },
  heroContainer: {
    marginBottom: 24,
    marginHorizontal: 20,
    marginTop: 60,
  },
  heroContent: {
    padding: 28,
    position: 'relative',
    zIndex: 2,
  },
  heroDate: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontWeight: '400',
    marginTop: 4,
  },
  heroGradient: {
    borderRadius: 28,
    overflow: 'hidden',
    position: 'relative',
  },
  heroGreeting: {
    color: 'rgba(255, 255, 255, 0.85)',
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  heroGreetingHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  heroGreetingSection: {
    marginBottom: 28,
  },
  heroMainStat: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 20,
    flexDirection: 'row',
    marginBottom: 20,
    padding: 20,
  },
  heroMainStatContent: {
    flex: 1,
    marginLeft: 16,
  },
  heroMainStatIcon: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 16,
    height: 56,
    justifyContent: 'center',
    width: 56,
  },
  heroMainStatLabel: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
    fontWeight: '500',
    marginTop: 4,
  },
  heroMainStatNumber: {
    color: '#fff',
    fontSize: 42,
    fontWeight: '800',
    lineHeight: 48,
  },
  heroName: {
    color: '#fff',
    fontSize: 24,
    fontWeight: '700',
    marginTop: 4,
  },
  heroPattern: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1,
  },
  heroSecondaryStat: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    flex: 1,
    flexDirection: 'row',
    marginHorizontal: 4,
    padding: 16,
  },
  heroSecondaryStatContent: {
    flex: 1,
    marginLeft: 12,
  },
  heroSecondaryStatIcon: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    height: 40,
    justifyContent: 'center',
    width: 40,
  },
  heroSecondaryStatLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
  },
  heroSecondaryStatNumber: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
  },
  heroSecondaryStats: {
    flexDirection: 'row',
    gap: 8,
  },
  heroStatsGrid: {
    marginBottom: 24,
  },
  heroTime: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  metricCard: {
    borderRadius: 12,
    borderWidth: 1,
    elevation: 2,
    flex: 1,
    minWidth: '45%',
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  metricCardHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 4,
  },
  metricCardIcon: {
    alignItems: 'center',
    borderRadius: 8,
    height: 24,
    justifyContent: 'center',
    marginRight: 8,
    width: 24,
  },
  metricCardProgressBackground: {
    borderRadius: 2,
    height: 4,
    position: 'absolute',
    width: '100%',
  },
  metricCardProgressContainer: {
    height: 4,
    marginTop: 8,
    position: 'relative',
  },
  metricCardProgressFill: {
    borderRadius: 2,
    height: 4,
  },
  metricCardUnit: {
    fontSize: 11,
    fontWeight: '500',
  },
  metricCardValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  milestoneContainer: {
    marginTop: 8,
  },
  milestoneDaysRemaining: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  milestoneFooter: {
    marginTop: 8,
  },
  milestoneHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 12,
  },
  milestoneIconContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    height: 36,
    justifyContent: 'center',
    width: 36,
  },
  milestonePercentage: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    justifyContent: 'center',
    minWidth: 50,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  milestonePercentageText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '700',
  },
  milestoneSubtitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
  },
  milestoneTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  milestoneTitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    fontWeight: '500',
  },
  patternCircle: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 50,
    position: 'absolute',
  },
  patternCircle1: {
    height: 100,
    right: -20,
    top: -30,
    width: 100,
  },
  patternCircle2: {
    bottom: -40,
    height: 80,
    left: -10,
    width: 80,
  },
  patternCircle3: {
    height: 60,
    right: 30,
    top: 120,
    width: 60,
  },
  progressBarBackground: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 4,
    height: 8,
    position: 'absolute',
    width: '100%',
  },
  progressBarContainer: {
    height: 8,
    position: 'relative',
  },
  progressBarFill: {
    backgroundColor: '#fff',
    borderRadius: 4,
    height: 8,
    shadowColor: '#fff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
  },
  progressBarGlow: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    height: 8,
    position: 'absolute',
    width: '100%',
  },
  quoteAuthor: {
    fontSize: 14,
    fontStyle: 'italic',
    marginTop: 8,
    textAlign: 'right',
  },
  quoteCard: {
    borderRadius: 20,
    elevation: 3,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  quoteIcon: {
    marginBottom: 12,
  },
  quoteText: {
    fontSize: 16,
    fontStyle: 'italic',
    lineHeight: 24,
  },
  sectionContainer: {
    marginBottom: 8,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    justifyContent: 'space-between',
  },
  quickActionButton: {
    alignItems: 'center',
    borderRadius: 16,
    borderWidth: 1,
    elevation: 2,
    flex: 1,
    minWidth: '45%',
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  quickActionIcon: {
    alignItems: 'center',
    borderRadius: 20,
    height: 56,
    justifyContent: 'center',
    marginBottom: 8,
    width: 56,
  },
  quickActionLabel: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});