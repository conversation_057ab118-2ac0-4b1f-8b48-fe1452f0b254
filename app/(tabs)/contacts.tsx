import React from "react";
import { useState, useCallback } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Modal,
  Platform,
  Alert,
  ScrollView,
  TextInput,
  RefreshControl,
  Animated,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import { Stack } from "expo-router";
import { useUserStore } from "@/store/user/user-store";
import { EmergencyContact } from "@/types/user";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { ContactForm } from "@/components/contacts/ContactForm";
import { ContactItem } from "@/components/contacts/ContactItem";
import { ContactImport } from "@/components/contacts/ContactImport";
import {
  Plus,
  Users,
  AlertTriangle,
  UserPlus,
  Users2,
  Briefcase,
  Search,
  X,
  Download,
  Filter,
} from "lucide-react-native";
import * as Haptics from "expo-haptics";
import { Linking } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
export default function ContactsScreen() {
  const { profile, removeEmergencyContact } = useUserStore();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const insets = useSafeAreaInsets();
  const [showContactForm, setShowContactForm] = useState(false);
  const [showImportContacts, setShowImportContacts] = useState(false);
  const [selectedContact, setSelectedContact] = useState<EmergencyContact | null>(null);
  const [activeCategory, setActiveCategory] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");
  const [showSearch, setShowSearch] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [sortOrder, setSortOrder] = useState<"name" | "date">("name");
  const [showSortOptions, setShowSortOptions] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  // Animation values
  const searchHeight = new Animated.Value(0);
  const sortOptionsHeight = new Animated.Value(0);
  // Move useCallback to top level to avoid conditional hook call
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    // Simulate a refresh
    setTimeout(() => {
      setRefreshing(false);
      if (Platform.OS !== "web") {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    }, 1000);
  }, []);
  if (!profile) {
    return null;
  }
  const categories = [
    {
      id: "All",
      label: profile.language === "nl" ? "Alle" : "All",
      icon: Users,
      color: colors.primary,
    },
    {
      id: "Emergency",
      label: profile.language === "nl" ? "Nood" : "Emergency",
      icon: AlertTriangle,
      color: colors.danger,
    },
    {
      id: "Family",
      label: profile.language === "nl" ? "Familie" : "Family",
      icon: Users2,
      color: colors.info,
    },
    {
      id: "Friends",
      label: profile.language === "nl" ? "Vrienden" : "Friends",
      icon: UserPlus,
      color: colors.success,
    },
    {
      id: "Work",
      label: profile.language === "nl" ? "Werk" : "Work",
      icon: Briefcase,
      color: colors.warning,
    },
  ];
  // Filter contacts based on category and search query
  const filteredContacts = (profile.emergencyContacts || [])
    .filter((contact) => {
      const matchesCategory =
        activeCategory === "All" || contact.category === activeCategory;
      const matchesSearch =
        searchQuery === "" ||
        contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        contact.phone.includes(searchQuery) ||
        (contact.email &&
          contact.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (contact.relationship &&
          contact.relationship
            .toLowerCase()
            .includes(searchQuery.toLowerCase()));
      return matchesCategory && matchesSearch;
    })
    .sort((a, b) => {
      if (sortOrder === "name") {
        return a.name.localeCompare(b.name);
      } else {
        // Sort by date (newest first)
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      }
    });
  const handleAddContact = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setSelectedContact(null);
    setShowContactForm(true);
  };
  const handleImportContacts = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    setShowImportContacts(true);
  };
  const handleEditContact = (contact: EmergencyContact) => {
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
    setSelectedContact(contact);
    setShowContactForm(true);
  };
  const handleDeleteContact = (id: string) => {
    if (Platform.OS !== "web") {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    }
    const confirmMessage =
      profile.language === "nl"
        ? "Weet je zeker dat je dit contact wilt verwijderen?"
        : "Are you sure you want to delete this contact?";
    if (Platform.OS === "web") {
      if (window.confirm(confirmMessage)) {
        setIsDeleting(true);
        setTimeout(() => {
          removeEmergencyContact(id);
          setIsDeleting(false);
        }, 300);
      }
    } else {
      Alert.alert(
        profile.language === "nl" ? "Bevestig verwijderen" : "Confirm Deletion",
        confirmMessage,
        [
          {
            text: profile.language === "nl" ? "Annuleren" : "Cancel",
            style: "cancel",
          },
          {
            text: profile.language === "nl" ? "Verwijderen" : "Delete",
            onPress: () => {
              setIsDeleting(true);
              setTimeout(() => {
                removeEmergencyContact(id);
                setIsDeleting(false);
              }, 300);
            },
            style: "destructive",
          },
        ]
      );
    }
  };
  const handleCallContact = (phone: string) => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    const phoneUrl = `tel:${phone}`;
    Linking.canOpenURL(phoneUrl)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(phoneUrl);
        } else {
          console.log("Phone calls not supported");
          // Show alert or toast that phone calls are not supported
          Alert.alert(
            profile.language === "nl" ? "Niet ondersteund" : "Not Supported",
            profile.language === "nl"
              ? "Telefoongesprekken worden niet ondersteund op dit apparaat."
              : "Phone calls are not supported on this device."
          );
        }
      })
      .catch((_err) => console.error("Error opening phone app:", _err));
  };
  const toggleSearch = () => {
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
    if (showSearch) {
      // Hide search bar
      setSearchQuery("");
      Animated.timing(searchHeight, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start(() => setShowSearch(false));
    } else {
      // Show search bar
      setShowSearch(true);
      Animated.timing(searchHeight, {
        toValue: 50,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  };
  const toggleSortOptions = () => {
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
    if (showSortOptions) {
      // Hide sort options
      Animated.timing(sortOptionsHeight, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start(() => setShowSortOptions(false));
    } else {
      // Show sort options
      setShowSortOptions(true);
      Animated.timing(sortOptionsHeight, {
        toValue: 100,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  };
  const renderHeader = () => (
    <>
      <View style={styles.headerContainer}>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: colors.card }]}
            onPress={toggleSearch}
          >
            {showSearch ? (
              <X size={20} color={colors.text} />
            ) : (
              <Search size={20} color={colors.text} />
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: colors.card }]}
            onPress={toggleSortOptions}
          >
            <Filter size={20} color={colors.text} />
          </TouchableOpacity>
          {Platform.OS !== "web" && (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: colors.card }]}
              onPress={handleImportContacts}
            >
              <Download size={20} color={colors.text} />
            </TouchableOpacity>
          )}
        </View>
      </View>
      {showSearch && (
        <Animated.View
          style={[styles.searchContainer, { height: searchHeight }]}
        >
          <View
            style={[
              styles.searchInputContainer,
              { backgroundColor: colors.card, borderColor: colors.border },
            ]}
          >
            <Search size={18} color={colors.muted} style={styles.searchIcon} />
            <TextInput
              style={[styles.searchInput, { color: colors.text }]}
              placeholder={
                profile.language === "nl" ? "Zoeken..." : "Search..."
              }
              placeholderTextColor={colors.muted}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery("")}>
                <X size={18} color={colors.muted} />
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>
      )}
      {showSortOptions && (
        <Animated.View
          style={[styles.sortOptionsContainer, { height: sortOptionsHeight }]}
        >
          <Text style={[styles.sortTitle, { color: colors.text }]}>
            {profile.language === "nl" ? "Sorteren op:" : "Sort by:"}
          </Text>
          <View style={styles.sortButtons}>
            <TouchableOpacity
              style={[
                styles.sortButton,
                sortOrder === "name" && { backgroundColor: colors.primary },
              ]}
              onPress={() => {
                setSortOrder("name");
                if (Platform.OS !== "web") {
                  Haptics.selectionAsync();
                }
              }}
            >
              <Text
                style={[
                  sortOrder === "name" ? styles.sortButtonTextActive : styles.sortButtonTextInactive,
                  sortOrder === "name" ? styles.sortButtonTextActiveColor : { color: colors.text },
                ]}
              >
                {profile.language === "nl" ? "Naam" : "Name"}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.sortButton,
                sortOrder === "date" && { backgroundColor: colors.primary },
              ]}
              onPress={() => {
                setSortOrder("date");
                if (Platform.OS !== "web") {
                  Haptics.selectionAsync();
                }
              }}
            >
              <Text
                style={[
                  sortOrder === "date" ? styles.sortButtonTextActive : styles.sortButtonTextInactive,
                  sortOrder === "date" ? styles.sortButtonTextActiveColor : { color: colors.text },
                ]}
              >
                {profile.language === "nl"
                  ? "Recent toegevoegd"
                  : "Recently Added"}
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}
      <View style={styles.categoriesWrapper}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesContainer}
        >
          {categories.map((category) => {
            const isActive = activeCategory === category.id;
            return (
              <TouchableOpacity
                key={category.id}
                onPress={() => {
                  setActiveCategory(category.id);
                  if (Platform.OS !== "web") {
                    Haptics.selectionAsync();
                  }
                }}
                style={styles.categoryButtonWrapper}
              >
                {isActive ? (
                  <LinearGradient
                    colors={[category.color, category.color + "CC"]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.categoryButton}
                  >
                    <category.icon
                      size={18}
                      color="#fff"
                      style={styles.categoryIcon}
                    />
                    <Text style={styles.categoryTextActive}>
                      {category.label}
                    </Text>
                  </LinearGradient>
                ) : (
                  <View
                    style={[
                      styles.categoryButtonInactive,
                      {
                        backgroundColor: colors.card,
                        borderColor: colors.border,
                      },
                    ]}
                  >
                    <category.icon
                      size={18}
                      color={category.color}
                      style={styles.categoryIcon}
                    />
                    <Text style={[styles.categoryTextInactive, { color: colors.text }]}>
                      {category.label}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>
    </>
  );
  return (
    <>
      <Stack.Screen
        options={{
          title: profile.language === "nl" ? "Contacten" : "Contacts",
          headerTitleStyle: { color: colors.text },
          headerStyle: { backgroundColor: colors.background },
        }}
      />
      <SafeAreaView
        style={[styles.container, { backgroundColor: colors.background }]}
        edges={["top", "bottom"]}
      >
        {renderHeader()}
        {isDeleting && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        )}
        {filteredContacts.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Users size={48} color={colors.muted} />
            <Text style={[styles.emptyText, { color: colors.muted }]}>
              {searchQuery.length > 0
                ? profile.language === "nl"
                  ? "Geen contacten gevonden voor je zoekopdracht."
                  : "No contacts found for your search."
                : profile.language === "nl"
                ? "Geen contacten gevonden. Voeg je eerste contact toe!"
                : "No contacts found. Add your first contact!"}
            </Text>
            {searchQuery.length > 0 && (
              <TouchableOpacity
                style={[
                  styles.clearSearchButton,
                  { borderColor: colors.primary },
                ]}
                onPress={() => setSearchQuery("")}
              >
                <Text
                  style={[styles.clearSearchText, { color: colors.primary }]}
                >
                  {profile.language === "nl"
                    ? "Zoekopdracht wissen"
                    : "Clear search"}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <FlatList
            data={filteredContacts}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <ContactItem
                contact={item}
                onEdit={() => handleEditContact(item)}
                onDelete={() => handleDeleteContact(item.id)}
                onCall={() => handleCallContact(item.phone)}
                colors={colors}
                language={profile.language || "en"}
              />
            )}
            contentContainerStyle={styles.listContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[colors.primary]}
                tintColor={colors.primary}
              />
            }
            ListFooterComponent={<View style={styles.listFooterSpacer} />}
          />
        )}
        <TouchableOpacity
          style={[
            styles.addButton,
            {
              backgroundColor: colors.primary,
              bottom: 90 + insets.bottom,
            },
          ]}
          onPress={handleAddContact}
        >
          <Plus size={24} color="#fff" />
        </TouchableOpacity>
        <Modal
          visible={showContactForm}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setShowContactForm(false)}
        >
          <ContactForm
            onClose={() => setShowContactForm(false)}
            colors={colors}
            initialContact={selectedContact || undefined}
            language={profile.language || "en"}
          />
        </Modal>
        {Platform.OS !== "web" && (
          <Modal
            visible={showImportContacts}
            animationType="slide"
            transparent={true}
            onRequestClose={() => setShowImportContacts(false)}
          >
            <ContactImport
              onClose={() => setShowImportContacts(false)}
              colors={colors}
              language={profile.language || "en"}
            />
          </Modal>
        )}
      </SafeAreaView>
    </>
  );
}
const styles = StyleSheet.create({


  addButton: {
    alignItems: "center",
    borderRadius: 28,
    elevation: 5,
    height: 56,
    justifyContent: "center",
    position: "absolute",
    right: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    width: 56,
  },
  categoriesContainer: {
    gap: 10,
    paddingHorizontal: 16,
  },
  categoriesWrapper: {
    paddingVertical: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  categoryButton: {
    alignItems: "center",
    borderRadius: 24,
    flexDirection: "row",
    justifyContent: "center",
    minWidth: 100,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  categoryButtonInactive: {
    alignItems: "center",
    borderRadius: 24,
    borderWidth: 1,
    flexDirection: "row",
    justifyContent: "center",
    minWidth: 100,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  categoryButtonWrapper: {
    borderRadius: 24,
    elevation: 3,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  categoryIcon: {
    marginRight: 6,
  },
  categoryTextActive: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  categoryTextInactive: {
    fontSize: 14,
    fontWeight: "600",
  },
  clearSearchButton: {
    borderRadius: 8,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  clearSearchText: {
    fontWeight: "500",
  },
  container: {
    flex: 1,
  },
  emptyContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    marginBottom: 16,
    marginTop: 16,
    maxWidth: "80%",
    textAlign: "center",
  },
  headerActions: {
    flexDirection: "row",
    gap: 8,
  },
  headerButton: {
    alignItems: "center",
    borderRadius: 20,
    elevation: 2,
    height: 40,
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    width: 40,
  },
  headerContainer: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "flex-end",
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  listContent: {
    padding: 16,
  },
  listFooterSpacer: {
    height: 80,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "center",
    zIndex: 1000,
  },
  searchContainer: {
    overflow: "hidden",
    paddingHorizontal: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    height: 40,
  },
  searchInputContainer: {
    alignItems: "center",
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: "row",
    height: 40,
    paddingHorizontal: 12,
  },
  sortButton: {
    backgroundColor: "rgba(150, 150, 150, 0.1)",
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  sortButtonTextActive: {
    color: "#fff",
    fontWeight: "500",
  },
  sortButtonTextActiveColor: {
    color: "#fff",
  },
  sortButtonTextInactive: {
    fontWeight: "500",
  },
  sortButtons: {
    flexDirection: "row",
    gap: 8,
  },
  sortOptionsContainer: {
    overflow: "hidden",
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  sortTitle: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 8,
  },


});