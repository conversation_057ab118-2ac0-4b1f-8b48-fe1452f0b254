/* eslint-disable react-native/no-unused-styles */
import React, { useState, useCallback } from "react";
import {
  View,
  FlatList,
  StyleSheet,
  SafeAreaView,
  Text,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { useLibraryStore, LibraryItem } from "@/store/media/library-store";
import {
  LibraryHeader,
  LibraryItemComponent,
  AddItemModal,
  ImageViewer,
  DocumentViewer,
} from "@/components/library";
import { Trash2, CheckSquare, Square } from "lucide-react-native";

export default function LibraryScreen() {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const {
    getFilteredItems,
    selectedItems,
    selectItem,
    deselectItem,
    clearSelection,
    deleteSelectedItems,
    deleteItem,
  } = useLibraryStore();

  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showAddModal, setShowAddModal] = useState(false);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [showDocumentViewer, setShowDocumentViewer] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedDocument, setSelectedDocument] = useState<LibraryItem | null>(null);

  const styles = createStyles(colors);
  const filteredItems = getFilteredItems();

  const handleItemPress = useCallback((item: LibraryItem) => {
    if (selectedItems.length > 0) {
      // If in selection mode, toggle selection
      if (selectedItems.includes(item.id)) {
        deselectItem(item.id);
      } else {
        selectItem(item.id);
      }
    } else {
      // Normal press - open appropriate viewer
      if (item.type === "image") {
        const imageItems = filteredItems.filter(i => i.type === "image");
        const imageIndex = imageItems.findIndex(i => i.id === item.id);
        setSelectedImageIndex(imageIndex);
        setShowImageViewer(true);
      } else if (item.type === "document") {
        setSelectedDocument(item);
        setShowDocumentViewer(true);
      } else if (item.type === "music") {
        // Music files are handled by the play button in LibraryItemComponent
        Alert.alert("Music File", `Use the play button to play "${item.title}"`);
      }
    }
  }, [selectedItems, selectItem, deselectItem, filteredItems]);

  const handleItemLongPress = useCallback((item: LibraryItem) => {
    if (!selectedItems.includes(item.id)) {
      selectItem(item.id);
    }
  }, [selectedItems, selectItem]);

  const handleDeleteSelected = () => {
    if (selectedItems.length === 0) return;

    Alert.alert(
      "Delete Items",
      `Are you sure you want to delete ${selectedItems.length} item(s)?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => {
            deleteSelectedItems();
            clearSelection();
          },
        },
      ]
    );
  };

  const renderItem = ({ item }: { item: LibraryItem }) => (
    <LibraryItemComponent
      item={item}
      viewMode={viewMode}
      isSelected={selectedItems.includes(item.id)}
      onPress={() => handleItemPress(item)}
      onLongPress={() => handleItemLongPress(item)}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyTitle}>Your Library is Empty</Text>
      <Text style={styles.emptySubtitle}>
        Start by adding some images, documents, or music files to your library.
      </Text>
      <TouchableOpacity
        style={styles.emptyButton}
        onPress={() => setShowAddModal(true)}
      >
        <Text style={styles.emptyButtonText}>Add Your First Item</Text>
      </TouchableOpacity>
    </View>
  );

  const renderSelectionBar = () => {
    if (selectedItems.length === 0) return null;

    return (
      <View style={styles.selectionBar}>
        <View style={styles.selectionInfo}>
          <CheckSquare size={20} color={colors.primary} />
          <Text style={styles.selectionText}>
            {selectedItems.length} item(s) selected
          </Text>
        </View>
        <View style={styles.selectionActions}>
          <TouchableOpacity
            style={styles.selectionButton}
            onPress={clearSelection}
          >
            <Square size={20} color={colors.muted} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.selectionButton, styles.deleteButton]}
            onPress={handleDeleteSelected}
          >
            <Trash2 size={20} color={colors.danger} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <LibraryHeader
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        onAddPress={() => setShowAddModal(true)}
      />

      {renderSelectionBar()}

      <FlatList
        data={filteredItems}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        numColumns={viewMode === "grid" ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        contentContainerStyle={[
          styles.listContainer,
          filteredItems.length === 0 && styles.emptyListContainer,
        ]}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      <AddItemModal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
      />

      <ImageViewer
        visible={showImageViewer}
        images={filteredItems.filter(item => item.type === "image")}
        initialIndex={selectedImageIndex}
        onClose={() => setShowImageViewer(false)}
        onDelete={(item) => {
          Alert.alert(
            "Delete Image",
            `Are you sure you want to delete "${item.title}"?`,
            [
              { text: "Cancel", style: "cancel" },
              {
                text: "Delete",
                style: "destructive",
                onPress: () => {
                  deleteItem(item.id);
                  setShowImageViewer(false);
                },
              },
            ]
          );
        }}
        onShare={(item) => {
          Alert.alert("Share", `Sharing "${item.title}" is not implemented yet.`);
        }}
      />

      <DocumentViewer
        visible={showDocumentViewer}
        document={selectedDocument}
        onClose={() => setShowDocumentViewer(false)}
        onDelete={(item) => {
          Alert.alert(
            "Delete Document",
            `Are you sure you want to delete "${item.title}"?`,
            [
              { text: "Cancel", style: "cancel" },
              {
                text: "Delete",
                style: "destructive",
                onPress: () => {
                  deleteItem(item.id);
                  setShowDocumentViewer(false);
                },
              },
            ]
          );
        }}
        onShare={(item) => {
          Alert.alert("Share", `Sharing "${item.title}" is not implemented yet.`);
        }}
      />
    </SafeAreaView>
  );
}

const createStyles = (colors: typeof Colors.light) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      flex: 1,
    },
    deleteButton: {
      backgroundColor: colors.danger + "20",
    },
    emptyButton: {
      backgroundColor: colors.primary,
      borderRadius: 12,
      marginTop: 24,
      paddingHorizontal: 24,
      paddingVertical: 12,
    },
    emptyButtonText: {
      color: colors.background,
      fontSize: 16,
      fontWeight: "600",
      textAlign: "center",
    },
    emptyContainer: {
      alignItems: "center",
      flex: 1,
      justifyContent: "center",
      paddingHorizontal: 32,
    },
    emptyListContainer: {
      flex: 1,
    },
    emptySubtitle: {
      color: colors.muted,
      fontSize: 16,
      lineHeight: 24,
      marginTop: 8,
      textAlign: "center",
    },
    emptyTitle: {
      color: colors.text,
      fontSize: 24,
      fontWeight: "700",
      textAlign: "center",
    },
    listContainer: {
      paddingBottom: 100, // Account for tab bar
      paddingTop: 8,
    },
    selectionActions: {
      alignItems: "center",
      flexDirection: "row",
      gap: 12,
    },
    selectionBar: {
      alignItems: "center",
      backgroundColor: colors.card,
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    selectionButton: {
      alignItems: "center",
      borderRadius: 8,
      height: 40,
      justifyContent: "center",
      width: 40,
    },
    selectionInfo: {
      alignItems: "center",
      flexDirection: "row",
      gap: 8,
    },
    selectionText: {
      color: colors.text,
      fontSize: 16,
      fontWeight: "600",
    },
  }); 