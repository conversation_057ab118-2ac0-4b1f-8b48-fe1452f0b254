import React from "react";
import { Tabs } from "expo-router";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { StyleSheet, Platform, Animated } from "react-native";
import { BlurView } from "expo-blur";
import { Home, TrendingUp, Brain, Users, Settings, FolderOpen, Shield } from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";

export default function TabsLayout() {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { t } = useTranslation();

  // Animated tab bar icon with scale effect
  const TabBarIcon = ({
    IconComponent,
    color,
    focused,
    size = 20,
    isHomeButton = false,
  }: {
    IconComponent: React.ComponentType<{ size: number; color: string }>;
    color: string;
    focused: boolean;
    size?: number;
    isHomeButton?: boolean;
  }) => {
    const scaleValue = React.useRef(new Animated.Value(focused ? 1.1 : 1)).current;

    React.useEffect(() => {
      Animated.spring(scaleValue, {
        toValue: focused ? 1.1 : 1,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }).start();
    }, [focused, scaleValue]);

    if (isHomeButton) {
      return (
        <Animated.View 
          style={[
            styles.homeButtonContainer,
            { 
              backgroundColor: focused ? colors.primary : colors.primary + '90',
              transform: [{ scale: scaleValue }] 
            }
          ]}
        >
          <IconComponent size={focused ? size + 2 : size} color={colors.background} />
        </Animated.View>
      );
    }

    return (
      <Animated.View 
        style={[
          styles.tabIconContainer,
          { transform: [{ scale: scaleValue }] }
        ]}
      >
        <IconComponent size={focused ? size + 1 : size} color={color} />
      </Animated.View>
    );
  };

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          position: "absolute",
          bottom: Platform.OS === "ios" ? 30 : 16,
          left: 12,
          right: 12,
          height: 68,
          backgroundColor: colors.card,
          borderRadius: 26,
          borderWidth: 1,
          borderColor: colors.border,
          shadowColor: currentTheme === 'dark' ? colors.primary : "#000",
          shadowOffset: { width: 0, height: 6 },
          shadowOpacity: currentTheme === 'dark' ? 0.2 : 0.1,
          shadowRadius: 20,
          elevation: 10,
          paddingBottom: 6,
          paddingTop: 6,
          paddingHorizontal: 4,
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.muted,
        tabBarLabelStyle: {
          fontSize: 9.5,
          fontWeight: "600",
          marginTop: 1,
          marginBottom: 3,
          letterSpacing: 0.1,
        },
        tabBarIconStyle: {
          marginBottom: 1,
        },
        tabBarItemStyle: {
          paddingHorizontal: 1,
          flex: 1,
          minWidth: 0,
          height: 56,
        },
        tabBarBackground: () => (
          <BlurView intensity={currentTheme === 'dark' ? 60 : 80} style={styles.tabBarBackground} />
        ),
      }}
    >
      {/* Left side tabs */}
      <Tabs.Screen
        name="progress"
        options={{
          title: t('navigation.progress'),
          tabBarIcon: ({ color, focused }) => (
            <TabBarIcon
              IconComponent={TrendingUp}
              color={color}
              focused={focused}
              size={20}
            />
          ),
        }}
      />
      
      <Tabs.Screen
        name="mindfulness"
        options={{
          title: t('navigation.mindfulness'),
          tabBarIcon: ({ color, focused }) => (
            <TabBarIcon IconComponent={Brain} color={color} focused={focused} size={20} />
          ),
        }}
      />
      
      <Tabs.Screen
        name="library"
        options={{
          title: t('navigation.library'),
          tabBarIcon: ({ color, focused }) => (
            <TabBarIcon IconComponent={FolderOpen} color={color} focused={focused} size={20} />
          ),
        }}
      />
      
      {/* Center - Dashboard/Home button */}
      <Tabs.Screen
        name="dashboard/index"
        options={{
          title: t('navigation.dashboard'),
          tabBarIcon: ({ color, focused }) => (
            <TabBarIcon 
              IconComponent={Home} 
              color={color} 
              focused={focused} 
              size={24}
              isHomeButton={true}
            />
          ),
          tabBarLabelStyle: {
            fontSize: 9.5,
            fontWeight: "600",
            marginTop: 1,
            marginBottom: 3,
            letterSpacing: 0.1,
          },
        }}
      />
      
      {/* Right side tabs */}
      <Tabs.Screen
        name="contacts"
        options={{
          title: t('navigation.contacts'),
          tabBarIcon: ({ color, focused }) => (
            <TabBarIcon IconComponent={Users} color={color} focused={focused} size={20} />
          ),
        }}
      />
      
      <Tabs.Screen
        name="crisis"
        options={{
          title: t('navigation.crisis'),
          tabBarIcon: ({ color, focused }) => (
            <TabBarIcon IconComponent={Shield} color={color} focused={focused} size={20} />
          ),
        }}
      />
      
      <Tabs.Screen
        name="settings"
        options={{
          title: t('navigation.settings'),
          tabBarIcon: ({ color, focused }) => (
            <TabBarIcon
              IconComponent={Settings}
              color={color}
              focused={focused}
              size={20}
            />
          ),
        }}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  homeButtonContainer: {
    alignItems: "center",
    borderRadius: 22,
    elevation: 12,
    height: 44,
    justifyContent: "center",
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    width: 44,
  },
  tabBarBackground: {
    ...StyleSheet.absoluteFillObject,
    borderRadius: 26,
    overflow: "hidden",
  },
  tabIconContainer: {
    alignItems: "center",
    height: 26,
    justifyContent: "center",
    width: 26,
  },
});
