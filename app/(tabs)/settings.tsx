import React from "react";
import { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  Alert,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useUserStore } from "@/store/user/user-store";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { router } from "expo-router";
import {
  Calendar,
  DollarSign,
  Euro,
  Globe,
  HelpCircle,
  Info,
  Lock,
  LogOut,
  Moon,
  Shield,
  Sun,
  Monitor,
  Bell,
  Download,
  Upload,
  AlertTriangle,
  LayoutGrid,
  Circle,
  Bug,
} from "lucide-react-native";
import * as Haptics from "expo-haptics";
import DateTimePicker from "@react-native-community/datetimepicker";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import * as DocumentPicker from "expo-document-picker";
// Import components
import { ProfileSection } from "@/components/settings/ProfileSection";
import { SettingsSection } from "@/components/settings/SettingsSection";
import { ProfileModal } from "@/components/settings/ProfileModal";
import { UsageModal } from "@/components/settings/UsageModal";
import { NotificationsModal } from "@/components/settings/NotificationsModal";
import { PrivacyModal } from "@/components/settings/PrivacyModal";
import { AboutModal } from "@/components/settings/AboutModal";
import { HelpSupportModal } from "@/components/settings/HelpSupportModal";
import { DatabaseMigrationModal } from "@/components/settings/DatabaseMigrationModal";
import { DebugPanel } from "@/components/shared";
import { useDebug } from "@/hooks/useDebug";
import { useDatabaseContext } from "@/context/database-context";
export default function SettingsScreen() {
  const {
    profile,
    updateProfile,
    resetOnboardingStatus,
    exportUserData,
    importUserData,
  } = useUserStore();
  const { theme, currentTheme, setTheme } = useTheme();
  const colors = Colors[currentTheme];
  const debug = useDebug();
  const { isInitialized: isDatabaseInitialized } = useDatabaseContext();
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showAboutModal, setShowAboutModal] = useState(false);
  const [showNotificationsModal, setShowNotificationsModal] = useState(false);
  const [showUsageModal, setShowUsageModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showHelpSupportModal, setShowHelpSupportModal] = useState(false);
  const [showDatabaseMigrationModal, setShowDatabaseMigrationModal] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  if (!profile) {
    return null;
  }
  const handleThemeChange = (newTheme: "light" | "dark" | "system") => {
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
    setTheme(newTheme);
  };
  const handleLanguageChange = () => {
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
    updateProfile({
      language: profile.language === "en" ? "nl" : "en",
    });
  };
  const handleCurrencyChange = () => {
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
    updateProfile({
      currency: profile.currency === "USD" ? "EUR" : "USD",
    });
  };
  const handleSobrietyDateChange = (event: unknown, selectedDate?: Date) => {
    if (selectedDate) {
      updateProfile({
        sobrietyDate: selectedDate.toISOString(),
      });
      if (Platform.OS === "android") {
        setShowDatePicker(false);
      }
    }
  };
  const toggleDashboardView = () => {
    const newView = profile.dashboardView === "cards" ? "circular" : "cards";
    updateProfile({ dashboardView: newView });
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
  };
  const confirmResetOnboarding = () => {
    Alert.alert(
      profile.language === "nl" ? "Onboarding resetten?" : "Reset Onboarding?",
      profile.language === "nl"
        ? "Weet je zeker dat je de onboarding opnieuw wilt doorlopen?"
        : "Are you sure you want to go through the onboarding again?",
      [
        {
          text: profile.language === "nl" ? "Annuleren" : "Cancel",
          style: "cancel",
        },
        {
          text: profile.language === "nl" ? "Resetten" : "Reset",
          onPress: () => {
            resetOnboardingStatus();
            router.replace("/onboarding");
          },
          style: "destructive",
        },
      ]
    );
  };
  const exportData = async () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    try {
      setIsExporting(true);
      // Get the data to export
      const exportData = await exportUserData();
      if (Platform.OS === "web") {
        // For web, create a download link
        const dataStr =
          "data:text/json;charset=utf-8," +
          encodeURIComponent(JSON.stringify(exportData));
        const downloadAnchorNode = document.createElement("a");
        downloadAnchorNode.setAttribute("href", dataStr);
        downloadAnchorNode.setAttribute("download", "crisisbox_data.json");
        document.body.appendChild(downloadAnchorNode);
        downloadAnchorNode.click();
        downloadAnchorNode.remove();
      } else {
        // For mobile, save to file and share
        const fileUri = `${FileSystem.cacheDirectory}crisisbox_data.json`;
        await FileSystem.writeAsStringAsync(
          fileUri,
          JSON.stringify(exportData),
          {
            encoding: FileSystem.EncodingType.UTF8,
          }
        );
        // Check if sharing is available
        const isSharingAvailable = await Sharing.isAvailableAsync();
        if (isSharingAvailable) {
          await Sharing.shareAsync(fileUri, {
            mimeType: "application/json",
            dialogTitle:
              profile.language === "nl"
                ? "Deel je gegevens"
                : "Share your data",
            UTI: "public.json",
          });
        } else {
          Alert.alert(
            profile.language === "nl"
              ? "Delen niet beschikbaar"
              : "Sharing not available",
            profile.language === "nl"
              ? "Delen is niet beschikbaar op dit apparaat."
              : "Sharing is not available on this device."
          );
        }
      }
      Alert.alert(
        profile.language === "nl" ? "Gegevens geëxporteerd" : "Data Exported",
        profile.language === "nl"
          ? "Je gegevens zijn succesvol geëxporteerd."
          : "Your data has been successfully exported."
      );
    } catch (_error) {
      console.error("Error exporting data:", _error);
      Alert.alert(
        profile.language === "nl" ? "Fout bij exporteren" : "Export Error",
        profile.language === "nl"
          ? "Er is een fout opgetreden bij het exporteren van je gegevens."
          : "An error occurred while exporting your data."
      );
    } finally {
      setIsExporting(false);
    }
  };
  const importData = async () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    Alert.alert(
      profile.language === "nl" ? "Gegevens importeren" : "Import Data",
      profile.language === "nl"
        ? "Wil je gegevens importeren? Dit zal je huidige gegevens overschrijven."
        : "Do you want to import data? This will overwrite your current data.",
      [
        {
          text: profile.language === "nl" ? "Annuleren" : "Cancel",
          style: "cancel",
        },
        {
          text: profile.language === "nl" ? "Importeren" : "Import",
          onPress: async () => {
            try {
              setIsImporting(true);
              // Pick a document
              const result = await DocumentPicker.getDocumentAsync({
                type: ["application/json"],
                copyToCacheDirectory: true,
              });
              if (result.canceled) {
                setIsImporting(false);
                return;
              }
              // Read the file
              let fileContent;
              if (Platform.OS === "web") {
                // For web, we need to handle the file differently
                try {
                  // Make sure we have a valid File object
                  const file = result.assets[0];
                  if (!file) {
                    throw new Error("No file selected");
                  }
                  // Create a new Promise to handle the FileReader
                  const fileData = await new Promise<string>(
                    (resolve, reject) => {
                      const reader = new FileReader();
                      reader.onload = (e) => {
                        try {
                          const content = e.target?.result;
                          if (typeof content === "string") {
                            resolve(content);
                          } else {
                            reject(
                              new Error("Failed to read file content as string")
                            );
                          }
                        } catch (_error) {
                          reject(_error);
                        }
                      };
                      reader.onerror = (e) => {
                        reject(
                          new Error("Error reading file: " + e.target?.error)
                        );
                      };
                      // Read the file as text - make sure we have a valid Blob
                      if (file instanceof Blob) {
                        reader.readAsText(file);
                      } else if (file.uri) {
                        // If we have a URI but not a Blob, fetch the file first
                        fetch(file.uri)
                          .then((response) => response.blob())
                          .then((blob) => reader.readAsText(blob))
                          .catch((_error) => reject(_error));
                      } else {
                        reject(new Error("Invalid file format"));
                      }
                    }
                  );
                  // Parse the JSON data
                  const data = JSON.parse(fileData);
                  // Import the data
                  await importUserData(data);
                  // Success feedback
                  if (Platform.OS !== "web") {
                    Haptics.notificationAsync(
                      Haptics.NotificationFeedbackType.Success
                    );
                  }
                  Alert.alert(
                    "Success",
                    profile.language === "nl"
                      ? "Gegevens geïmporteerd"
                      : "Data Imported",
                    [
                      {
                        text: "OK",
                        onPress: () => {
                          // Refresh the page to show the imported data
                          setIsImporting(false);
                        },
                      },
                    ]
                  );
                } catch (_error) {
                  console.error("Error processing file on web:", _error);
                  Alert.alert(
                    "Error",
                    profile.language === "nl"
                      ? "Het bestand bevat ongeldige gegevens of kon niet worden gelezen."
                      : "The file contains invalid data or could not be read."
                  );
                  setIsImporting(false);
                }
              } else {
                // For mobile, read the file using FileSystem
                try {
                  fileContent = await FileSystem.readAsStringAsync(
                    result.assets[0].uri
                  );
                  // Parse the JSON data
                  const data = JSON.parse(fileContent);
                  // Import the data
                  await importUserData(data);
                  // Success feedback
                  if (Platform.OS === "ios" || Platform.OS === "android") {
                    Haptics.notificationAsync(
                      Haptics.NotificationFeedbackType.Success
                    );
                  }
                  Alert.alert(
                    "Success",
                    profile.language === "nl"
                      ? "Gegevens geïmporteerd"
                      : "Data Imported",
                    [
                      {
                        text: "OK",
                        onPress: () => {
                          // Refresh the page to show the imported data
                          setIsImporting(false);
                        },
                      },
                    ]
                  );
                } catch (_error) {
                  console.error("Error processing file on mobile:", _error);
                  Alert.alert(
                    "Error",
                    profile.language === "nl"
                      ? "Het bestand bevat ongeldige gegevens of kon niet worden gelezen."
                      : "The file contains invalid data or could not be read."
                  );
                  setIsImporting(false);
                }
              }
            } catch (_error) {
              console.error("Error importing data:", _error);
              Alert.alert(
                profile.language === "nl"
                  ? "Fout bij importeren"
                  : "Import Error",
                profile.language === "nl"
                  ? "Er is een fout opgetreden bij het importeren van je gegevens."
                  : "An error occurred while importing your data."
              );
              setIsImporting(false);
            }
          },
        },
      ]
    );
  };
  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
      edges={["top", "bottom"]}
    >
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            {profile.language === "nl" ? "Instellingen" : "Settings"}
          </Text>
        </View>
        <ProfileSection
          profile={profile}
          colors={colors}
          onEditProfile={() => {
            console.log('Setting showProfileModal to true');
            setShowProfileModal(true);
          }}
        />
        <SettingsSection
          title={profile.language === "nl" ? "Account" : "Account"}
          colors={colors}
        >
          <SettingsSection.Item
            icon={<Calendar size={20} color={colors.primary} />}
            title={
              profile.language === "nl" ? "Nuchterheid datum" : "Sobriety Date"
            }
            value={new Date(profile.sobrietyDate || "").toLocaleDateString()}
            onPress={() => {
              if (Platform.OS === "android") {
                setShowDatePicker(true);
              } else {
                setShowDatePicker(!showDatePicker);
              }
              if (Platform.OS !== "web") {
                Haptics.selectionAsync();
              }
            }}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
          {showDatePicker && (
            <DateTimePicker
              value={new Date(profile.sobrietyDate || "")}
              mode="date"
              display={Platform.OS === "ios" ? "spinner" : "default"}
              onChange={handleSobrietyDateChange}
              maximumDate={new Date()}
            />
          )}
          <SettingsSection.Item
            icon={
              profile.currency === "USD" ? (
                <DollarSign size={20} color={colors.primary} />
              ) : (
                <Euro size={20} color={colors.primary} />
              )
            }
            title={profile.language === "nl" ? "Valuta" : "Currency"}
            value={profile.currency === "USD" ? "USD ($)" : "EUR (€)"}
            onPress={handleCurrencyChange}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
          <SettingsSection.Item
            icon={
              profile.currency === "USD" ? (
                <DollarSign size={20} color={colors.primary} />
              ) : (
                <Euro size={20} color={colors.primary} />
              )
            }
            title={
              profile.language === "nl" ? "Gebruiksgegevens" : "Usage Details"
            }
            onPress={() => setShowUsageModal(true)}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
          <SettingsSection.Item
            icon={<Download size={20} color={colors.primary} />}
            title={
              profile.language === "nl" ? "Gegevens exporteren" : "Export Data"
            }
            onPress={exportData}
            isLoading={isExporting}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
          <SettingsSection.Item
            icon={<Upload size={20} color={colors.primary} />}
            title={
              profile.language === "nl" ? "Gegevens importeren" : "Import Data"
            }
            onPress={importData}
            isLoading={isImporting}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
        </SettingsSection>
        <SettingsSection
          title={profile.language === "nl" ? "Weergave" : "Appearance"}
          colors={colors}
        >
          <SettingsSection.Item
            icon={<Sun size={20} color={colors.primary} />}
            title={profile.language === "nl" ? "Licht thema" : "Light Theme"}
            onPress={() => handleThemeChange("light")}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
            rightElement={
              theme === "light" && (
                <View
                  style={[
                    styles.checkmark,
                    { backgroundColor: colors.primary },
                  ]}
                >
                  <Text style={styles.checkmarkText}>✓</Text>
                </View>
              )
            }
          />
          <SettingsSection.Item
            icon={<Moon size={20} color={colors.primary} />}
            title={profile.language === "nl" ? "Donker thema" : "Dark Theme"}
            onPress={() => handleThemeChange("dark")}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
            rightElement={
              theme === "dark" && (
                <View
                  style={[
                    styles.checkmark,
                    { backgroundColor: colors.primary },
                  ]}
                >
                  <Text style={styles.checkmarkText}>✓</Text>
                </View>
              )
            }
          />
          <SettingsSection.Item
            icon={<Monitor size={20} color={colors.primary} />}
            title={profile.language === "nl" ? "Systeemthema" : "System Theme"}
            onPress={() => handleThemeChange("system")}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
            rightElement={
              theme === "system" && (
                <View
                  style={[
                    styles.checkmark,
                    { backgroundColor: colors.primary },
                  ]}
                >
                  <Text style={styles.checkmarkText}>✓</Text>
                </View>
              )
            }
          />
          <SettingsSection.Item
            icon={
              profile.dashboardView === "cards" ? (
                <LayoutGrid size={20} color={colors.primary} />
              ) : (
                <Circle size={20} color={colors.primary} />
              )
            }
            title={
              profile.language === "nl"
                ? "Dashboard weergave"
                : "Dashboard View"
            }
            value={
              profile.dashboardView === "cards"
                ? profile.language === "nl"
                  ? "Kaarten"
                  : "Cards"
                : profile.language === "nl"
                ? "Circulair"
                : "Circular"
            }
            onPress={toggleDashboardView}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
        </SettingsSection>
        <SettingsSection
          title={profile.language === "nl" ? "Voorkeuren" : "Preferences"}
          colors={colors}
        >
          <SettingsSection.Item
            icon={<Globe size={20} color={colors.primary} />}
            title={profile.language === "nl" ? "Taal" : "Language"}
            value={profile.language === "en" ? "English" : "Nederlands"}
            onPress={handleLanguageChange}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
          <SettingsSection.Item
            icon={<Bell size={20} color={colors.primary} />}
            title={profile.language === "nl" ? "Notificaties" : "Notifications"}
            onPress={() => {
              if (Platform.OS !== "web") {
                Haptics.selectionAsync();
              }
              setShowNotificationsModal(true);
            }}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
          <SettingsSection.Item
            icon={<Lock size={20} color={colors.primary} />}
            title={
              profile.language === "nl"
                ? "Privacy-instellingen"
                : "Privacy Settings"
            }
            onPress={() => {
              if (Platform.OS !== "web") {
                Haptics.selectionAsync();
              }
              setShowPrivacyModal(true);
            }}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
        </SettingsSection>
        <SettingsSection
          title={profile.language === "nl" ? "Ondersteuning" : "Support"}
          colors={colors}
        >
          <SettingsSection.Item
            icon={<HelpCircle size={20} color={colors.primary} />}
            title={
              profile.language === "nl"
                ? "Help & Ondersteuning"
                : "Help & Support"
            }
            onPress={() => {
              if (Platform.OS !== "web") {
                Haptics.selectionAsync();
              }
              setShowHelpSupportModal(true);
            }}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
          {debug.isEnabled && (
            <SettingsSection.Item
              icon={<Bug size={20} color={colors.warning} />}
              title={
                profile.language === "nl"
                  ? "Debug Informatie"
                  : "Debug Information"
              }
              onPress={() => {
                if (Platform.OS !== "web") {
                  Haptics.selectionAsync();
                }
                debug.setIsDebugPanelVisible(true);
              }}
              textColor={colors.text}
              borderColor={colors.border}
              colors={colors}
            />
          )}
          <SettingsSection.Item
            icon={<Shield size={20} color={colors.primary} />}
            title={
              profile.language === "nl" ? "Privacy Beleid" : "Privacy Policy"
            }
            onPress={() => {
              if (Platform.OS !== "web") {
                Haptics.selectionAsync();
              }
              Alert.alert(
                profile.language === "nl" ? "Privacy Beleid" : "Privacy Policy",
                profile.language === "nl"
                  ? "Het privacy beleid is beschikbaar op onze website."
                  : "The privacy policy is available on our website.",
                [{ text: "OK" }]
              );
            }}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
          <SettingsSection.Item
            icon={<Info size={20} color={colors.primary} />}
            title={
              profile.language === "nl" ? "Over CrisisBox" : "About CrisisBox"
            }
            onPress={() => setShowAboutModal(true)}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
        </SettingsSection>
        {__DEV__ && (
          <SettingsSection
            title={profile.language === "nl" ? "Ontwikkelaar" : "Developer"}
            colors={colors}
          >
            <SettingsSection.Item
              icon={<Download size={20} color={colors.primary} />}
              title={
                profile.language === "nl"
                  ? "Database Migratie"
                  : "Database Migration"
              }
              onPress={() => {
                if (Platform.OS !== "web") {
                  Haptics.selectionAsync();
                }
                setShowDatabaseMigrationModal(true);
              }}
              textColor={colors.text}
              borderColor={colors.border}
              colors={colors}
            />
            <SettingsSection.Item
              icon={<Monitor size={20} color={colors.accent} />}
              title={
                profile.language === "nl"
                  ? "Database Testen"
                  : "Database Testing"
              }
              onPress={() => {
                if (Platform.OS !== "web") {
                  Haptics.selectionAsync();
                }
                router.push('/database-test');
              }}
              textColor={colors.text}
              borderColor={colors.border}
              colors={colors}
            />
            {debug.isEnabled && (
              <SettingsSection.Item
                icon={<Bug size={20} color={colors.warning} />}
                title={
                  profile.language === "nl"
                    ? "Debug Panel"
                    : "Debug Panel"
                }
                onPress={() => {
                  if (Platform.OS !== "web") {
                    Haptics.selectionAsync();
                  }
                  debug.setIsDebugPanelVisible(true);
                }}
                textColor={colors.text}
                borderColor={colors.border}
                colors={colors}
              />
            )}
          </SettingsSection>
        )}
        <SettingsSection title="" colors={colors}>
          <SettingsSection.Item
            icon={<AlertTriangle size={20} color={colors.warning} />}
            title={
              profile.language === "nl"
                ? "Onboarding resetten"
                : "Reset Onboarding"
            }
            onPress={confirmResetOnboarding}
            textColor={colors.text}
            borderColor={colors.border}
            colors={colors}
          />
          <SettingsSection.Item
            icon={<LogOut size={20} color={colors.danger} />}
            title={profile.language === "nl" ? "Uitloggen" : "Log Out"}
            onPress={() => {
              if (Platform.OS !== "web") {
                Haptics.notificationAsync(
                  Haptics.NotificationFeedbackType.Warning
                );
              }
              Alert.alert(
                profile.language === "nl" ? "Uitloggen" : "Log Out",
                profile.language === "nl"
                  ? "Weet je zeker dat je wilt uitloggen?"
                  : "Are you sure you want to log out?",
                [
                  {
                    text: profile.language === "nl" ? "Annuleren" : "Cancel",
                    style: "cancel",
                  },
                  {
                    text: profile.language === "nl" ? "Uitloggen" : "Log Out",
                    onPress: () => console.log("User logged out"),
                    style: "destructive",
                  },
                ]
              );
            }}
            textColor={colors.danger}
            borderColor={colors.border}
            colors={colors}
          />
        </SettingsSection>
        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: colors.muted }]}>
            CrisisBox v1.0.0
          </Text>
        </View>
      </ScrollView>
      {/* Modals */}
      <ProfileModal
        visible={showProfileModal}
        onClose={() => {
          console.log('Closing ProfileModal');
          setShowProfileModal(false);
        }}
        colors={colors}
        language={profile.language || "en"}
      />
      <UsageModal
        visible={showUsageModal}
        onClose={() => setShowUsageModal(false)}
        profile={profile}
        colors={colors}
      />
      <NotificationsModal
        visible={showNotificationsModal}
        onClose={() => setShowNotificationsModal(false)}
        profile={profile}
        colors={colors}
      />
      <PrivacyModal
        visible={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
        profile={profile}
        colors={colors}
      />
      <AboutModal
        visible={showAboutModal}
        onClose={() => setShowAboutModal(false)}
        profile={profile}
        colors={colors}
      />
      <HelpSupportModal
        visible={showHelpSupportModal}
        onClose={() => setShowHelpSupportModal(false)}
        profile={profile}
        colors={colors}
      />
      <DatabaseMigrationModal
        visible={showDatabaseMigrationModal}
        onClose={() => setShowDatabaseMigrationModal(false)}
      />
      <DebugPanel
        visible={debug.isDebugPanelVisible}
        onClose={() => debug.setIsDebugPanelVisible(false)}
      />
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({


  checkmark: {
    alignItems: "center",
    borderRadius: 12,
    height: 24,
    justifyContent: "center",
    width: 24,
  },
  checkmarkText: {
    color: "#fff",
    fontWeight: "bold",
  },
  container: {
    flex: 1,
  },
  footer: {
    alignItems: "center",
    padding: 20,
  },
  footerText: {
    fontSize: 14,
  },
  header: {
    padding: 20,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  scrollView: {
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
  },


});