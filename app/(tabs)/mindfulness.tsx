import React, { useState, useCallback } from "react";
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  Platform,
  Modal,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useUserStore } from "@/store/user/user-store";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";

import * as Haptics from "expo-haptics";
import { Ionicons } from "@expo/vector-icons";
import { BreathingExercise } from "@/components/mindfulness/BreathingExercise";
import { GroundingExercise } from "@/components/mindfulness/GroundingExercise";
import { MeditationExercise } from "@/components/mindfulness/MeditationExercise";
import { BodyScanExercise } from "@/components/mindfulness/BodyScanExercise";
import { VisualizationExercise } from "@/components/mindfulness/VisualizationExercise";
import { GratitudeExercise } from "@/components/mindfulness/GratitudeExercise";
import { MuscleRelaxationExercise } from "@/components/mindfulness/MuscleRelaxationExercise";
import { CustomExercise } from "@/components/mindfulness/CustomExercise";
import { ExerciseForm } from "@/components/mindfulness/ExerciseForm";
import { ExerciseList } from "@/components/mindfulness/ExerciseList";
import {
  BUILT_IN_EXERCISES,
  getAllExercises,
  getFavoriteExercises,
} from "@/constants/mindfulness-exercises";
import { MindfulnessExercise } from "@/types/mindfulness";
export default function MindfulnessScreen() {
  const {
    profile,
    toggleFavoriteExercise,
    removeCustomExercise,
    addCompletedExercise,
  } = useUserStore();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const [activeView, setActiveView] = useState<
    "featured" | "all" | "favorites" | "custom"
  >("featured");
  const [activeExercise, setActiveExercise] =
    useState<MindfulnessExercise | null>(null);
  const [showAddExerciseModal, setShowAddExerciseModal] = useState(false);
  const [editExerciseId, setEditExerciseId] = useState<string | undefined>(
    undefined
  );
  const [currentExerciseElapsedSeconds, setCurrentExerciseElapsedSeconds] =
    useState(0);
  // Get exercises based on active view
  const getExercisesToDisplay = () => {
    const customExercises = profile?.customExercises || [];
    const favoriteIds = profile?.favoriteExercises || [];
    switch (activeView) {
      case "all":
        return getAllExercises(customExercises);
      case "favorites":
        return getFavoriteExercises(favoriteIds, customExercises);
      case "custom":
        return customExercises;
      case "featured":
      default:
        // Return a subset of built-in exercises for the featured view
        return BUILT_IN_EXERCISES.slice(0, 4);
    }
  };
  const handleExerciseSelect = (exercise: MindfulnessExercise) => {
    setActiveExercise(exercise);
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
    setCurrentExerciseElapsedSeconds(0); // Reset elapsed time for new exercise
  };
  const handleToggleFavorite = (id: string) => {
    toggleFavoriteExercise(id);
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  const handleEditExercise = (id: string) => {
    setEditExerciseId(id);
    setShowAddExerciseModal(true);
  };
  const handleDeleteExercise = (id: string) => {
    if (Platform.OS === "web") {
      if (
        confirm(
          profile?.language === "nl"
            ? "Weet je zeker dat je deze oefening wilt verwijderen?"
            : "Are you sure you want to delete this exercise?"
        )
      ) {
        removeCustomExercise(id);
      }
    } else {
      Alert.alert(
        profile?.language === "nl" ? "Oefening Verwijderen" : "Delete Exercise",
        profile?.language === "nl"
          ? "Weet je zeker dat je deze oefening wilt verwijderen?"
          : "Are you sure you want to delete this exercise?",
        [
          {
            text: profile?.language === "nl" ? "Annuleren" : "Cancel",
            style: "cancel",
          },
          {
            text: profile?.language === "nl" ? "Verwijderen" : "Delete",
            style: "destructive",
            onPress: () => {
              removeCustomExercise(id);
              if (Platform.OS !== "web") {
                Haptics.notificationAsync(
                  Haptics.NotificationFeedbackType.Success
                );
              }
            },
          },
        ]
      );
    }
  };
  const handleCloseExercise = () => {
    // Record completed exercise
    if (activeExercise) {
      addCompletedExercise({
        exerciseId: activeExercise.id,
        date: new Date().toISOString(),
        duration: currentExerciseElapsedSeconds, // Use actual tracked duration
      });
    }
    setActiveExercise(null);
  };
  const handleAddExercise = () => {
    setEditExerciseId(undefined);
    setShowAddExerciseModal(true);
  };
  const handleDurationUpdate = useCallback((seconds: number) => {
    setCurrentExerciseElapsedSeconds(seconds);
  }, []);
  const handleCloseAddExerciseModal = () => {
    setShowAddExerciseModal(false);
    setEditExerciseId(undefined);
  };
  const renderExerciseContent = () => {
    if (!activeExercise) return null;
    switch (activeExercise.type) {
      case "breathing":
        return (
          <BreathingExercise
            colors={colors}
            language={profile?.language || "en"}
            _activeExercise={activeExercise}
            onDurationUpdate={handleDurationUpdate}
          />
        );
      case "grounding":
        return (
          <GroundingExercise
            colors={colors}
            language={profile?.language || "en"}
            activeExercise={activeExercise}
            onDurationUpdate={handleDurationUpdate}
          />
        );
      case "meditation":
        return (
          <MeditationExercise
            colors={colors}
            language={profile?.language || "en"}
            activeExercise={activeExercise}
            onDurationUpdate={handleDurationUpdate}
          />
        );
      case "bodyscan":
        return (
          <BodyScanExercise
            colors={colors}
            language={profile?.language || "en"}
            activeExercise={activeExercise}
            onDurationUpdate={handleDurationUpdate}
          />
        );
      case "visualization":
        return (
          <VisualizationExercise
            colors={colors}
            language={profile?.language || "en"}
            activeExercise={activeExercise}
            onDurationUpdate={handleDurationUpdate}
          />
        );
      case "gratitude":
        return (
          <GratitudeExercise
            colors={colors}
            language={profile?.language || "en"}
            _activeExercise={activeExercise}
            _onDurationUpdate={handleDurationUpdate}
          />
        );
      case "muscle-relaxation":
        return (
          <MuscleRelaxationExercise
            colors={colors}
            language={profile?.language || "en"}
            _activeExercise={activeExercise}
            onDurationUpdate={handleDurationUpdate}
          />
        );
      case "custom":
      default:
        return (
          <CustomExercise
            colors={colors}
            language={profile?.language || "en"}
            exercise={activeExercise}
            onDurationUpdate={handleDurationUpdate}
          />
        );
    }
  };
  // Helper functions for dynamic styles
  const getViewTabStyle = (isActive: boolean) => [
    styles.viewTab,
    isActive && styles.activeViewTab,
    {
      borderColor: isActive ? colors.primary : colors.border,
      backgroundColor: isActive ? colors.primary : "transparent",
    },
  ];
  const getViewTabTextStyle = (isActive: boolean) => [
    styles.viewTabText,
    {
      color: isActive ? colors.background : colors.text,
    },
  ];
  // Render item for benefits section
  const renderBenefitItem = ({
    item,
  }: {
    item: { title: string; description: string };
  }) => (
    <View
      style={[
        styles.benefitCard,
        { backgroundColor: colors.card, borderColor: colors.border },
      ]}
    >
      <Text style={[styles.benefitTitle, { color: colors.text }]}>
        {item.title}
      </Text>
      <Text style={[styles.benefitDescription, { color: colors.muted }]}>
        {item.description}
      </Text>
    </View>
  );
  // Render item for stats section
  const renderStatItem = ({
    item,
  }: {
    item: { value: number; label: string };
  }) => (
    <View
      style={[
        styles.statCard,
        { backgroundColor: colors.card, borderColor: colors.border },
      ]}
    >
      <Text style={[styles.statValue, { color: colors.primary }]}>
        {item.value}
      </Text>
      <Text style={[styles.statLabel, { color: colors.muted }]}>
        {item.label}
      </Text>
    </View>
  );
  // Benefits data
  const benefitsData = [
    {
      title:
        profile?.language === "nl" ? "Vermindert Stress" : "Reduces Stress",
      description:
        profile?.language === "nl"
          ? "Regelmatige mindfulness-oefeningen kunnen stress en angst verminderen."
          : "Regular mindfulness practice can reduce stress and anxiety.",
    },
    {
      title: profile?.language === "nl" ? "Verbetert Focus" : "Improves Focus",
      description:
        profile?.language === "nl"
          ? "Mindfulness helpt je om je aandacht te richten en afleiding te verminderen."
          : "Mindfulness helps you direct your attention and reduce distractions.",
    },
    {
      title:
        profile?.language === "nl"
          ? "Ondersteunt Herstel"
          : "Supports Recovery",
      description:
        profile?.language === "nl"
          ? "Mindfulness kan helpen bij het beheersen van cravings en het voorkomen van terugvallen."
          : "Mindfulness can help manage cravings and prevent relapses.",
    },
    {
      title: profile?.language === "nl" ? "Verbetert Slaap" : "Improves Sleep",
      description:
        profile?.language === "nl"
          ? "Mindfulness kan helpen om beter in slaap te vallen en de slaapkwaliteit te verbeteren."
          : "Mindfulness can help you fall asleep more easily and improve sleep quality.",
    },
  ];
  // Stats data
  const statsData = [
    {
      value: (profile?.completedExercises || []).length,
      label:
        profile?.language === "nl"
          ? "Voltooide Oefeningen"
          : "Completed Exercises",
    },
    {
      value: (profile?.favoriteExercises || []).length,
      label:
        profile?.language === "nl"
          ? "Favoriete Oefeningen"
          : "Favorite Exercises",
    },
    {
      value: (profile?.customExercises || []).length,
      label:
        profile?.language === "nl"
          ? "Aangepaste Oefeningen"
          : "Custom Exercises",
    },
  ];
  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
      edges={["top", "bottom"]}
    >
      {activeExercise ? (
        <View style={styles.exerciseContainer}>
          <View
            style={[
              styles.exerciseHeader,
              { borderBottomColor: colors.border },
            ]}
          >
            <TouchableOpacity
              onPress={handleCloseExercise}
              style={styles.backButton}
            >
              <Ionicons name="chevron-back" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.exerciseHeaderTitle, { color: colors.text }]}>
              {activeExercise.title}
            </Text>
            <TouchableOpacity
              style={styles.favoriteButton}
              onPress={() => handleToggleFavorite(activeExercise.id)}
            >
              <Ionicons
                name={
                  (profile?.favoriteExercises || []).includes(activeExercise.id)
                    ? "heart"
                    : "heart-outline"
                }
                size={24}
                color={
                  (profile?.favoriteExercises || []).includes(activeExercise.id)
                    ? colors.danger
                    : colors.text
                }
              />
            </TouchableOpacity>
          </View>
          <View style={styles.exerciseContent}>{renderExerciseContent()}</View>
        </View>
      ) : (
        <FlatList
          style={styles.flatList}
          contentContainerStyle={styles.contentContainer}
          data={[{ key: "content" }]}
          renderItem={() => (
            <>
              <View style={styles.header}>
                <Text style={[styles.pageTitle, { color: colors.text }]}>
                  {profile?.language === "nl"
                    ? "Mindfulness Oefeningen"
                    : "Mindfulness Exercises"}
                </Text>
                <TouchableOpacity
                  style={[
                    styles.addButton,
                    { backgroundColor: colors.primary },
                  ]}
                  onPress={handleAddExercise}
                >
                  <Ionicons name="add" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              <Text style={[styles.pageDescription, { color: colors.muted }]}>
                {profile?.language === "nl"
                  ? "Gebruik deze oefeningen om je te helpen ontspannen, focus te vinden, en in het moment te blijven."
                  : "Use these exercises to help you relax, find focus, and stay in the present moment."}
              </Text>
              <View style={styles.viewTabs}>
                <TouchableOpacity
                  style={getViewTabStyle(activeView === "featured")}
                  onPress={() => setActiveView("featured")}
                >
                  <Text style={getViewTabTextStyle(activeView === "featured")}>
                    {profile?.language === "nl" ? "Uitgelicht" : "Featured"}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={getViewTabStyle(activeView === "all")}
                  onPress={() => setActiveView("all")}
                >
                  <Text style={getViewTabTextStyle(activeView === "all")}>
                    {profile?.language === "nl" ? "Alle" : "All"}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={getViewTabStyle(activeView === "favorites")}
                  onPress={() => setActiveView("favorites")}
                >
                  <Text style={getViewTabTextStyle(activeView === "favorites")}>
                    {profile?.language === "nl" ? "Favorieten" : "Favorites"}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={getViewTabStyle(activeView === "custom")}
                  onPress={() => setActiveView("custom")}
                >
                  <Text style={getViewTabTextStyle(activeView === "custom")}>
                    {profile?.language === "nl" ? "Aangepast" : "Custom"}
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={styles.exerciseList}>
                <ExerciseList
                  colors={colors}
                  language={profile?.language || "en"}
                  exercises={getExercisesToDisplay()}
                  favoriteExercises={profile?.favoriteExercises || []}
                  onSelectExercise={handleExerciseSelect}
                  onToggleFavorite={handleToggleFavorite}
                  _onEditExercise={
                    activeView === "custom" ? handleEditExercise : undefined
                  }
                  _onDeleteExercise={
                    activeView === "custom" ? handleDeleteExercise : undefined
                  }
                />
              </View>
              <View style={styles.benefitsSection}>
                <Text
                  style={[styles.benefitsSectionTitle, { color: colors.text }]}
                >
                  {profile?.language === "nl"
                    ? "Voordelen van Mindfulness"
                    : "Benefits of Mindfulness"}
                </Text>
                <FlatList
                  data={benefitsData}
                  renderItem={renderBenefitItem}
                  keyExtractor={(item, _index) => `benefit-${_index}`}
                  scrollEnabled={false}
                />
              </View>
              <View style={styles.statsSection}>
                <Text
                  style={[styles.statsSectionTitle, { color: colors.text }]}
                >
                  {profile?.language === "nl"
                    ? "Jouw Mindfulness Statistieken"
                    : "Your Mindfulness Stats"}
                </Text>
                <FlatList
                  data={statsData}
                  renderItem={renderStatItem}
                  keyExtractor={(item, _index) => `stat-${_index}`}
                  horizontal
                  scrollEnabled={false}
                  contentContainerStyle={styles.statsCards}
                />
              </View>
            </>
          )}
          keyExtractor={(item) => item.key}
        />
      )}
      <Modal
        visible={showAddExerciseModal}
        animationType="slide"
        transparent={false}
        onRequestClose={handleCloseAddExerciseModal}
      >
        <ExerciseForm
          colors={colors}
          language={profile?.language || "en"}
          onClose={handleCloseAddExerciseModal}
          editExerciseId={editExerciseId}
        />
      </Modal>
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({

  activeViewTab: {
    borderWidth: 1,
  },
  addButton: {
    alignItems: "center",
    borderRadius: 20,
    height: 40,
    justifyContent: "center",
    width: 40,
  },
  backButton: {
    padding: 8,
  },
  benefitCard: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
    padding: 16,
  },
  benefitDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  benefitsSection: {
    marginBottom: 30,
  },
  benefitsSectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 16,
  },
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 120, // Space for floating tab bar
  },
  exerciseContainer: {
    flex: 1,
  },
  exerciseContent: {
    flex: 1,
    padding: 20,
  },
  exerciseHeader: {
    alignItems: "center",
    borderBottomWidth: 1,
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  exerciseHeaderTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
  },
  exerciseList: {
    marginBottom: 30,
  },
  favoriteButton: {
    padding: 8,
  },
  flatList: {
    flex: 1,
  },
  header: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  pageDescription: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 24,
  },
  pageTitle: {
    flex: 1,
    fontSize: 24,
    fontWeight: "700",
  },
  statCard: {
    alignItems: "center",
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    width: "31%",
  },
  statLabel: {
    fontSize: 12,
    textAlign: "center",
  },
  statValue: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 4,
  },
  statsCards: {
    justifyContent: "space-between",
    width: "100%",
  },
  statsSection: {
    marginBottom: 40,
  },
  statsSectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 16,
  },
  viewTab: {
    alignItems: "center",
    borderRadius: 8,
    borderWidth: 1,
    flex: 1,
    justifyContent: "center",
    marginHorizontal: 4,
    paddingVertical: 8,
  },
  viewTabText: {
    fontSize: 14,
    fontWeight: "500",
  },
  viewTabs: {
    flexDirection: "row",
    marginBottom: 20,
  },

});