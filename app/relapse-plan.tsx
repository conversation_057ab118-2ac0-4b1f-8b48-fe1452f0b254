import React from "react";
import { View, Text, ScrollView, SafeAreaView, StyleSheet, TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";
import { useUserStore } from "@/store/user/user-store";
import { useTheme } from "@/context/theme-context";
import { useTranslation } from "@/hooks/useTranslation";
import Colors from "@/constants/colors";
import { ArrowLeft, Edit, RotateCcw } from "lucide-react-native";
import { RelapsePlanManager } from "@/components/crisis/RelapsePlanManager";

export default function RelapsePlanScreen() {
  const router = useRouter();
  const { profile } = useUserStore();
  const { currentTheme } = useTheme();
  const { t } = useTranslation();
  const colors = Colors[currentTheme];
  const [showManager, setShowManager] = React.useState(false);
  
  // Get the first relapse plan document
  const relapsePlan = profile?.documents?.filter(doc => doc.category === "RelapsePlan")?.[0];
  
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      padding: 8,
      marginRight: 12,
    },
    title: {
      fontSize: 20,
      fontWeight: '700',
      color: colors.text,
      flex: 1,
    },
    editButton: {
      padding: 8,
      backgroundColor: colors.primary + '20',
      borderRadius: 8,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      paddingVertical: 20,
    },
    planCard: {
      backgroundColor: colors.card,
      borderRadius: 16,
      padding: 20,
      marginBottom: 20,
      borderWidth: 1,
      borderColor: colors.border,
    },
    planTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 12,
    },
    planContent: {
      fontSize: 16,
      lineHeight: 24,
      color: colors.text,
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    },
    emptyIcon: {
      backgroundColor: colors.primary + '20',
      borderRadius: 32,
      padding: 20,
      marginBottom: 16,
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 22,
      marginBottom: 24,
    },
    createButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 12,
    },
    createButtonText: {
      color: '#fff',
      fontSize: 16,
      fontWeight: '600',
    },
  });
  
  if (showManager) {
    return (
      <RelapsePlanManager 
        onClose={() => setShowManager(false)}
      />
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>
          {t('dashboard.relapsePlan')}
        </Text>
        <TouchableOpacity 
          style={styles.editButton}
          onPress={() => setShowManager(true)}
        >
          <Edit size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        {relapsePlan ? (
          <View style={styles.planCard}>
            <Text style={styles.planTitle}>
              {relapsePlan.title}
            </Text>
            <Text style={styles.planContent}>
              {relapsePlan.content}
            </Text>
          </View>
        ) : (
          <View style={styles.emptyState}>
            <View style={styles.emptyIcon}>
              <RotateCcw size={32} color={colors.primary} />
            </View>
            <Text style={styles.emptyTitle}>
              {t('crisis.relapsePlans.noPlans')}
            </Text>
            <Text style={styles.emptyText}>
              {t('crisis.relapsePlans.noPlanDescription')}
            </Text>
            <TouchableOpacity 
              style={styles.createButton}
              onPress={() => setShowManager(true)}
            >
              <Text style={styles.createButtonText}>
                {t('crisis.relapsePlans.createNew')}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
} 