import React from "react";
import { useEffect } from "react";
import { Redirect } from "expo-router";
import { useUserStore } from "@/store/user/user-store";
import {
  registerForPushNotificationsAsync,
  scheduleDailyCheckinNotification,
} from "@/utils/system/notification-utils";

export default function Index() {
  const { profile, isLoading } = useUserStore();

  useEffect(() => {
    // Register for push notifications and schedule daily check-in
    const setupNotifications = async () => {
      await registerForPushNotificationsAsync();
      await scheduleDailyCheckinNotification();
    };

    if (!isLoading) {
      setupNotifications();
    }
  }, [isLoading, profile?.notificationSettings?.dailyReminders]); // Re-schedule if daily reminders setting changes

  // Wait for the store to be rehydrated
  if (isLoading) {
    return null;
  }

  // If user hasn't completed onboarding, redirect to onboarding
  if (!profile || !profile.hasCompletedOnboarding) {
    return <Redirect href="/onboarding" />;
  }

  // Otherwise, redirect to the main app
  return <Redirect href="/(tabs)/dashboard" />;
}
