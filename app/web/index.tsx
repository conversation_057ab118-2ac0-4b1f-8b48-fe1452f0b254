import React from 'react';
import { View, Text, StyleSheet, Pressable, Platform, ScrollView } from 'react-native';
import { Link } from 'expo-router';
import { useColorScheme as useNativeColorScheme } from 'react-native';

export default function WebLanding() {
  const nativeColorScheme = useNativeColorScheme();
  const textColor = nativeColorScheme === 'dark' ? '#fff' : '#000';
  
  // Redirect to app on mobile platforms
  if (Platform.OS !== 'web') {
    return <Link href="/" replace />;
  }
  
  return (
    <ScrollView style={styles.container}>
      <View style={styles.hero}>
        <Text style={[styles.heroTitle, { color: textColor }]}>
          SobrixHealth
        </Text>
        <Text style={[styles.heroSubtitle, { color: textColor }]}>
          Your comprehensive addiction recovery companion
        </Text>
        <View style={styles.ctaContainer}>
          <Pressable style={styles.ctaButton}>
            <Text style={styles.ctaButtonText}>Download on iOS</Text>
          </Pressable>
          <Pressable style={styles.ctaButton}>
            <Text style={styles.ctaButtonText}>Download on Android</Text>
          </Pressable>
        </View>
      </View>
      
      <View style={styles.featuresSection}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>Key Features</Text>
        <View style={styles.featuresGrid}>
          {/* Feature cards would go here */}
          <View style={styles.featureCard}>
            <Text style={[styles.featureTitle, { color: textColor }]}>Recovery Dashboard</Text>
            <Text style={[styles.featureDescription, { color: textColor }]}>
              Track your progress and celebrate milestones
            </Text>
          </View>
          {/* More feature cards */}
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  ctaButton: {
    backgroundColor: '#4F46E5',
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  ctaButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  ctaContainer: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 32,
  },
  featureCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    borderWidth: 1,
    padding: 24,
    width: 300,
  },
  featureDescription: {
    fontSize: 16,
    opacity: 0.8,
  },
  featureTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 24,
    justifyContent: 'center',
  },
  featuresSection: {
    padding: 40,
  },
  hero: {
    alignItems: 'center',
    padding: 40,
  },
  heroSubtitle: {
    fontSize: 24,
    marginTop: 16,
    textAlign: 'center',
  },
  heroTitle: {
    fontSize: 48,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
});