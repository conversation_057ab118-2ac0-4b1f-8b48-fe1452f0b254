import React from "react";
import { View, Text, SafeAreaView, StyleSheet, TouchableOpacity, Image, Dimensions } from "react-native";
import { useRouter } from "expo-router";
import { useUserStore, getMediaUri } from "@/store/user/user-store";
import { useTheme } from "@/context/theme-context";
import { useTranslation } from "@/hooks/useTranslation";
import Colors from "@/constants/colors";
import { ArrowLeft, Edit, CreditCard } from "lucide-react-native";
import { EmergencyCardManager } from "@/components/crisis/EmergencyCardManager";

const { width } = Dimensions.get('window');

export default function EmergencyCardScreen() {
  const router = useRouter();
  const { profile } = useUserStore();
  const { currentTheme } = useTheme();
  const { t } = useTranslation();
  const colors = Colors[currentTheme];
  const [showManager, setShowManager] = React.useState(false);
  
  // Get the first emergency card
  const emergencyCard = profile?.mediaFiles?.filter(file => file.category === "EmergencyCard")?.[0];
  const imageUri = emergencyCard ? (getMediaUri(emergencyCard.id) || emergencyCard.uri) : null;
  
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      padding: 8,
      marginRight: 12,
    },
    title: {
      fontSize: 20,
      fontWeight: '700',
      color: colors.text,
      flex: 1,
    },
    editButton: {
      padding: 8,
      backgroundColor: colors.primary + '20',
      borderRadius: 8,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      paddingVertical: 20,
    },
    cardContainer: {
      backgroundColor: colors.card,
      borderRadius: 16,
      padding: 20,
      marginBottom: 20,
      borderWidth: 1,
      borderColor: colors.border,
      alignItems: 'center',
    },
    cardTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    cardImage: {
      width: width - 80,
      height: (width - 80) * 0.6, // Maintain aspect ratio
      borderRadius: 12,
      backgroundColor: colors.border,
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    },
    emptyIcon: {
      backgroundColor: colors.primary + '20',
      borderRadius: 32,
      padding: 20,
      marginBottom: 16,
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 22,
      marginBottom: 24,
    },
    createButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 12,
    },
    createButtonText: {
      color: '#fff',
      fontSize: 16,
      fontWeight: '600',
    },
  });
  
  if (showManager) {
    return (
      <EmergencyCardManager 
        onClose={() => setShowManager(false)}
      />
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>
          {t('dashboard.emergencyCard')}
        </Text>
        <TouchableOpacity 
          style={styles.editButton}
          onPress={() => setShowManager(true)}
        >
          <Edit size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.content}>
        {emergencyCard && imageUri ? (
          <View style={styles.cardContainer}>
            <Text style={styles.cardTitle}>
              {emergencyCard.title || t('dashboard.emergencyCard')}
            </Text>
            <Image 
              source={{ uri: imageUri }}
              style={styles.cardImage}
              resizeMode="contain"
            />
          </View>
        ) : (
          <View style={styles.emptyState}>
            <View style={styles.emptyIcon}>
              <CreditCard size={32} color={colors.primary} />
            </View>
            <Text style={styles.emptyTitle}>
              {t('crisis.emergencyCards.newCard')}
            </Text>
            <Text style={styles.emptyText}>
              {t('crisis.emergencyCards.newCardDescription')}
            </Text>
            <TouchableOpacity 
              style={styles.createButton}
              onPress={() => setShowManager(true)}
            >
              <Text style={styles.createButtonText}>
                {t('crisis.emergencyCards.newCard')}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
} 