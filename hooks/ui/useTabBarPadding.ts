import { Platform } from "react-native";

export const useTabBarPadding = () => {
  // Height of the floating tab bar (68) + margin from bottom + safe area
  const tabBarHeight = 68;
  const bottomMargin = Platform.OS === "ios" ? 30 : 16;
  const additionalPadding = 20; // Extra space for better UX
  const paddingBottom = tabBarHeight + bottomMargin + additionalPadding;
  
  return {
    paddingBottom,
    tabBarHeight,
    bottomMargin,
  };
};