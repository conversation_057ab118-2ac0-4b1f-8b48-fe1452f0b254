import { useColorScheme as useNativeColorScheme } from 'react-native';
import Colors from '@/constants/colors';

export interface ColorScheme {
  // Primary brand colors
  primary: string;
  primaryLight: string;
  primaryDark: string;
  
  // Secondary colors
  secondary: string;
  secondaryLight: string;
  secondaryDark: string;
  
  // Accent colors
  accent: string;
  accentLight: string;
  accentDark: string;
  
  // Background colors
  background: string;
  backgroundSecondary: string;
  card: string;
  cardSecondary: string;
  cardGradient: string;
  
  // Text colors
  text: string;
  textSecondary: string;
  textTertiary: string;
  
  // Border colors
  border: string;
  borderLight: string;
  borderDark: string;
  
  // Status colors
  success: string;
  successLight: string;
  successDark: string;
  
  warning: string;
  warningLight: string;
  warningDark: string;
  
  danger: string;
  dangerLight: string;
  dangerDark: string;
  
  info: string;
  infoLight: string;
  infoDark: string;
  
  // Utility colors
  muted: string;
  mutedLight: string;
  mutedDark: string;
  
  notification: string;
  
  // Glassmorphism
  glass: string;
  glassSecondary: string;
  
  // Shadows
  shadow: string;
  shadowLight: string;
  shadowDark: string;
  
  gray: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  };
}

export function useColorScheme(): ColorScheme {
  const colorScheme = useNativeColorScheme();
  return Colors[colorScheme ?? 'light'];
}