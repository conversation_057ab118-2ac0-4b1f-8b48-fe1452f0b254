import { useState, useRef, useEffect } from 'react';

export type ExerciseState = 'idle' | 'running' | 'paused';

export interface MindfulnessTimerConfig {
  onDurationUpdate?: (seconds: number) => void;
}

export interface MindfulnessTimerReturn {
  exerciseState: ExerciseState;
  timerSeconds: number;
  setExerciseState: (state: ExerciseState) => void;
  toggleTimer: () => void;
  resetTimer: () => void;
  formatTime: (seconds: number) => string;
  timerRunning: boolean;
}

export const useMindfulnessTimer = (
  config: MindfulnessTimerConfig = {}
): MindfulnessTimerReturn => {
  const { onDurationUpdate } = config;
  
  const [exerciseState, setExerciseState] = useState<ExerciseState>('idle');
  const [timerSeconds, setTimerSeconds] = useState(0);
  const timerRef = useRef<number | null>(null);
  
  const timerRunning = exerciseState === 'running';

  // Timer effect
  useEffect(() => {
    if (timerRunning) {
      timerRef.current = setInterval(() => {
        setTimerSeconds(prev => prev + 1);
      }, 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [timerRunning]);

  // Effect to report duration when timerSeconds changes
  useEffect(() => {
    onDurationUpdate?.(timerSeconds);
  }, [timerSeconds, onDurationUpdate]);

  const toggleTimer = () => {
    if (exerciseState === 'idle') {
      setExerciseState('running');
    } else if (exerciseState === 'running') {
      setExerciseState('paused');
    } else if (exerciseState === 'paused') {
      setExerciseState('running');
    }
  };

  const resetTimer = () => {
    setExerciseState('idle');
    setTimerSeconds(0);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return {
    exerciseState,
    timerSeconds,
    setExerciseState,
    toggleTimer,
    resetTimer,
    formatTime,
    timerRunning,
  };
}; 