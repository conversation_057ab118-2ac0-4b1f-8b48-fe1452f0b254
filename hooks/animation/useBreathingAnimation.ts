import { useRef, useEffect, useCallback } from "react";
import { Animated, Easing } from "react-native";

export type BreathingPhase = "inhale" | "hold" | "exhale" | "rest";

export interface BreathingAnimationConfig {
  isRunning?: boolean;
  phase?: BreathingPhase;
  inhaleDuration?: number;
  holdDuration?: number;
  exhaleDuration?: number;
  restDuration?: number;
  minSize?: number;
  maxSize?: number;
  minAuraOpacity?: number;
  maxAuraOpacity?: number;
  useNativeDriver?: boolean;
}

export interface BreathingAnimationReturn {
  circleSize: Animated.Value;
  auraOpacity: Animated.Value;
  circleStyle: {
    width: Animated.Value;
    height: Animated.Value;
  };
  auraStyle: {
    opacity: Animated.Value;
  };
  resetAnimation: () => void;
  stopAnimation: () => void;
}

export const useBreathingAnimation = (
  config: BreathingAnimationConfig = {}
): BreathingAnimationReturn => {
  const {
    isRunning = false,
    phase = "inhale",
    inhaleDuration = 4000,
    holdDuration = 4000,
    exhaleDuration = 4000,
    restDuration = 2000,
    minSize = 100,
    maxSize = 200,
    minAuraOpacity = 0.1,
    maxAuraOpacity = 0.5,
    useNativeDriver = false,
  } = config;

  const circleSize = useRef(new Animated.Value(minSize)).current;
  const auraOpacity = useRef(new Animated.Value(minAuraOpacity)).current;

  const animatePhase = useCallback(
    (
      targetPhase: BreathingPhase,
      onComplete?: () => void
    ): Animated.CompositeAnimation => {
      const easing = Easing.inOut(Easing.quad);
      let duration: number;
      let circleTarget: number;
      let auraTarget: number;

      switch (targetPhase) {
        case "inhale":
          duration = inhaleDuration;
          circleTarget = maxSize;
          auraTarget = maxAuraOpacity;
          break;
        case "hold":
          duration = holdDuration;
          circleTarget = maxSize;
          auraTarget = maxAuraOpacity;
          break;
        case "exhale":
          duration = exhaleDuration;
          circleTarget = minSize;
          auraTarget = minAuraOpacity;
          break;
        case "rest":
          duration = restDuration;
          circleTarget = minSize;
          auraTarget = minAuraOpacity;
          break;
        default:
          duration = inhaleDuration;
          circleTarget = maxSize;
          auraTarget = maxAuraOpacity;
      }

      const animation = Animated.parallel([
        Animated.timing(circleSize, {
          toValue: circleTarget,
          duration,
          useNativeDriver,
          easing,
        }),
        Animated.timing(auraOpacity, {
          toValue: auraTarget,
          duration,
          useNativeDriver,
          easing,
        }),
      ]);

      if (onComplete) {
        animation.start(onComplete);
      }

      return animation;
    },
    [
      circleSize,
      auraOpacity,
      inhaleDuration,
      holdDuration,
      exhaleDuration,
      restDuration,
      minSize,
      maxSize,
      minAuraOpacity,
      maxAuraOpacity,
      useNativeDriver,
    ]
  );

  useEffect(() => {
    if (isRunning) {
      const animation = animatePhase(phase);
      animation.start();
      return () => animation.stop();
    } else {
      circleSize.stopAnimation();
      auraOpacity.stopAnimation();
    }
  }, [isRunning, phase, animatePhase, circleSize, auraOpacity]);

  const resetAnimation = () => {
    circleSize.setValue(minSize);
    auraOpacity.setValue(minAuraOpacity);
  };

  const stopAnimation = () => {
    circleSize.stopAnimation();
    auraOpacity.stopAnimation();
  };

  const circleStyle = {
    width: circleSize,
    height: circleSize,
  };

  const auraStyle = {
    opacity: auraOpacity,
  };

  return {
    circleSize,
    auraOpacity,
    circleStyle,
    auraStyle,
    resetAnimation,
    stopAnimation,
  };
};
