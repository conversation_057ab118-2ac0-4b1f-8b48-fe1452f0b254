import { useRef, useEffect, useCallback } from "react";
import { Animated, Easing } from "react-native";

export interface FadeSlideAnimationConfig {
  duration?: number;
  delay?: number;
  slideDistance?: number;
  easing?: (value: number) => number;
  useNativeDriver?: boolean;
}

export interface FadeSlideAnimationReturn {
  fadeAnim: Animated.Value;
  slideAnim: Animated.Value;
  animatedStyle: {
    opacity: Animated.Value;
    transform: { translateY: Animated.Value }[];
  };
  startAnimation: () => void;
  resetAnimation: () => void;
}

export const useFadeSlideAnimation = (
  config: FadeSlideAnimationConfig = {}
): FadeSlideAnimationReturn => {
  const {
    duration = 600,
    delay = 0,
    slideDistance = 30,
    easing = Easing.out(Easing.cubic),
    useNativeDriver = true,
  } = config;

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(slideDistance)).current;

  const startAnimation = useCallback(() => {
    const animations = [
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration,
        useNativeDriver,
        easing,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration,
        useNativeDriver,
        easing,
      }),
    ];

    if (delay > 0) {
      Animated.sequence([
        Animated.delay(delay),
        Animated.parallel(animations),
      ]).start();
    } else {
      Animated.parallel(animations).start();
    }
  }, [fadeAnim, slideAnim, duration, useNativeDriver, easing, delay]);

  const resetAnimation = () => {
    fadeAnim.setValue(0);
    slideAnim.setValue(slideDistance);
  };

  useEffect(() => {
    startAnimation();
  }, [startAnimation]);

  const animatedStyle = {
    opacity: fadeAnim,
    transform: [{ translateY: slideAnim }],
  };

  return {
    fadeAnim,
    slideAnim,
    animatedStyle,
    startAnimation,
    resetAnimation,
  };
};
