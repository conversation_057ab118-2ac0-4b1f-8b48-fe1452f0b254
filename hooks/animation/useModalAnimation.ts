import { useRef, useCallback } from "react";
import { Animated, Easing } from "react-native";

export interface ModalAnimationConfig {
  duration?: number;
  easing?: (value: number) => number;
  useNativeDriver?: boolean;
  initialScale?: number;
}

export interface ModalAnimationReturn {
  modalScale: Animated.Value;
  modalOpacity: Animated.Value;
  animatedStyle: {
    opacity: Animated.Value;
    transform: { scale: Animated.Value }[];
  };
  showModal: () => Promise<void>;
  hideModal: () => Promise<void>;
  resetAnimation: () => void;
}

export const useModalAnimation = (
  config: ModalAnimationConfig = {}
): ModalAnimationReturn => {
  const {
    duration = 300,
    easing = Easing.out(Easing.cubic),
    useNativeDriver = true,
    initialScale = 0.8,
  } = config;

  const modalScale = useRef(new Animated.Value(initialScale)).current;
  const modalOpacity = useRef(new Animated.Value(0)).current;

  const showModal = useCallback((): Promise<void> => {
    return new Promise((resolve) => {
      Animated.parallel([
        Animated.timing(modalScale, {
          toValue: 1,
          duration,
          useNativeDriver,
          easing,
        }),
        Animated.timing(modalOpacity, {
          toValue: 1,
          duration,
          useNativeDriver,
          easing,
        }),
      ]).start(() => resolve());
    });
  }, [modalScale, modalOpacity, duration, useNativeDriver, easing]);

  const hideModal = useCallback((): Promise<void> => {
    return new Promise((resolve) => {
      Animated.parallel([
        Animated.timing(modalScale, {
          toValue: initialScale,
          duration,
          useNativeDriver,
          easing,
        }),
        Animated.timing(modalOpacity, {
          toValue: 0,
          duration,
          useNativeDriver,
          easing,
        }),
      ]).start(() => resolve());
    });
  }, [
    modalScale,
    modalOpacity,
    duration,
    useNativeDriver,
    easing,
    initialScale,
  ]);

  const resetAnimation = () => {
    modalScale.setValue(initialScale);
    modalOpacity.setValue(0);
  };

  const animatedStyle = {
    opacity: modalOpacity,
    transform: [{ scale: modalScale }],
  };

  return {
    modalScale,
    modalOpacity,
    animatedStyle,
    showModal,
    hideModal,
    resetAnimation,
  };
};
