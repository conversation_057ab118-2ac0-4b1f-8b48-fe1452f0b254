import { useRef, useEffect, useCallback } from "react";
import { Animated, Easing } from "react-native";
export interface StaggeredAnimationConfig {
  itemCount: number;
  staggerDelay?: number;
  duration?: number;
  easing?: (value: number) => number;
  useNativeDriver?: boolean;
  initialScale?: number;
}
export interface StaggeredAnimationItem {
  fade: Animated.Value;
  scale: Animated.Value;
  animatedStyle: {
    opacity: Animated.Value;
    transform: { scale: Animated.Value }[];
  };
}
export interface StaggeredAnimationReturn {
  items: StaggeredAnimationItem[];
  startAnimation: () => void;
  resetAnimation: () => void;
}
export const useStaggeredAnimation = (
  config: StaggeredAnimationConfig
): StaggeredAnimationReturn => {
  const {
    itemCount,
    staggerDelay = 50,
    duration = 400,
    easing = Easing.out(Easing.cubic),
    useNativeDriver = true,
    initialScale = 0.8,
  } = config;
  // Create animation values for each item
  const items = useRef(
    Array(itemCount)
      .fill(0)
      .map(() => ({
        fade: new Animated.Value(0),
        scale: new Animated.Value(initialScale),
      }))
  ).current;
  const startAnimation = useCallback(() => {
    const animations = items.map((item, index) => {
      const delay = index * staggerDelay;
      return Animated.sequence([
        Animated.delay(delay),
        Animated.parallel([
          Animated.timing(item.fade, {
            toValue: 1,
            duration,
            useNativeDriver,
            easing,
          }),
          Animated.spring(item.scale, {
            toValue: 1,
            friction: 7,
            tension: 40,
            useNativeDriver,
          }),
        ]),
      ]);
    });
    Animated.parallel(animations).start();
  }, [items, staggerDelay, duration, useNativeDriver, easing]);
  const resetAnimation = () => {
    items.forEach((item) => {
      item.fade.setValue(0);
      item.scale.setValue(initialScale);
    });
  };
  useEffect(() => {
    startAnimation();
  }, [itemCount, startAnimation]);
  // Create the return object with animated styles
  const itemsWithStyles: StaggeredAnimationItem[] = items.map((item) => ({
    fade: item.fade,
    scale: item.scale,
    animatedStyle: {
      opacity: item.fade,
      transform: [{ scale: item.scale }],
    },
  }));
  return {
    items: itemsWithStyles,
    startAnimation,
    resetAnimation,
  };
};