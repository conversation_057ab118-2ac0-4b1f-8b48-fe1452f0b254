import { useRef, useCallback } from 'react';
import { Animated, Platform } from 'react-native';
import * as Haptics from 'expo-haptics';

export const useCardPressAnimation = () => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const shadowAnim = useRef(new Animated.Value(1)).current;

  const animatePress = useCallback(() => {
    // Haptic feedback
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    // Scale down and reduce shadow
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 0.95,
        tension: 300,
        friction: 10,
        useNativeDriver: true,
      }),
      Animated.timing(shadowAnim, {
        toValue: 0.5,
        duration: 100,
        useNativeDriver: false,
      }),
    ]).start();
  }, [scaleAnim, shadowAnim]);

  const animateRelease = useCallback(() => {
    // Scale back up and restore shadow
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 300,
        friction: 10,
        useNativeDriver: true,
      }),
      Animated.timing(shadowAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: false,
      }),
    ]).start();
  }, [scaleAnim, shadowAnim]);

  return {
    animatedStyle: {
      transform: [{ scale: scaleAnim }],
    },
    shadowStyle: {
      shadowOpacity: shadowAnim,
    },
    animatePress,
    animateRelease,
  };
};

export const useCardHoverAnimation = () => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const shadowAnim = useRef(new Animated.Value(1)).current;
  const translateYAnim = useRef(new Animated.Value(0)).current;

  const animateHoverIn = useCallback(() => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1.02,
        tension: 200,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(shadowAnim, {
        toValue: 1.5,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(translateYAnim, {
        toValue: -2,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  }, [scaleAnim, shadowAnim, translateYAnim]);

  const animateHoverOut = useCallback(() => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 200,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(shadowAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(translateYAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  }, [scaleAnim, shadowAnim, translateYAnim]);

  return {
    animatedStyle: {
      transform: [
        { scale: scaleAnim },
        { translateY: translateYAnim },
      ],
    },
    shadowStyle: {
      shadowOpacity: shadowAnim,
    },
    animateHoverIn,
    animateHoverOut,
  };
};

export const useCardShimmerAnimation = () => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  const startShimmer = useCallback(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [shimmerAnim]);

  const stopShimmer = useCallback(() => {
    shimmerAnim.stopAnimation();
    shimmerAnim.setValue(0);
  }, [shimmerAnim]);

  const shimmerStyle = {
    opacity: shimmerAnim.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: [0.3, 1, 0.3],
    }),
  };

  return {
    shimmerStyle,
    startShimmer,
    stopShimmer,
  };
};

export const useCardBounceAnimation = () => {
  const bounceAnim = useRef(new Animated.Value(1)).current;

  const triggerBounce = useCallback(() => {
    Animated.sequence([
      Animated.timing(bounceAnim, {
        toValue: 1.1,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.spring(bounceAnim, {
        toValue: 1,
        tension: 300,
        friction: 10,
        useNativeDriver: true,
      }),
    ]).start();
  }, [bounceAnim]);

  return {
    animatedStyle: {
      transform: [{ scale: bounceAnim }],
    },
    triggerBounce,
  };
};

export const useCardSlideAnimation = () => {
  const translateXAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  const slideOut = useCallback((direction: 'left' | 'right' = 'right') => {
    const toValue = direction === 'right' ? 300 : -300;
    
    Animated.parallel([
      Animated.timing(translateXAnim, {
        toValue,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [translateXAnim, opacityAnim]);

  const slideIn = useCallback(() => {
    translateXAnim.setValue(300);
    opacityAnim.setValue(0);
    
    Animated.parallel([
      Animated.spring(translateXAnim, {
        toValue: 0,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [translateXAnim, opacityAnim]);

  return {
    animatedStyle: {
      transform: [{ translateX: translateXAnim }],
      opacity: opacityAnim,
    },
    slideOut,
    slideIn,
  };
}; 