import { useRef, useCallback } from "react";
import { Animated, Easing } from "react-native";

export interface TextFadeAnimationConfig {
  fadeDuration?: number;
  easing?: (value: number) => number;
  useNativeDriver?: boolean;
}

export interface TextFadeAnimationReturn {
  textOpacity: Animated.Value;
  animatedStyle: {
    opacity: Animated.Value;
  };
  fadeToNewText: (
    newText: string,
    onTextChange: (text: string) => void
  ) => void;
  resetAnimation: () => void;
}

export const useTextFadeAnimation = (
  config: TextFadeAnimationConfig = {}
): TextFadeAnimationReturn => {
  const {
    fadeDuration = 200,
    easing = Easing.linear,
    useNativeDriver = false,
  } = config;

  const textOpacity = useRef(new Animated.Value(1)).current;

  const fadeToNewText = useCallback(
    (newText: string, onTextChange: (text: string) => void) => {
      Animated.sequence([
        Animated.timing(textOpacity, {
          toValue: 0,
          duration: fadeDuration,
          useNativeDriver,
          easing,
        }),
        Animated.timing(textOpacity, {
          toValue: 1,
          duration: fadeDuration,
          useNativeDriver,
          easing,
        }),
      ]).start();

      // Change text at the midpoint of the animation
      setTimeout(() => {
        onTextChange(newText);
      }, fadeDuration);
    },
    [textOpacity, fadeDuration, useNativeDriver, easing]
  );

  const resetAnimation = () => {
    textOpacity.setValue(1);
  };

  const animatedStyle = {
    opacity: textOpacity,
  };

  return {
    textOpacity,
    animatedStyle,
    fadeToNewText,
    resetAnimation,
  };
};
