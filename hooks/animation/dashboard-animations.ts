import { useRef, useEffect } from 'react';
import { Animated, Easing } from 'react-native';
export const useFadeInAnimation = (delay: number = 0) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(30)).current;
  useEffect(() => {
    const timer = setTimeout(() => {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 600,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: 0,
          duration: 600,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
      ]).start();
    }, delay);
    return () => clearTimeout(timer);
  }, [fadeAnim, translateY, delay]);
  return {
    opacity: fadeAnim,
    transform: [{ translateY }],
  };
};
export const useScaleAnimation = (delay: number = 0) => {
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  useEffect(() => {
    const timer = setTimeout(() => {
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 400,
          easing: Easing.out(Easing.quad),
          useNativeDriver: true,
        }),
      ]).start();
    }, delay);
    return () => clearTimeout(timer);
  }, [scaleAnim, fadeAnim, delay]);
  return {
    opacity: fadeAnim,
    transform: [{ scale: scaleAnim }],
  };
};
export const useSlideInAnimation = (direction: 'left' | 'right' = 'left', delay: number = 0) => {
  const translateX = useRef(new Animated.Value(direction === 'left' ? -100 : 100)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  useEffect(() => {
    const timer = setTimeout(() => {
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: 0,
          duration: 500,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          easing: Easing.out(Easing.quad),
          useNativeDriver: true,
        }),
      ]).start();
    }, delay);
    return () => clearTimeout(timer);
  }, [translateX, fadeAnim, delay, direction]);
  return {
    opacity: fadeAnim,
    transform: [{ translateX }],
  };
};
export const usePulseAnimation = () => {
  const pulseAnim = useRef(new Animated.Value(1)).current;
  useEffect(() => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 1000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };
    pulse();
  }, [pulseAnim]);
  return {
    transform: [{ scale: pulseAnim }],
  };
};
export const useStaggeredAnimation = (itemCount: number, staggerDelay: number = 100) => {
  const animations = useRef(
    Array.from({ length: itemCount }, () => ({
      fadeAnim: new Animated.Value(0),
      translateY: new Animated.Value(20),
    }))
  ).current;
  useEffect(() => {
    const animationPromises = animations.map((anim, index) => {
      return new Promise<void>((resolve) => {
        setTimeout(() => {
          Animated.parallel([
            Animated.timing(anim.fadeAnim, {
              toValue: 1,
              duration: 400,
              easing: Easing.out(Easing.quad),
              useNativeDriver: true,
            }),
            Animated.timing(anim.translateY, {
              toValue: 0,
              duration: 400,
              easing: Easing.out(Easing.quad),
              useNativeDriver: true,
            }),
          ]).start(() => resolve());
        }, index * staggerDelay);
      });
    });
    Promise.all(animationPromises);
  }, [animations, staggerDelay]);
  return animations.map((anim) => ({
    opacity: anim.fadeAnim,
    transform: [{ translateY: anim.translateY }],
  }));
}; 