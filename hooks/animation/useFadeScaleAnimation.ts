import { useRef, useEffect, useCallback } from "react";
import { Animated, Easing } from "react-native";

export interface FadeScaleAnimationConfig {
  duration?: number;
  delay?: number;
  easing?: (value: number) => number;
  useNativeDriver?: boolean;
}

export interface FadeScaleAnimationReturn {
  fadeAnim: Animated.Value;
  scaleAnim: Animated.Value;
  animatedStyle: {
    opacity: Animated.Value;
    transform: { scale: Animated.Value }[];
  };
  startAnimation: () => void;
  resetAnimation: () => void;
}

export const useFadeScaleAnimation = (
  config: FadeScaleAnimationConfig = {}
): FadeScaleAnimationReturn => {
  const {
    duration = 800,
    delay = 0,
    easing = Easing.out(Easing.cubic),
    useNativeDriver = true,
  } = config;

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  const startAnimation = useCallback(() => {
    const animations = [
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration,
        useNativeDriver,
        easing,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver,
      }),
    ];

    if (delay > 0) {
      Animated.sequence([
        Animated.delay(delay),
        Animated.parallel(animations),
      ]).start();
    } else {
      Animated.parallel(animations).start();
    }
  }, [fadeAnim, scaleAnim, duration, useNativeDriver, easing, delay]);

  const resetAnimation = () => {
    fadeAnim.setValue(0);
    scaleAnim.setValue(0.95);
  };

  useEffect(() => {
    startAnimation();
  }, [startAnimation]);

  const animatedStyle = {
    opacity: fadeAnim,
    transform: [{ scale: scaleAnim }],
  };

  return {
    fadeAnim,
    scaleAnim,
    animatedStyle,
    startAnimation,
    resetAnimation,
  };
};
