// Animation Hooks
export { useFadeScaleAnimation } from "./useFadeScaleAnimation";
export { useFadeSlideAnimation } from "./useFadeSlideAnimation";
export { useStaggeredAnimation } from "./useStaggeredAnimation";
export { useTextFadeAnimation } from "./useTextFadeAnimation";
export { useModalAnimation } from "./useModalAnimation";
export { useBreathingAnimation } from "./useBreathingAnimation";

// Re-export types for convenience
export type {
  FadeScaleAnimationConfig,
  FadeScaleAnimationReturn,
} from "./useFadeScaleAnimation";

export type {
  FadeSlideAnimationConfig,
  FadeSlideAnimationReturn,
} from "./useFadeSlideAnimation";

export type {
  StaggeredAnimationConfig,
  StaggeredAnimationReturn,
  StaggeredAnimationItem,
} from "./useStaggeredAnimation";

export type {
  TextFadeAnimationConfig,
  TextFadeAnimationReturn,
} from "./useTextFadeAnimation";

export type {
  ModalAnimationConfig,
  ModalAnimationReturn,
} from "./useModalAnimation";

export type {
  BreathingAnimationConfig,
  BreathingAnimationReturn,
  BreathingPhase,
} from "./useBreathingAnimation"; 