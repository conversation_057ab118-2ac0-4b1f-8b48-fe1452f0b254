import { useEffect, useState } from 'react';
import { migrationService, MigrationResult } from '@/services/migration-service';
import { useDatabase } from './use-database';

export interface MigrationState {
  isChecking: boolean;
  isMigrating: boolean;
  needsMigration: boolean;
  migrationResult: MigrationResult | null;
  error: string | null;
  runMigration: () => Promise<void>;
  checkMigrationStatus: () => Promise<void>;
}

export const useMigration = (): MigrationState => {
  const [isChecking, setIsChecking] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);
  const [needsMigration, setNeedsMigration] = useState(false);
  const [migrationResult, setMigrationResult] = useState<MigrationResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const { isInitialized } = useDatabase();

  const checkMigrationStatus = async () => {
    if (!isInitialized) return;
    
    try {
      setIsChecking(true);
      setError(null);
      
      const needsMigration = await migrationService.isMigrationNeeded();
      setNeedsMigration(needsMigration);
      
      if (needsMigration) {
        console.log('Migration needed - AsyncStorage data found');
      } else {
        console.log('No migration needed');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to check migration status';
      setError(errorMessage);
      console.error('Migration check failed:', err);
    } finally {
      setIsChecking(false);
    }
  };

  const runMigration = async () => {
    if (!isInitialized) {
      setError('Database not initialized');
      return;
    }

    try {
      setIsMigrating(true);
      setError(null);
      
      console.log('Starting migration...');
      const result = await migrationService.migrateAllStores();
      setMigrationResult(result);
      
      if (result.success) {
        setNeedsMigration(false);
        console.log('Migration completed successfully');
      } else {
        setError(`Migration completed with errors: ${result.errors.join(', ')}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Migration failed';
      setError(errorMessage);
      console.error('Migration failed:', err);
    } finally {
      setIsMigrating(false);
    }
  };

  // Check migration status when database is initialized
  useEffect(() => {
    if (isInitialized) {
      checkMigrationStatus();
    }
  }, [isInitialized]);

  return {
    isChecking,
    isMigrating,
    needsMigration,
    migrationResult,
    error,
    runMigration,
    checkMigrationStatus
  };
}; 