import { useEffect, useState } from 'react';
import { initDatabase, simpleDatabaseService } from '@/services/database-simple';

export interface DatabaseState {
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  service: typeof simpleDatabaseService;
}

export const useDatabase = (): DatabaseState => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeDatabase = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        await initDatabase();
        setIsInitialized(true);
        
        console.log('Database initialized successfully');
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize database';
        setError(errorMessage);
        console.error('Database initialization failed:', err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeDatabase();
  }, []);

  return {
    isInitialized,
    isLoading,
    error,
    service: simpleDatabaseService
  };
};

// Hook for database migration
export const useDatabaseMigration = () => {
  const [isMigrating, setIsMigrating] = useState(false);
  const [migrationError, setMigrationError] = useState<string | null>(null);
  const [migrationComplete, setMigrationComplete] = useState(false);

  const migrateData = async () => {
    try {
      setIsMigrating(true);
      setMigrationError(null);
      
      await simpleDatabaseService.migrateFromAsyncStorage();
      setMigrationComplete(true);
      
      console.log('Data migration completed successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Migration failed';
      setMigrationError(errorMessage);
      console.error('Migration failed:', err);
    } finally {
      setIsMigrating(false);
    }
  };

  return {
    isMigrating,
    migrationError,
    migrationComplete,
    migrateData
  };
}; 