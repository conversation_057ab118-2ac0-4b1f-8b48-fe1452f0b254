import { useEffect } from 'react';
import { useUserStore } from '@/store/user/user-store';
import { 
  translationService, 
  type NestedTranslationKey, 
  type InterpolationValues,
  type SupportedLanguage 
} from '@/services/translation';

/**
 * Hook for using translations in React components
 * Automatically syncs with user's language preference from user store
 */
export function useTranslation() {
  const { profile } = useUserStore();
  
  // Sync translation service with user's language preference
  useEffect(() => {
    const userLanguage = profile?.language as SupportedLanguage;
    if (userLanguage && ['en', 'nl'].includes(userLanguage)) {
      translationService.setLanguage(userLanguage);
    } else {
      translationService.setLanguage('en'); // Default fallback
    }
  }, [profile?.language]);

  /**
   * Translate a key with optional interpolation
   */
  const t = (key: NestedTranslationKey, interpolation?: InterpolationValues): string => {
    return translationService.translate(key, interpolation);
  };

  /**
   * Get mood label for a numeric value
   */
  const getMoodLabel = (value: number): string => {
    return translationService.getMoodLabel(value);
  };

  /**
   * Get craving label for a numeric value
   */
  const getCravingLabel = (value: number): string => {
    return translationService.getCravingLabel(value);
  };

  /**
   * Get health metric label with interpolation
   */
  const getHealthMetricLabel = (metric: string, value: number): string => {
    return translationService.getHealthMetricLabel(metric, value);
  };

  /**
   * Get contact category name
   */
  const getContactCategoryName = (category: string): string => {
    return translationService.getContactCategoryName(category);
  };

  /**
   * Get common triggers array
   */
  const getCommonTriggers = (): string[] => {
    return translationService.getCommonTriggers();
  };

  /**
   * Get current language
   */
  const getCurrentLanguage = (): SupportedLanguage => {
    return translationService.getLanguage();
  };

  /**
   * Get available languages
   */
  const getAvailableLanguages = (): SupportedLanguage[] => {
    return translationService.getAvailableLanguages();
  };

  return {
    t,
    getMoodLabel,
    getCravingLabel,
    getHealthMetricLabel,
    getContactCategoryName,
    getCommonTriggers,
    getCurrentLanguage,
    getAvailableLanguages,
    currentLanguage: translationService.getLanguage(),
  };
} 