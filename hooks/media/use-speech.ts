import { useState, useEffect, useCallback, useRef } from 'react';
import { Platform } from 'react-native';
import { speak, stop } from '@/utils/media/speech-utils';
export interface UseSpeechOptions {
  language?: string;
  rate?: number;
  autoResume?: boolean;
}
export const useSpeech = (options: UseSpeechOptions = {}) => {
  const { 
    language = 'en-US', 
    rate = 0.9,
    autoResume = true
  } = options;
  
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [_error, setError] = useState<string | null>(null);
  
  const currentTextRef = useRef<string>('');
  const speakTimeoutRef = useRef<number | null>(null);
  
  // Check if speech synthesis is available
  useEffect(() => {
    // For expo-speech, we'll assume it's available and handle errors in the speak function
    setIsAvailable(true);
    
    return () => {
      // Clean up any ongoing speech when component unmounts
      if (isSpeaking) {
        stop();
      }
      
      if (speakTimeoutRef.current) {
        clearTimeout(speakTimeoutRef.current);
      }
    };
  }, [isSpeaking]);
  
  const speakText = useCallback(async (text: string, delay: number = 0) => {
    try {
      // Clear any existing timeout
      if (speakTimeoutRef.current) {
        clearTimeout(speakTimeoutRef.current);
      }
      
      // Store the current text
      currentTextRef.current = text;
      
      // Stop any ongoing speech
      await stop();
      
      if (delay > 0) {
        speakTimeoutRef.current = setTimeout(() => {
          speak(text, {
            language: language,
            rate: rate,
            onStart: () => setTimeout(() => setIsSpeaking(true), 0),
            onDone: () => setTimeout(() => setIsSpeaking(false), 0),
            onStopped: () => setTimeout(() => setIsSpeaking(false), 0),
            onError: (_err) => setTimeout(() => {
              setError(`Speech error: ${_err instanceof Error ? _err.message : String(_err)}`);
              setIsSpeaking(false);
            }, 0)
          });
        }, delay);
      } else {
        await speak(text, {
          language: language,
          rate: rate,
          onStart: () => setTimeout(() => setIsSpeaking(true), 0),
          onDone: () => setTimeout(() => setIsSpeaking(false), 0),
          onStopped: () => setTimeout(() => setIsSpeaking(false), 0),
          onError: (_err) => setTimeout(() => {
            setError(`Speech error: ${_err instanceof Error ? _err.message : _err}`);
            setIsSpeaking(false);
          }, 0)
        });
      }
    } catch (_err) {
      setError(`Failed to speak: ${_err instanceof Error ? _err.message : _err}`);
      setIsSpeaking(false);
    }
  }, [language, rate]);
  
  const pauseSpeech = useCallback(async () => {
    if (Platform.OS === 'web' && 'speechSynthesis' in window) {
      window.speechSynthesis.pause();
      setIsPaused(true);
    } else {
      // Mobile doesn't support pause, so we'll stop instead
      await stop();
      setIsSpeaking(false);
    }
  }, []);
  
  const resumeSpeech = useCallback(async () => {
    if (Platform.OS === 'web' && 'speechSynthesis' in window && isPaused) {
      window.speechSynthesis.resume();
      setIsPaused(false);
    } else if (autoResume && currentTextRef.current) {
      // For mobile, we need to restart the speech
      await speakText(currentTextRef.current);
    }
  }, [isPaused, autoResume, speakText]);
  
  const stopSpeech = useCallback(async () => {
    await stop();
    setIsSpeaking(false);
    setIsPaused(false);
  }, []);
  
  return {
    speakText,
    pauseSpeech,
    resumeSpeech,
    stopSpeech,
    isSpeaking,
    isPaused,
    isAvailable,
    error: _error,
  };
};