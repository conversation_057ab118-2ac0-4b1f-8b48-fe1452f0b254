import { useState } from 'react';
import { Platform } from 'react-native';

interface DebugConfig {
  enabled: boolean;
  showConsoleLog: boolean;
  showStorageInfo: boolean;
  showPerformanceMetrics: boolean;
}

const defaultConfig: DebugConfig = {
  enabled: __DEV__, // Only enable in development by default
  showConsoleLog: true,
  showStorageInfo: true,
  showPerformanceMetrics: false,
};

export const useDebug = () => {
  const [config, setConfig] = useState<DebugConfig>(defaultConfig);
  const [isDebugPanelVisible, setIsDebugPanelVisible] = useState(false);

  const log = (message: string, data?: unknown) => {
    if (config.enabled && config.showConsoleLog) {
      console.log(`[DEBUG] ${message}`, data || '');
    }
  };

  const logError = (message: string, error?: unknown) => {
    if (config.enabled) {
      console.error(`[DEBUG ERROR] ${message}`, error || '');
    }
  };

  const logWarning = (message: string, data?: unknown) => {
    if (config.enabled) {
      console.warn(`[DEBUG WARNING] ${message}`, data || '');
    }
  };

  const logPerformance = (label: string, startTime: number) => {
    if (config.enabled && config.showPerformanceMetrics) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.log(`[DEBUG PERFORMANCE] ${label}: ${duration}ms`);
    }
  };

  const toggleDebugPanel = () => {
    if (config.enabled) {
      setIsDebugPanelVisible(!isDebugPanelVisible);
    }
  };

  const updateConfig = (updates: Partial<DebugConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const getSystemInfo = () => {
    return {
      platform: Platform.OS,
      version: Platform.Version,
      isDev: __DEV__,
      timestamp: new Date().toISOString(),
    };
  };

  return {
    config,
    updateConfig,
    isDebugPanelVisible,
    setIsDebugPanelVisible,
    toggleDebugPanel,
    log,
    logError,
    logWarning,
    logPerformance,
    getSystemInfo,
    isEnabled: config.enabled,
  };
}; 