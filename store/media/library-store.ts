import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as DocumentPicker from "expo-document-picker";
import * as ImagePicker from "expo-image-picker";
import * as FileSystem from "expo-file-system";

export interface LibraryItem {
  id: string;
  title: string;
  type: "image" | "document" | "music";
  uri: string;
  fileName: string;
  fileType: string;
  fileSize?: number;
  dateAdded: string;
  dateModified?: string;
  thumbnailUri?: string;
  duration?: number; // for music files
  width?: number; // for images
  height?: number; // for images
  description?: string;
  tags?: string[];
}

interface LibraryState {
  items: LibraryItem[];
  selectedItems: string[];
  isLoading: boolean;
  error: string | null;
  searchQuery: string;
  filterType: "all" | "image" | "document" | "music";
  sortBy: "name" | "date" | "size" | "type";
  sortOrder: "asc" | "desc";

  // Actions
  addItem: (item: Omit<LibraryItem, "id" | "dateAdded">) => void;
  updateItem: (id: string, updates: Partial<LibraryItem>) => void;
  deleteItem: (id: string) => void;
  deleteSelectedItems: () => void;
  selectItem: (id: string) => void;
  deselectItem: (id: string) => void;
  selectAllItems: () => void;
  clearSelection: () => void;
  setSearchQuery: (query: string) => void;
  setFilterType: (type: "all" | "image" | "document" | "music") => void;
  setSortBy: (sortBy: "name" | "date" | "size" | "type") => void;
  setSortOrder: (order: "asc" | "desc") => void;
  getFilteredItems: () => LibraryItem[];
  importFromDevice: (type: "image" | "document" | "music") => Promise<void>;
  exportItem: (id: string) => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const useLibraryStore = create<LibraryState>()(
  persist(
    (set, get) => ({
      items: [],
      selectedItems: [],
      isLoading: false,
      error: null,
      searchQuery: "",
      filterType: "all",
      sortBy: "date",
      sortOrder: "desc",

  addItem: (item) => {
    const newItem: LibraryItem = {
      ...item,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      dateAdded: new Date().toISOString(),
    };
    set((state) => ({
      items: [...state.items, newItem],
    }));
  },

  updateItem: (id, updates) => {
    set((state) => ({
      items: state.items.map((item) =>
        item.id === id
          ? { ...item, ...updates, dateModified: new Date().toISOString() }
          : item
      ),
    }));
  },

  deleteItem: (id) => {
    set((state) => ({
      items: state.items.filter((item) => item.id !== id),
      selectedItems: state.selectedItems.filter((itemId) => itemId !== id),
    }));
  },

  deleteSelectedItems: () => {
    const { selectedItems } = get();
    set((state) => ({
      items: state.items.filter((item) => !selectedItems.includes(item.id)),
      selectedItems: [],
    }));
  },

  selectItem: (id) => {
    set((state) => ({
      selectedItems: state.selectedItems.includes(id)
        ? state.selectedItems
        : [...state.selectedItems, id],
    }));
  },

  deselectItem: (id) => {
    set((state) => ({
      selectedItems: state.selectedItems.filter((itemId) => itemId !== id),
    }));
  },

  selectAllItems: () => {
    const { getFilteredItems } = get();
    const filteredItems = getFilteredItems();
    set({
      selectedItems: filteredItems.map((item) => item.id),
    });
  },

  clearSelection: () => {
    set({ selectedItems: [] });
  },

  setSearchQuery: (query) => {
    set({ searchQuery: query });
  },

  setFilterType: (type) => {
    set({ filterType: type });
  },

  setSortBy: (sortBy) => {
    set({ sortBy });
  },

  setSortOrder: (order) => {
    set({ sortOrder: order });
  },

  getFilteredItems: () => {
    const { items, searchQuery, filterType, sortBy, sortOrder } = get();
    
    let filtered = items;

    // Apply type filter
    if (filterType !== "all") {
      filtered = filtered.filter((item) => item.type === filterType);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.title.toLowerCase().includes(query) ||
          item.fileName.toLowerCase().includes(query) ||
          item.description?.toLowerCase().includes(query) ||
          item.tags?.some((tag) => tag.toLowerCase().includes(query))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case "name":
          comparison = a.title.localeCompare(b.title);
          break;
        case "date":
          comparison = new Date(a.dateAdded).getTime() - new Date(b.dateAdded).getTime();
          break;
        case "size":
          comparison = (a.fileSize || 0) - (b.fileSize || 0);
          break;
        case "type":
          comparison = a.type.localeCompare(b.type);
          break;
      }

      return sortOrder === "asc" ? comparison : -comparison;
    });

    return filtered;
  },

  importFromDevice: async (type) => {
    try {
      set({ isLoading: true, error: null });

      if (type === "image") {
        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsMultipleSelection: true,
          quality: 1,
        });

        if (!result.canceled) {
          for (const asset of result.assets) {
            const fileInfo = await FileSystem.getInfoAsync(asset.uri);
            get().addItem({
              title: asset.fileName || `Image_${Date.now()}`,
              type: "image",
              uri: asset.uri,
              fileName: asset.fileName || `Image_${Date.now()}.jpg`,
              fileType: asset.type || "image/jpeg",
              fileSize: fileInfo.exists ? fileInfo.size : undefined,
              width: asset.width,
              height: asset.height,
            });
          }
        }
      } else if (type === "document") {
        const result = await DocumentPicker.getDocumentAsync({
          type: "*/*",
          multiple: true,
        });

        if (!result.canceled) {
          for (const asset of result.assets) {
            get().addItem({
              title: asset.name,
              type: "document",
              uri: asset.uri,
              fileName: asset.name,
              fileType: asset.mimeType || "application/octet-stream",
              fileSize: asset.size,
            });
          }
        }
      } else if (type === "music") {
        const result = await DocumentPicker.getDocumentAsync({
          type: "audio/*",
          multiple: true,
        });

        if (!result.canceled) {
          for (const asset of result.assets) {
            get().addItem({
              title: asset.name.replace(/\.[^/.]+$/, ""), // Remove file extension
              type: "music",
              uri: asset.uri,
              fileName: asset.name,
              fileType: asset.mimeType || "audio/mpeg",
              fileSize: asset.size,
            });
          }
        }
      }
    } catch (error) {
      console.error("Error importing from device:", error);
      set({ error: error instanceof Error ? error.message : "Failed to import file" });
    } finally {
      set({ isLoading: false });
    }
  },

  exportItem: async (id) => {
    try {
      set({ isLoading: true, error: null });
      const item = get().items.find((item) => item.id === id);
      if (!item) {
        throw new Error("Item not found");
      }

      // For now, we'll just copy to a temporary location
      // In a real app, you might want to use expo-sharing or similar
      const fileName = `exported_${item.fileName}`;
      const newUri = `${FileSystem.documentDirectory}${fileName}`;
      await FileSystem.copyAsync({
        from: item.uri,
        to: newUri,
      });

      console.log(`Item exported to: ${newUri}`);
    } catch (error) {
      console.error("Error exporting item:", error);
      set({ error: error instanceof Error ? error.message : "Failed to export item" });
    } finally {
      set({ isLoading: false });
    }
  },

  setLoading: (loading) => {
    set({ isLoading: loading });
  },

  setError: (error) => {
    set({ error });
  },
}),
{
  name: "library-storage",
  storage: createJSONStorage(() => AsyncStorage),
  partialize: (state) => ({
    items: state.items,
    filterType: state.filterType,
    sortBy: state.sortBy,
    sortOrder: state.sortOrder,
  }),
}
)
); 