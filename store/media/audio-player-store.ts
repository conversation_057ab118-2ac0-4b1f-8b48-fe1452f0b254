import { create } from "zustand";
import { createAudioPlayer, AudioPlayer } from "expo-audio";
export interface Track {
  id: string;
  title: string;
  artist?: string;
  album?: string;
  uri: string;
  duration?: number;
  imageUri?: string;
}
interface AudioPlayerState {
  player: AudioPlayer | null;
  isPlaying: boolean;
  isPaused: boolean;
  isLoaded: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  playbackRate: number;
  isLooping: boolean;
  isBuffering: boolean;
  currentResourceId: string | null;
  error: string | null;
  currentTrack: Track | null;
  queue: Track[];
  history: Track[];
  // Actions
  loadAudio: (uri: string, resourceId: string) => Promise<void>;
  play: () => void;
  pause: () => void;
  stop: () => void;
  seekTo: (seconds: number) => Promise<void>;
  setVolume: (volume: number) => void;
  setPlaybackRate: (rate: number) => void;
  setIsLooping: (loop: boolean) => void;
  unloadAudio: () => void;
  updatePlaybackStatus: () => void;
  playTrack: (track: Track) => Promise<void>;
  setCurrentTrack: (track: Track) => void;
  clearCurrentTrack: () => void;
  addToQueue: (track: Track) => void;
  removeFromQueue: (trackId: string) => void;
  clearQueue: () => void;
  nextTrack: () => Promise<void>;
  previousTrack: () => Promise<void>;
}
export const useAudioPlayerStore = create<AudioPlayerState>((set, get) => ({
  player: null,
  isPlaying: false,
  isPaused: false,
  isLoaded: false,
  currentTime: 0,
  duration: 0,
  volume: 1.0,
  playbackRate: 1.0,
  isLooping: false,
  isBuffering: false,
  currentResourceId: null,
  error: null,
  currentTrack: null,
  queue: [],
  history: [],
  loadAudio: async (uri: string, resourceId: string) => {
    try {
      const { player: currentPlayer } = get();
      // Clean up existing player
      if (currentPlayer) {
        currentPlayer.remove();
      }
      // Create new player
      const newPlayer = createAudioPlayer(uri);
      set({
        player: newPlayer,
        currentResourceId: resourceId,
        error: null,
        isLoaded: false,
        isPlaying: false,
        isPaused: false,
        currentTime: 0,
        duration: 0,
      });
      // Set up status listener
      newPlayer.addListener("playbackStatusUpdate", (_status) => {
        set({
          isLoaded: newPlayer.isLoaded,
          isPlaying: newPlayer.playing,
          isPaused: newPlayer.paused,
          currentTime: newPlayer.currentTime,
          duration: newPlayer.duration,
          volume: newPlayer.volume,
          playbackRate: newPlayer.playbackRate,
          isLooping: newPlayer.loop,
          isBuffering: newPlayer.isBuffering,
        });
      });
    } catch (_error) {
      console.error("Failed to load audio:", _error);
      set({
        error: _error instanceof Error ? _error.message : "Failed to load audio",
        isLoaded: false,
      });
    }
  },
  play: () => {
    const { player } = get();
    if (player && player.isLoaded) {
      player.play();
      set({ isPlaying: true, isPaused: false });
    }
  },
  pause: () => {
    const { player } = get();
    if (player && player.isLoaded) {
      player.pause();
      set({ isPlaying: false, isPaused: true });
    }
  },
  stop: () => {
    const { player } = get();
    if (player && player.isLoaded) {
      player.pause();
      player.seekTo(0);
      set({
        isPlaying: false,
        isPaused: false,
        currentTime: 0,
      });
    }
  },
  seekTo: async (seconds: number) => {
    const { player } = get();
    if (player && player.isLoaded) {
      try {
        await player.seekTo(seconds);
        set({ currentTime: seconds });
      } catch (_error) {
        console.error("Failed to seek:", _error);
      }
    }
  },
  setVolume: (volume: number) => {
    const { player } = get();
    if (player && player.isLoaded) {
      player.volume = Math.max(0, Math.min(1, volume));
      set({ volume: player.volume });
    }
  },
  setPlaybackRate: (rate: number) => {
    const { player } = get();
    if (player && player.isLoaded) {
      player.setPlaybackRate(rate);
      set({ playbackRate: rate });
    }
  },
  setIsLooping: (loop: boolean) => {
    const { player } = get();
    if (player && player.isLoaded) {
      player.loop = loop;
      set({ isLooping: loop });
    }
  },
  unloadAudio: () => {
    const { player } = get();
    if (player) {
      player.remove();
    }
    set({
      player: null,
      isPlaying: false,
      isPaused: false,
      isLoaded: false,
      currentTime: 0,
      duration: 0,
      volume: 1.0,
      playbackRate: 1.0,
      isLooping: false,
      isBuffering: false,
      currentResourceId: null,
      error: null,
    });
  },
  updatePlaybackStatus: () => {
    const { player } = get();
    if (player) {
      set({
        isLoaded: player.isLoaded,
        isPlaying: player.playing,
        isPaused: player.paused,
        currentTime: player.currentTime,
        duration: player.duration,
        volume: player.volume,
        playbackRate: player.playbackRate,
        isLooping: player.loop,
        isBuffering: player.isBuffering,
      });
    }
  },
  playTrack: async (track: Track) => {
    try {
      const { currentTrack, history } = get();
      // Add current track to history if it exists
      if (currentTrack) {
        set({ history: [currentTrack, ...history.slice(0, 9)] });
      }
      // Load and play the new track
      await get().loadAudio(track.uri, track.id);
      await get().play();
      set({ currentTrack: track });
    } catch (_error) {
      console.error("Error playing track:", _error);
    }
  },
  setCurrentTrack: (track: Track) => {
    set({ currentTrack: track });
  },
  clearCurrentTrack: () => {
    set({ currentTrack: null });
  },
  addToQueue: (track: Track) => {
    set((state) => ({
      queue: [...state.queue, track],
    }));
  },
  removeFromQueue: (trackId: string) => {
    set((state) => ({
      queue: state.queue.filter((track) => track.id !== trackId),
    }));
  },
  clearQueue: () => {
    set({ queue: [] });
  },
  nextTrack: async () => {
    const { queue } = get();
    if (queue.length > 0) {
      const nextTrack = queue[0];
      set({ queue: queue.slice(1) });
      await get().playTrack(nextTrack);
    } else {
      await get().stop();
    }
  },
  previousTrack: async () => {
    const { history } = get();
    if (history.length > 0) {
      const prevTrack = history[0];
      set({ history: history.slice(1) });
      await get().playTrack(prevTrack);
    } else {
      await get().seekTo(0);
      await get().play();
    }
  },
}));