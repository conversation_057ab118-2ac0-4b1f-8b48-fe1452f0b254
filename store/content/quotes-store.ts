import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
export interface Quote {
  id: string;
  text: string;
  author: string;
  category?: string;
}
interface QuotesState {
  quotes: Quote[];
  dailyQuote: Quote | null;
  isLoading: boolean;
  error: string | null;
  fetchQuotes: () => Promise<void>;
  setDailyQuote: () => void;
  addQuote: (quote: Omit<Quote, 'id'>) => void;
  removeQuote: (id: string) => void;
}
export const useQuotesStore = create<QuotesState>()(
  persist(
    (set, get) => ({
      quotes: [
        {
          id: '1',
          text: "Recovery is not a race. You don't have to feel guilty if it takes you longer than you thought it would.",
          author: 'Unknown'
        },
        {
          id: '2',
          text: "The greatest glory in living lies not in never falling, but in rising every time we fall.",
          author: '<PERSON>'
        },
        {
          id: '3',
          text: "Every day is a new beginning, stay away from what might have been and look at what can be.",
          author: '<PERSON><PERSON>'
        },
        {
          id: '4',
          text: "Healing is a process, not an event.",
          author: '<PERSON>'
        },
        {
          id: '5',
          text: "Sometimes the smallest step in the right direction ends up being the biggest step of your life.",
          author: 'Unknown'
        },
        {
          id: '6',
          text: "You can't go back and change the beginning, but you can start where you are and change the ending.",
          author: 'C.S. Lewis'
        },
        {
          id: '7',
          text: "Recovery is hard. Regret is harder.",
          author: 'Brittany Burgunder'
        },
        {
          id: '8',
          text: "The only person you are destined to become is the person you decide to be.",
          author: 'Ralph Waldo Emerson'
        },
        {
          id: '9',
          text: "Rock bottom became the solid foundation on which I rebuilt my life.",
          author: 'J.K. Rowling'
        },
        {
          id: '10',
          text: "Courage isn't having the strength to go on – it is going on when you don't have strength.",
          author: 'Napoleon Bonaparte'
        },
        {
          id: '11',
          text: "Success is the sum of small efforts, repeated day in and day out.",
          author: 'Robert Collier'
        },
        {
          id: '12',
          text: "Though no one can go back and make a brand new start, anyone can start from now and make a brand new ending.",
          author: 'Carl Bard'
        },
        {
          id: '13',
          text: "It does not matter how slowly you go as long as you do not stop.",
          author: 'Confucius'
        },
        {
          id: '14',
          text: "The best time to plant a tree was 20 years ago. The second best time is now.",
          author: 'Chinese Proverb'
        },
        {
          id: '15',
          text: "If you can quit for a day, you can quit for a lifetime.",
          author: 'Benjamin Alire Sáenz'
        },
        {
          id: '16',
          text: "Your present circumstances don't determine where you can go; they merely determine where you start.",
          author: 'Nido Qubein'
        },
        {
          id: '17',
          text: "The only journey is the one within.",
          author: 'Rainer Maria Rilke'
        },
        {
          id: '18',
          text: "Fall seven times, stand up eight.",
          author: 'Japanese Proverb'
        },
        {
          id: '19',
          text: "Every moment is a fresh beginning.",
          author: 'T.S. Eliot'
        },
        {
          id: '20',
          text: "Nothing is impossible. The word itself says 'I'm possible!'",
          author: 'Audrey Hepburn'
        },
        {
          id: '21',
          text: "When everything seems to be going against you, remember that the airplane takes off against the wind, not with it.",
          author: 'Henry Ford'
        },
        {
          id: '22',
          text: "Believe you can and you're halfway there.",
          author: 'Theodore Roosevelt'
        },
        {
          id: '23',
          text: "Start where you are. Use what you have. Do what you can.",
          author: 'Arthur Ashe'
        },
        {
          id: '24',
          text: "Don't judge each day by the harvest you reap but by the seeds that you plant.",
          author: 'Robert Louis Stevenson'
        },
        {
          id: '25',
          text: "The secret of getting ahead is getting started.",
          author: 'Mark Twain'
        }
      ],
      dailyQuote: null,
      isLoading: false,
      error: null,
      fetchQuotes: async () => {
        set({ isLoading: true, error: null });
        try {
          // In a real app, this would fetch quotes from an API
          // For now, we'll just use the hardcoded quotes
          
          // Set a daily quote if not already set
          if (!get().dailyQuote) {
            get().setDailyQuote();
          }
          
          set({ isLoading: false });
        } catch (_error) {
          set({ 
            isLoading: false, 
            error: _error instanceof Error ? _error.message : 'An unknown error occurred' 
          });
        }
      },
      setDailyQuote: () => {
        const { quotes } = get();
        if (quotes.length > 0) {
          // Get a random quote for the day
          const randomIndex = Math.floor(Math.random() * quotes.length);
          set({ dailyQuote: quotes[randomIndex] });
        }
      },
      addQuote: (quote) => {
        const newQuote = {
          ...quote,
          id: Date.now().toString()
        };
        set(state => ({
          quotes: [...state.quotes, newQuote]
        }));
      },
      removeQuote: (id) => {
        set(state => ({
          quotes: state.quotes.filter(quote => quote.id !== id)
        }));
      }
    }),
    {
      name: 'quotes-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);