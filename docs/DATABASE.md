# Database Implementation

## Overview

The Sobrix Health app now uses SQLite as its primary database solution, replacing AsyncStorage for better performance, scalability, and data integrity. This implementation provides:

- **Relational data storage** with ACID compliance
- **Complex query capabilities** for analytics and reporting
- **Better performance** for large datasets
- **Reduced storage quota issues** on web platforms
- **Data migration** from AsyncStorage to SQLite

## Architecture

### Database Service (`services/database-simple.ts`)

The database service provides a simple, singleton-based interface for all database operations:

```typescript
import { initDatabase, simpleDatabaseService } from '@/services/database-simple';

// Initialize database
await initDatabase();

// Use the service
await simpleDatabaseService.saveUserProfile(profileData);
const profile = await simpleDatabaseService.getUserProfile();
```

### React Integration

The database is integrated into the React app through:

1. **Database Context** (`context/database-context.tsx`) - Provides app-wide database access
2. **Database Hook** (`hooks/database/use-database.ts`) - Manages database state and initialization
3. **Migration Hook** (`hooks/database/use-database.ts`) - Handles data migration from AsyncStorage

### App Integration

The database is initialized at the app root level in `app/_layout.tsx`:

```tsx
<DatabaseProvider>
  <ThemeProvider>
    <RootLayoutNav />
  </ThemeProvider>
</DatabaseProvider>
```

## Database Schema

### Core Tables

#### `user_profile`
Stores user profile data as JSON for flexibility:
- `id` (INTEGER PRIMARY KEY)
- `data` (TEXT) - JSON serialized profile data
- `created_at`, `updated_at` (DATETIME)

#### `health_data`
Daily health metrics:
- `id` (INTEGER PRIMARY KEY)
- `date` (TEXT UNIQUE) - Date in YYYY-MM-DD format
- `sleep`, `pills`, `hydration`, `exercise` (REAL)
- `created_at`, `updated_at` (DATETIME)

#### `mood_entries`
User mood and craving tracking:
- `id` (TEXT PRIMARY KEY)
- `date` (TEXT)
- `mood` (INTEGER 1-5)
- `craving_intensity` (INTEGER 1-5)
- `notes` (TEXT)
- `created_at` (DATETIME)

#### `emergency_contacts`
Emergency contact information:
- `id` (TEXT PRIMARY KEY)
- `name`, `phone` (TEXT NOT NULL)
- `email`, `relationship`, `notes` (TEXT)
- `created_at` (DATETIME)

#### `documents`
User documents and notes:
- `id` (TEXT PRIMARY KEY)
- `title` (TEXT NOT NULL)
- `content`, `category` (TEXT)
- `date` (TEXT)
- `created_at` (DATETIME)

#### `media_files`
Media file metadata:
- `id` (TEXT PRIMARY KEY)
- `title` (TEXT NOT NULL)
- `description`, `type`, `category` (TEXT)
- `file_name` (TEXT)
- `file_size` (INTEGER)
- `date` (TEXT)
- `created_at` (DATETIME)

## API Reference

### User Profile Operations

```typescript
// Save user profile (overwrites existing)
await simpleDatabaseService.saveUserProfile(profileData);

// Get user profile
const profile = await simpleDatabaseService.getUserProfile();
```

### Health Data Operations

```typescript
// Save daily health data
await simpleDatabaseService.saveHealthData('2024-01-15', {
  sleep: 8.5,
  pills: 1,
  hydration: 2.5,
  exercise: 1
});

// Get health data for specific date
const healthData = await simpleDatabaseService.getHealthData('2024-01-15');

// Get health data range
const rangeData = await simpleDatabaseService.getHealthDataRange('2024-01-01', '2024-01-31');
```

### Mood Entry Operations

```typescript
// Add mood entry
await simpleDatabaseService.addMoodEntry({
  id: 'mood-123',
  date: '2024-01-15',
  mood: 4,
  cravingIntensity: 2,
  notes: 'Feeling good today'
});

// Get recent mood entries
const moodEntries = await simpleDatabaseService.getMoodEntries(50);
```

### Emergency Contact Operations

```typescript
// Add emergency contact
await simpleDatabaseService.addEmergencyContact({
  id: 'contact-123',
  name: 'John Doe',
  phone: '+**********',
  email: '<EMAIL>',
  relationship: 'friend',
  notes: 'Available 24/7'
});

// Get all emergency contacts
const contacts = await simpleDatabaseService.getEmergencyContacts();

// Delete emergency contact
await simpleDatabaseService.deleteEmergencyContact('contact-123');
```

### Document Operations

```typescript
// Add document
await simpleDatabaseService.addDocument({
  id: 'doc-123',
  title: 'Recovery Journal',
  content: 'Today was a good day...',
  category: 'journal',
  date: '2024-01-15'
});

// Get all documents
const documents = await simpleDatabaseService.getDocuments();
```

### Media File Operations

```typescript
// Add media file metadata
await simpleDatabaseService.addMediaFile({
  id: 'media-123',
  title: 'Progress Photo',
  description: 'Monthly progress photo',
  type: 'image',
  category: 'progress',
  fileName: 'progress_jan_2024.jpg',
  fileSize: 1024000,
  date: '2024-01-15'
});

// Get all media files
const mediaFiles = await simpleDatabaseService.getMediaFiles();
```

### Utility Operations

```typescript
// Get database statistics
const stats = await simpleDatabaseService.getDatabaseStats();
// Returns: { tables: number, totalRecords: number }

// Clear all data (use with caution)
await simpleDatabaseService.clearAllData();
```

## Data Migration

### Automatic Migration

The app includes automatic migration from AsyncStorage to SQLite:

```typescript
// Migrate data from AsyncStorage
await simpleDatabaseService.migrateFromAsyncStorage();
```

### Migration UI

A user-friendly migration modal is available in `components/settings/DatabaseMigrationModal.tsx`:

```typescript
import { DatabaseMigrationModal } from '@/components/settings/DatabaseMigrationModal';

<DatabaseMigrationModal
  visible={showMigrationModal}
  onClose={() => setShowMigrationModal(false)}
/>
```

### Migration Process

1. **Scans AsyncStorage** for existing data keys
2. **Extracts user profile** data from `user-profile-storage` key
3. **Migrates health data** from `healthData_*` keys
4. **Preserves data integrity** during the migration process
5. **Provides feedback** to users about migration status

## React Hooks

### useDatabase

Provides database state and service access:

```typescript
import { useDatabaseContext } from '@/context/database-context';

const { isInitialized, isLoading, error, service } = useDatabaseContext();
```

### useDatabaseMigration

Handles data migration from AsyncStorage:

```typescript
import { useDatabaseMigration } from '@/hooks/database/use-database';

const { isMigrating, migrationError, migrationComplete, migrateData } = useDatabaseMigration();
```

## Error Handling

The database service includes comprehensive error handling:

- **Connection errors** are logged and thrown
- **Query errors** are caught and logged with context
- **Migration errors** are handled gracefully with user feedback
- **Type safety** is enforced where possible

## Performance Considerations

- **Indexed queries** for frequently accessed data
- **Batch operations** for bulk data insertion
- **Connection pooling** through singleton pattern
- **Lazy loading** of database initialization
- **Optimized queries** for common operations

## Testing

A test script is available to verify database functionality:

```bash
# Run database tests
npx ts-node scripts/test-database.ts
```

The test covers:
- Database initialization
- User profile operations
- Health data operations
- Mood entry operations
- Emergency contact operations
- Database statistics

## Future Enhancements

Potential improvements for the database implementation:

1. **Data encryption** for sensitive information
2. **Backup and restore** functionality
3. **Data synchronization** with cloud services
4. **Advanced analytics** queries
5. **Database versioning** and schema migrations
6. **Performance monitoring** and optimization
7. **Offline-first** data synchronization

## Troubleshooting

### Common Issues

1. **Database not initialized**: Ensure `initDatabase()` is called before using the service
2. **Migration failures**: Check AsyncStorage data format and permissions
3. **Type errors**: Ensure data matches expected schema types
4. **Performance issues**: Consider indexing frequently queried columns

### Debug Information

Enable debug logging by setting:
```typescript
console.log('Database debug info:', await simpleDatabaseService.getDatabaseStats());
```

## Dependencies

- `expo-sqlite`: SQLite database for React Native/Expo
- `@react-native-async-storage/async-storage`: For migration from AsyncStorage
- React Context API for state management
- TypeScript for type safety 