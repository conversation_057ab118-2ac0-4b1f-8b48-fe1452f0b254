# Debug Functionality

The app includes comprehensive debug functionality to help track data persistence, storage usage, and system information during development.

## Features

### Debug Panel
A comprehensive debug panel that shows:
- **Storage Statistics**: Total storage usage, user profile size, browser storage quota (web)
- **User Profile**: Basic info and data array counts
- **Documents**: Emergency plans, relapse plans with details
- **Media Files**: Emergency cards and other media with file sizes
- **System Info**: Platform, theme, and refresh tracking

### Debug Hook (`useDebug`)
A React hook that provides:
- Conditional logging (only in development)
- Performance tracking
- Error and warning logging
- Debug panel visibility control

## Usage

### Accessing the Debug Panel

#### Method 1: Settings Page (Recommended)
In development mode, go to **Settings > Support > Debug Information** to access the debug panel.

#### Method 2: Triple Tap (Using DebugTrigger)
Wrap any component with `DebugTrigger` to enable triple-tap debug access:

```tsx
import { DebugTrigger } from '@/components/shared';

function MyComponent() {
  return (
    <DebugTrigger>
      <View>
        {/* Your component content */}
      </View>
    </DebugTrigger>
  );
}
```

### Using the Debug Hook

```tsx
import { useDebug } from '@/hooks/useDebug';

function MyComponent() {
  const debug = useDebug();

  useEffect(() => {
    debug.log('Component mounted');
    debug.log('Data loaded', { count: data.length });
  }, []);

  const handleError = (error: Error) => {
    debug.logError('Failed to save data', error);
  };

  const handlePerformance = () => {
    const startTime = Date.now();
    // ... some operation
    debug.logPerformance('Data processing', startTime);
  };

  return (
    <View>
      {/* Component content */}
    </View>
  );
}
```

### Debug Configuration

The debug hook accepts configuration:

```tsx
const debug = useDebug();

// Update debug settings
debug.updateConfig({
  showConsoleLog: true,
  showStorageInfo: true,
  showPerformanceMetrics: true,
});
```

## Debug Panel Sections

### Storage Statistics
- Total storage keys in AsyncStorage
- User profile data size
- Total storage usage
- Browser storage quota (web only)

### User Profile
- Basic profile information
- Onboarding status
- Count of data arrays (contacts, mood entries, etc.)

### Documents
- Emergency plans with titles and dates
- Relapse plans with details
- Document categories and counts

### Media Files
- Emergency cards with file sizes
- Media file categories
- File types and metadata

### System Info
- Platform and version
- Current theme
- Refresh tracking

## Development vs Production

- Debug functionality is automatically disabled in production builds
- The debug option in settings only appears in development mode
- Console logging is conditional based on `__DEV__` flag
- No performance impact in production

## Examples

### Crisis Support Debug Logging
The Crisis Support component uses debug logging to track data persistence:

```tsx
useEffect(() => {
  debug.log("CrisisSupport: Component mounted/updated");
  debug.log("CrisisSupport: Documents count:", profile?.documents?.length || 0);
  debug.log("CrisisSupport: Media files count:", profile?.mediaFiles?.length || 0);
}, [profile?.documents, profile?.mediaFiles, debug]);
```

### Storage Debugging
Track when data is added to storage:

```tsx
const handleAddDocument = (document) => {
  debug.log("Adding document:", document.title);
  addDocument(document);
  debug.log("Total documents:", profile?.documents?.length || 0);
};
```

## Troubleshooting

### Data Not Persisting
1. Open debug panel
2. Check storage statistics
3. Verify document/media counts
4. Check console for storage errors

### Performance Issues
1. Enable performance metrics in debug config
2. Use `debug.logPerformance()` around slow operations
3. Check storage quota on web

### Storage Quota Issues (Web)
1. Check browser storage usage in debug panel
2. Monitor storage quota percentage
3. Clear old media files if approaching limit

## Best Practices

1. **Use debug logging instead of console.log**: Automatically disabled in production
2. **Wrap screens with DebugTrigger**: Easy access to debug panel
3. **Track data persistence**: Log when adding/removing data
4. **Monitor performance**: Use performance logging for slow operations
5. **Check storage regularly**: Monitor storage usage, especially on web 