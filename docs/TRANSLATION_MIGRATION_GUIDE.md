# Translation Service Migration Guide

This guide explains how to migrate from hardcoded translations to the new centralized translation service.

## Overview

The new translation service provides:
- ✅ Centralized translation management
- ✅ TypeScript safety with auto-completion
- ✅ Interpolation support for dynamic values
- ✅ Automatic fallback to English
- ✅ Easy integration with user language preferences
- ✅ Consistent API across the entire app

## Architecture

```
locales/
├── en.json          # English translations
└── nl.json          # Dutch translations

services/
└── translation.ts   # Translation service

hooks/
└── useTranslation.ts # React hook for components

context/
└── translation-context.tsx # Optional context provider
```

## Migration Steps

### 1. Replace Hardcoded Strings

**Before:**
```tsx
// ❌ Old way - hardcoded translations
{language === "nl" ? "Opslaan" : "Save"}
```

**After:**
```tsx
// ✅ New way - using translation service
import { useTranslation } from '@/hooks/useTranslation';

const { t } = useTranslation();
{t('common.save')}
```

### 2. Replace Translation Objects

**Before:**
```tsx
// ❌ Old way - component-specific translation objects
interface Props {
  translations: {
    en: { welcome: { title: string; subtitle: string; } };
    nl: { welcome: { title: string; subtitle: string; } };
  };
}

const t = translations[language];
{t.welcome.title}
```

**After:**
```tsx
// ✅ New way - using translation hook
import { useTranslation } from '@/hooks/useTranslation';

const { t } = useTranslation();
{t('onboarding.welcome.title')}
```

### 3. Replace Utility Functions

**Before:**
```tsx
// ❌ Old way - utility functions with language parameter
export const getMoodLabel = (value: number, language: Language): string => {
  if (language === "nl") {
    switch (value) {
      case 1: return "Zeer slecht";
      case 2: return "Slecht";
      // ...
    }
  } else {
    switch (value) {
      case 1: return "Very Bad";
      case 2: return "Bad";
      // ...
    }
  }
};
```

**After:**
```tsx
// ✅ New way - using translation service methods
import { useTranslation } from '@/hooks/useTranslation';

const { getMoodLabel } = useTranslation();
const label = getMoodLabel(value); // Automatically uses current language
```

### 4. Handle Interpolation

**Before:**
```tsx
// ❌ Old way - manual string interpolation
const message = language === "nl" 
  ? `${value} uur slaap` 
  : `${value} hours of sleep`;
```

**After:**
```tsx
// ✅ New way - using interpolation
const { t } = useTranslation();
const message = t('progress.health.sleep', { value });
```

## Translation Key Structure

The translation keys follow a hierarchical structure:

```
common.*              # Common UI elements (save, cancel, etc.)
navigation.*          # Navigation labels
onboarding.*          # Onboarding flow
settings.*            # Settings screens
progress.*            # Progress tracking
contacts.*            # Contact management
crisis.*              # Crisis management
errors.*              # Error messages
```

### Examples:

```tsx
// Common actions
t('common.save')           // "Save" / "Opslaan"
t('common.cancel')         // "Cancel" / "Annuleren"
t('common.delete')         // "Delete" / "Verwijderen"

// Navigation
t('navigation.dashboard')  // "Dashboard" / "Dashboard"
t('navigation.progress')   // "Progress" / "Voortgang"

// Onboarding
t('onboarding.welcome.title')        // "Welcome to CrisisBox"
t('onboarding.name.placeholder')     // "Enter your name"

// With interpolation
t('progress.health.sleep', { value: 8 })  // "8 hours of sleep"
```

## Component Migration Example

### Before (WelcomeStep.tsx):
```tsx
interface WelcomeStepProps {
  language: "en" | "nl";
  translations: {
    en: { welcome: { title: string; subtitle: string; } };
    nl: { welcome: { title: string; subtitle: string; } };
  };
}

export default function WelcomeStep({ language, translations }: WelcomeStepProps) {
  const t = translations[language];
  
  return (
    <View>
      <Text>{t.welcome.title}</Text>
      <Text>{language === "nl" ? "Voortgang bijhouden" : "Track Progress"}</Text>
    </View>
  );
}
```

### After (WelcomeStepMigrated.tsx):
```tsx
import { useTranslation } from '@/hooks/useTranslation';

interface WelcomeStepProps {
  // No more language or translations props needed!
}

export default function WelcomeStepMigrated(props: WelcomeStepProps) {
  const { t } = useTranslation();
  
  return (
    <View>
      <Text>{t('onboarding.welcome.title')}</Text>
      <Text>{t('onboarding.welcome.trackProgress')}</Text>
    </View>
  );
}
```

## Available Hook Methods

```tsx
const {
  t,                        // Main translation function
  getMoodLabel,             // Get mood label (1-5)
  getCravingLabel,          // Get craving label (1-5)
  getHealthMetricLabel,     // Get health metric with value
  getContactCategoryName,   // Get contact category name
  getCommonTriggers,        // Get triggers array
  getCurrentLanguage,       // Get current language
  getAvailableLanguages,    // Get available languages
  currentLanguage,          // Current language state
} = useTranslation();
```

## Adding New Translations

1. **Add to English file** (`locales/en.json`):
```json
{
  "myFeature": {
    "title": "My Feature",
    "description": "This is my new feature with {{count}} items"
  }
}
```

2. **Add to Dutch file** (`locales/nl.json`):
```json
{
  "myFeature": {
    "title": "Mijn Functie",
    "description": "Dit is mijn nieuwe functie met {{count}} items"
  }
}
```

3. **Use in component**:
```tsx
const { t } = useTranslation();
const title = t('myFeature.title');
const description = t('myFeature.description', { count: 5 });
```

## TypeScript Benefits

The translation service provides full TypeScript support:

- ✅ **Auto-completion** for translation keys
- ✅ **Type safety** - invalid keys show errors
- ✅ **Refactoring support** - rename keys across the codebase
- ✅ **Interpolation typing** - ensures correct variable names

## Migration Checklist

- [ ] Replace `language === "nl" ? "Dutch" : "English"` patterns
- [ ] Remove `language` props from components
- [ ] Remove `translations` props from components
- [ ] Replace utility functions with hook methods
- [ ] Update component interfaces
- [ ] Add missing translations to JSON files
- [ ] Test both English and Dutch languages
- [ ] Remove old translation utility files

## Best Practices

1. **Use descriptive key names**: `onboarding.welcome.title` not `welcome1`
2. **Group related translations**: Keep feature translations together
3. **Use interpolation**: For dynamic values, use `{{variable}}` syntax
4. **Provide fallbacks**: English translations should always exist
5. **Keep keys consistent**: Use the same structure across features
6. **Test thoroughly**: Verify both languages work correctly

## Common Patterns

### Conditional Text
```tsx
// ❌ Old
{isLoading ? (language === "nl" ? "Laden..." : "Loading...") : content}

// ✅ New
{isLoading ? t('common.loading') : content}
```

### Arrays/Lists
```tsx
// ❌ Old
const triggers = language === "nl" ? ["Stress", "Verveling"] : ["Stress", "Boredom"];

// ✅ New
const { getCommonTriggers } = useTranslation();
const triggers = getCommonTriggers();
```

### Form Labels
```tsx
// ❌ Old
placeholder={language === "nl" ? "Voer naam in" : "Enter name"}

// ✅ New
placeholder={t('onboarding.name.placeholder')}
```

This migration will significantly improve the maintainability and consistency of translations across your app! 