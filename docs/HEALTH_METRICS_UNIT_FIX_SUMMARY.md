# Health Metrics Unit Consistency Fix

## Issue Identified

The user reported two main issues:

1. **Energy metric not needed**: Remove "energy" from health metrics as it's not used
2. **Hydration unit inconsistency**: Health cards showed "liters" but user prefers "glasses"

## Root Cause Analysis

1. **Energy metric**: Was included in health tracking but not actually used by the user
2. **Hydration units**: The LogHealthModal was using dynamic units from onboarding (liters) but user prefers glasses

## Solution Implemented

### 1. Removed Energy Metric Completely

**Updated files:**

- `components/health/LogHealthModal.tsx` - Removed energy state and rendering
- `utils/dashboard-utils.ts` - Removed energy from UserHealthData interface and HealthMetric type
- `components/dashboard/HealthMetricsCard.tsx` - Removed energy icon, name, and color handling

**Key changes:**

```typescript
// BEFORE
export interface UserHealthData {
  sleep?: number;
  energy?: number; // REMOVED
  pills?: number;
  hydration?: number;
  exercise?: number;
}

// AFTER
export interface UserHealthData {
  sleep?: number;
  pills?: number;
  hydration?: number;
  exercise?: number;
}
```

### 2. Fixed Hydration to Use "Glasses"

**Updated LogHealthModal.tsx:**

- Changed default hydration unit from "liters" to "glasses"
- Force hydration to always use "glasses" regardless of onboarding setting

**Updated onboarding configuration:**

- Changed water intake unit from "liters" to "glasses"
- Updated default target from 2 liters to 8 glasses
- Updated translations to use "glasses/day" instead of "liters/day"

**Key changes:**

```typescript
// NEW (force glasses for hydration)
case "waterIntake":
  goals.hydration = goal.target;
  units.hydration = "glasses"; // Force to glasses regardless of onboarding setting
  break;
```

**Updated onboarding configuration:**

```typescript
{
  id: "waterIntake",
  label: "Water Intake",
  labelNL: "Waterinname",
  unit: "glasses",        // Changed from "liters"
  unitNL: "glazen",       // Changed from "liters"
  defaultTarget: 8,       // Changed from 2
  placeholder: "e.g., 8", // Changed from "e.g., 2"
}
```

### 3. Updated Dashboard Utils

**Enhanced generateHealthMetrics():**

- Force hydration to use "glasses" unit regardless of profile setting
- Removed energy metric handling completely

```typescript
// Force hydration to use "glasses" unit regardless of onboarding setting
let displayUnit = goal.unit;
if (goal.id === "waterIntake") {
  displayUnit = "glasses";
}
```

## Current Health Metrics

The app now tracks only these 4 health metrics:

1. **Sleep** - hours (e.g., "8 hours")
2. **Pills** - pills (e.g., "2 pills")
3. **Hydration** - glasses (e.g., "8 glasses")
4. **Exercise** - minutes (e.g., "30 minutes")

## Data Flow Consistency

Now the data flows consistently with user preferences:

1. **Onboarding** → Sets up health goals with "glasses" for water intake
2. **User Profile** → Stores health goals with configured units
3. **HealthMetricsCard** → Always displays "glasses" for hydration
4. **LogHealthModal** → Always uses "glasses" for hydration input/display
5. **Dashboard Utils** → Forces "glasses" for hydration metrics

## Benefits Achieved

✅ **Simplified Metrics**: Removed unused energy metric for cleaner interface
✅ **User Preference**: Hydration now consistently uses "glasses" as preferred
✅ **Consistent Units**: All hydration-related components use the same unit
✅ **Cleaner Codebase**: Removed energy-related code throughout the app
✅ **Better UX**: No more confusion between liters and glasses

## Testing Verification

To verify the fix:

1. Complete onboarding with health goals enabled
2. Check health cards on dashboard - should show "glasses" for hydration (no energy card)
3. Open LogHealthModal - should show "glasses" for hydration (no energy input)
4. All hydration displays should consistently show "glasses"

## Files Modified

- `components/health/LogHealthModal.tsx` - Removed energy, fixed hydration to glasses
- `utils/dashboard-utils.ts` - Removed energy from interfaces, forced glasses for hydration
- `components/dashboard/HealthMetricsCard.tsx` - Removed energy handling
- `app/onboarding.tsx` - Updated water intake to use glasses
- `app/components/onboarding/HealthGoalsStep.tsx` - Updated water intake to use glasses
- `HEALTH_METRICS_UNIT_FIX_SUMMARY.md` - This documentation

## Future Improvements

- Consider adding more relevant health metrics if needed
- Add unit preferences in settings for other metrics
- Implement metric customization for different user needs
