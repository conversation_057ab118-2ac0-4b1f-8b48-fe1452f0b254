# Health Metrics Card Update Fix

## Issue Identified

When users add health data (like glasses of water) in the LogHealthModal, the health cards on the dashboard were not updating to show the new values.

## Root Cause Analysis

The issue was caused by a **data mapping mismatch** between:

1. **Goal ID in profile**: `waterIntake` (set during onboarding)
2. **Data key in storage**: `hydration` (used in UserHealthData interface)
3. **Data retrieval**: The code was trying to access `storedData.waterIntake` but the data was actually stored as `storedData.hydration`

### The Problem Code

```typescript
// BEFORE (incorrect mapping)
return {
  id: goal.id,
  type: iconType,
  value: storedData?.[goal.id as keyof UserHealthData] ?? 0, // ❌ Tries to access 'waterIntake' key
  maxValue: goal.target,
  unit: displayUnit,
  trend: "stable" as const,
};
```

When `goal.id` was `"waterIntake"`, the code tried to access `storedData.waterIntake`, but the actual data was stored under `storedData.hydration`.

## Solution Implemented

### 1. Fixed Data Mapping in generateHealthMetrics()

**Updated `utils/dashboard-utils.ts`:**

```typescript
// NEW (correct mapping)
// Map goal ID to stored data key
let dataKey: keyof UserHealthData;
if (goal.id === "waterIntake") {
  dataKey = "hydration"; // Map waterIntake goal to hydration data
} else {
  dataKey = goal.id as keyof UserHealthData;
}

return {
  id: goal.id,
  type: iconType,
  value: storedData?.[dataKey] ?? 0, // ✅ Uses correct data key
  maxValue: goal.target,
  unit: displayUnit,
  trend: "stable" as const,
};
```

### 2. Added Callback for Immediate Updates

**Updated `LogHealthModal.tsx`:**

- Added `onDataSaved?: () => void` prop to interface
- Call the callback after successful data save to trigger dashboard refresh

```typescript
const handleSave = async () => {
  // ... save data ...
  await saveUserHealthData(dateString, healthData);
  Alert.alert("Success", "Health data saved!");

  // Call the callback to refresh dashboard data
  if (onDataSaved) {
    onDataSaved();
  }

  onClose();
};
```

**Updated `app/(tabs)/index.tsx`:**

- Added `handleHealthDataSaved` callback function
- Pass callback to LogHealthModal for immediate refresh

```typescript
// Add a callback to force refresh when health data is saved
const handleHealthDataSaved = () => {
  console.log("Health data saved, refreshing metrics...");
  fetchHealthData();
};

<LogHealthModal
  visible={isLogHealthModalVisible}
  onClose={() => setIsLogHealthModalVisible(false)}
  onDataSaved={handleHealthDataSaved} // ✅ Immediate refresh callback
/>;
```

### 3. Added Debug Logging

Added console logs to help track the data flow:

```typescript
console.log("Fetching health metrics for date:", todayString);
console.log("Generated health metrics:", metrics);
console.log("Health data saved, refreshing metrics...");
```

## Data Flow After Fix

1. **User enters data** in LogHealthModal (e.g., 5 glasses of water)
2. **Data is saved** to AsyncStorage under `hydration` key
3. **Callback is triggered** (`onDataSaved`) immediately after save
4. **Dashboard refreshes** health metrics by calling `fetchHealthData()`
5. **generateHealthMetrics** correctly maps `waterIntake` goal → `hydration` data
6. **Health card updates** to show the new value (5 glasses)

## Key Mapping Fixed

| Component      | Goal ID       | Data Storage Key | Display                |
| -------------- | ------------- | ---------------- | ---------------------- |
| Onboarding     | `waterIntake` | `hydration`      | "Water Intake"         |
| LogHealthModal | `waterIntake` | `hydration`      | "Hydration"            |
| HealthCard     | `waterIntake` | `hydration`      | "Hydration: 5 glasses" |

## Benefits Achieved

✅ **Real-time Updates**: Health cards now update immediately after saving data
✅ **Correct Data Mapping**: Fixed mismatch between goal IDs and storage keys
✅ **Better UX**: Users see their changes reflected instantly
✅ **Debug Visibility**: Added logging to track data flow
✅ **Consistent Behavior**: All health metrics update properly

## Testing Verification

To verify the fix:

1. Open LogHealthModal from dashboard
2. Enter health data (e.g., 5 glasses of water)
3. Save the data
4. Health card should immediately update to show "5 glasses"
5. Check browser console for debug logs confirming the flow

## Files Modified

- `utils/dashboard-utils.ts` - Fixed data mapping in generateHealthMetrics
- `components/health/LogHealthModal.tsx` - Added onDataSaved callback
- `app/(tabs)/index.tsx` - Added callback handler and debug logging
- `HEALTH_METRICS_UPDATE_FIX.md` - This documentation

## Future Improvements

- Consider standardizing goal IDs to match storage keys
- Add error handling for failed data saves
- Implement optimistic updates for better perceived performance
- Add unit tests for data mapping logic
