# AsyncStorage to SQLite Migration

## Overview

The Sobrix Health app has been updated to use SQLite instead of AsyncStorage for better performance, reliability, and storage capacity. This document explains the migration process and how it affects your data.

## What Changed

### Before (AsyncStorage)
- Data stored in browser/device local storage
- Limited storage capacity (especially on web)
- Prone to quota exceeded errors
- Simple key-value storage

### After (SQLite)
- Data stored in SQLite database
- Much larger storage capacity
- Better performance for complex queries
- Relational database with ACID compliance
- Automatic data integrity checks

## Migration Process

### Automatic Migration

When you update to the new version, the app will:

1. **Check for existing data** - Scan AsyncStorage for any existing app data
2. **Prompt for migration** - Show a migration dialog if data is found
3. **Migrate data safely** - Transfer all data to SQLite while preserving structure
4. **Clean up** - Remove old AsyncStorage data after successful migration
5. **Verify integrity** - Ensure all data was migrated correctly

### What Gets Migrated

The migration process handles:

- **User Profile Data** - All profile information, settings, and preferences
- **Quotes Data** - Custom quotes and daily quote preferences
- **Library Data** - Media files, documents, and library items
- **Health Data** - Daily health metrics and tracking data
- **Emergency Contacts** - Contact information and relationships
- **Mood Entries** - Mood tracking data and notes
- **Documents** - Personal documents and files
- **Media Files** - Photos, audio, and other media content

### Migration Safety

- **Non-destructive** - Original data is preserved until migration is confirmed successful
- **Rollback capable** - Can revert to AsyncStorage if issues occur
- **Data validation** - Ensures data integrity during transfer
- **Error handling** - Graceful handling of any migration issues

## Using the Migration

### Migration Dialog

When the app detects existing AsyncStorage data, you'll see a migration dialog with options:

- **Migrate Data** - Transfers all data to SQLite (recommended)
- **Skip for now** - Continue using the app without migrating (data may be lost)

### Migration Status

The dialog shows:
- Migration progress
- Which stores are being migrated
- Total number of records transferred
- Any errors that occur during migration

### After Migration

Once migration is complete:
- All your data is available in the new SQLite database
- App performance should be improved
- Storage quota issues should be resolved
- Old AsyncStorage data is cleaned up

## Troubleshooting

### Common Issues

1. **Migration fails**
   - Check device storage space
   - Ensure app has necessary permissions
   - Try restarting the app

2. **Data appears missing**
   - Check if migration completed successfully
   - Look for error messages in the migration dialog
   - Contact support if data is critical

3. **Performance issues**
   - SQLite should improve performance
   - If issues persist, try clearing app cache
   - Restart the app

### Manual Migration

If automatic migration fails, you can:

1. Export your data from the old version
2. Import it into the new version
3. Use the app's export/import features

### Getting Help

If you encounter issues:

1. Check the migration status in the app
2. Note any error messages
3. Try the migration process again
4. Contact support with specific error details

## Technical Details

### Database Schema

The new SQLite database includes:

- **Structured tables** for different data types
- **Indexes** for improved query performance
- **Foreign key constraints** for data integrity
- **Backup and restore** capabilities

### Storage Locations

- **iOS**: App's Documents directory
- **Android**: App's internal storage
- **Web**: IndexedDB with SQLite WASM

### Performance Benefits

- **Faster queries** for large datasets
- **Better memory usage** for complex operations
- **Reduced storage quota issues** on web platforms
- **Improved app startup time** with optimized data loading

## Developer Information

### Migration Service

The migration is handled by:
- `MigrationService` - Core migration logic
- `useMigration` hook - React integration
- `MigrationModal` - User interface
- `DatabaseContext` - App-wide database access

### Testing Migration

To test migration:
1. Use the old version to create test data
2. Update to the new version
3. Verify migration dialog appears
4. Confirm all data is transferred correctly

### Monitoring

The app logs migration progress and any issues to help with debugging and support. 