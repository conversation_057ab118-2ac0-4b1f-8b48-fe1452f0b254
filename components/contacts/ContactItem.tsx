import React from "react";
import { useRef } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  Platform, 
  Animated, 
  Pressable,
  Linking
} from 'react-native';
import { 
  Phone, 
  Edit2, 
  Trash2, 
  Mail,
  ChevronRight
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { 
  getContactCategoryInfo, 
  ContactCategory 
} from '@/utils';
interface ColorScheme {
  danger: string;
  info: string;
  success: string;
  warning: string;
  primary: string;
  textSecondary: string;
  textTertiary: string;
  card: string;
  cardGradient: string;
  border: string;
  text: string;
  muted: string;
  dangerDark?: string;
  infoDark?: string;
  successDark?: string;
  warningDark?: string;
}
interface Contact {
  id: string;
  name: string;
  phone: string;
  email?: string;
  relationship?: string;
  category?: ContactCategory;
  date?: string;
}
interface ContactItemProps {
  contact: Contact;
  onEdit: () => void;
  onDelete: () => void;
  onCall: () => void;
  colors: ColorScheme;
  language: string;
}
export const ContactItem: React.FC<ContactItemProps> = ({
  contact,
  onEdit,
  onDelete,
  onCall,
  colors,
  language,
}) => {
  // Animation values
  const scale = useRef(new Animated.Value(1)).current;
  const opacity = useRef(new Animated.Value(1)).current;
  
  // Get category information using shared utilities
  const categoryInfo = getContactCategoryInfo(contact.category || 'emergency', colors, language);
  
  const handlePressIn = () => {
    Animated.parallel([
      Animated.spring(scale, {
        toValue: 0.98,
        useNativeDriver: true,
        speed: 50,
        bounciness: 5,
      }),
      Animated.timing(opacity, {
        toValue: 0.8,
        duration: 150,
        useNativeDriver: true,
      })
    ]).start();
  };
  
  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(scale, {
        toValue: 1,
        useNativeDriver: true,
        speed: 50,
        bounciness: 5,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      })
    ]).start();
  };
  
  const handleEmailPress = () => {
    if (!contact.email) return;
    
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
    
    const emailUrl = `mailto:${contact.email}`;
    Linking.canOpenURL(emailUrl)
      .then(supported => {
        if (supported) {
          return Linking.openURL(emailUrl);
        }
      })
      .catch(err => console.error('Error opening email app:', err));
  };
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(language === 'nl' ? 'nl-NL' : 'en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch {
      return '';
    }
  };
  return (
    <Animated.View 
      style={[
        styles.container, 
        { 
          backgroundColor: colors.card, 
          borderColor: colors.border,
          transform: [{ scale }],
          opacity: opacity
        }
      ]}
    >
      <LinearGradient
        colors={[colors.card, colors.cardGradient] as [string, string]}
        style={styles.gradientBackground}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Pressable
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={styles.contactContent}
          onPress={onEdit}
        >
          <View style={styles.contactInfo}>
            <View style={styles.nameContainer}>
              <View style={[styles.categoryBadge, { backgroundColor: categoryInfo.color + '20' }]}>
                <categoryInfo.IconComponent size={20} color={categoryInfo.color} strokeWidth={2.5} />
                <Text style={[styles.categoryText, { color: categoryInfo.color }]}>
                  {categoryInfo.name}
                </Text>
              </View>
              <Text style={[styles.name, { color: colors.text }]}>{contact.name}</Text>
            </View>
            
            <View style={styles.detailsContainer}>
              <View style={styles.detailRow}>
                <Phone size={16} color={colors.muted} style={styles.detailIcon} strokeWidth={2} />
                <Text style={[styles.phone, { color: colors.textSecondary }]}>{contact.phone}</Text>
              </View>
              
              {contact.email && (
                <TouchableOpacity style={styles.detailRow} onPress={handleEmailPress}>
                  <Mail size={16} color={colors.muted} style={styles.detailIcon} strokeWidth={2} />
                  <Text style={[styles.email, { color: colors.primary }]}>{contact.email}</Text>
                </TouchableOpacity>
              )}
              
              {contact.relationship && (
                <View style={styles.detailRow}>
                  <Text style={[styles.relationship, { color: colors.textSecondary }]}>
                    {contact.relationship}
                  </Text>
                </View>
              )}
              
              {contact.date && (
                <Text style={[styles.date, { color: colors.textTertiary }]}>
                  {language === 'nl' ? 'Toegevoegd op ' : 'Added on '} 
                  {formatDate(contact.date)}
                </Text>
              )}
            </View>
          </View>
          
          <ChevronRight size={20} color={colors.muted} strokeWidth={2} />
        </Pressable>
        
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary + '20' }]}
            onPress={() => {
              if (Platform.OS !== 'web') {
                Haptics.selectionAsync();
              }
              onCall();
            }}
          >
            <Phone size={18} color={colors.primary} strokeWidth={2.5} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.warning + '20' }]}
            onPress={() => {
              if (Platform.OS !== 'web') {
                Haptics.selectionAsync();
              }
              onEdit();
            }}
          >
            <Edit2 size={18} color={colors.warning} strokeWidth={2.5} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.danger + '20' }]}
            onPress={() => {
              if (Platform.OS !== 'web') {
                Haptics.selectionAsync();
              }
              onDelete();
            }}
          >
            <Trash2 size={18} color={colors.danger} strokeWidth={2.5} />
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </Animated.View>
  );
};
const styles = StyleSheet.create({


  actionButton: {
    alignItems: 'center',
    borderRadius: 14,
    height: 44,
    justifyContent: 'center',
    width: 44,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    justifyContent: 'flex-end',
    padding: 16,
    paddingTop: 0,
  },
  categoryBadge: {
    alignItems: 'center',
    borderRadius: 20,
    flexDirection: 'row',
    marginBottom: 6,
    marginRight: 12,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '600',
    letterSpacing: 0.3,
    marginLeft: 6,
  },
  contactContent: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingBottom: 12,
  },
  contactInfo: {
    flex: 1,
  },
  container: {
    borderRadius: 16,
    borderWidth: 1,
    elevation: 4,
    marginBottom: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  date: {
    fontSize: 13,
    fontWeight: '400',
    marginTop: 4,
  },
  detailIcon: {
    marginRight: 8,
  },
  detailRow: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 6,
  },
  detailsContainer: {
    marginBottom: 8,
  },
  email: {
    fontSize: 15,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  gradientBackground: {
    width: '100%',
  },
  name: {
    flex: 1,
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  nameContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  phone: {
    fontSize: 16,
    fontWeight: '500',
  },
  relationship: {
    fontSize: 15,
    fontStyle: 'italic',
    fontWeight: '400',
  },


});