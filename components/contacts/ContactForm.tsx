import React from "react";
import { useState, useRef, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  ScrollView, 
  Platform,
  Animated,
  Keyboard,
  ActivityIndicator
} from 'react-native';
import { X as XIcon, Save, Phone, Check } from 'lucide-react-native';
import { useUserStore } from '@/store/user/user-store';
import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  contactCategories, 
  getContactCategoryInfo, 
  ContactCategory 
} from '@/utils';
import { modalStyles } from '@/style/modal';
interface ColorScheme {
  primary: string;
  danger: string;
  info: string;
  success: string;
  warning: string;
  card: string;
  cardGradient: string;
  background: string;
  border: string;
  text: string;
  muted: string;
  primaryDark?: string;
  dangerDark?: string;
  infoDark?: string;
  successDark?: string;
  warningDark?: string;
}
interface Contact {
  id?: string;
  name: string;
  phone: string;
  email?: string;
  relationship?: string;
  category?: ContactCategory;
  date: string;
}
interface ContactFormProps {
  onClose: () => void;
  colors: ColorScheme;
  initialContact?: Contact;
  language: string;
}
export const ContactForm: React.FC<ContactFormProps> = ({
  onClose,
  colors,
  initialContact,
  language,
}) => {
  const { addEmergencyContact, editEmergencyContact } = useUserStore();
  
  const [name, setName] = useState(initialContact?.name || '');
  const [phone, setPhone] = useState(initialContact?.phone || '');
  const [email, setEmail] = useState(initialContact?.email || '');
  const [relationship, setRelationship] = useState(initialContact?.relationship || '');
  const [category, setCategory] = useState<ContactCategory>(initialContact?.category || 'emergency');
  const [isSaving, setIsSaving] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errors, setErrors] = useState<{name?: string; phone?: string}>({});
  
  // Animation values
  const formOpacity = useRef(new Animated.Value(0)).current;
  const formTranslateY = useRef(new Animated.Value(50)).current;
  const successScale = useRef(new Animated.Value(0)).current;
  
  // Refs for inputs
  const phoneInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const relationshipInputRef = useRef<TextInput>(null);
  
  useEffect(() => {
    // Animate form in
    Animated.parallel([
      Animated.timing(formOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(formTranslateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
    
    // Add keyboard listeners
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {}
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {}
    );
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, [formOpacity, formTranslateY]);
  
  const validateForm = () => {
    const newErrors: {name?: string; phone?: string} = {};
    
    if (!name.trim()) {
      newErrors.name = language === 'nl' ? 'Naam is verplicht' : 'Name is required';
    }
    
    if (!phone.trim()) {
      newErrors.phone = language === 'nl' ? 'Telefoonnummer is verplicht' : 'Phone number is required';
    } else if (!/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/im.test(phone.trim())) {
      newErrors.phone = language === 'nl' ? 'Ongeldig telefoonnummer' : 'Invalid phone number';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSave = async () => {
    if (!validateForm()) {
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
      return;
    }
    
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
    
    setIsSaving(true);
    
    try {
      if (initialContact) {
        // Edit existing contact
        await editEmergencyContact(initialContact.id!, {
          name,
          phone,
          email,
          relationship,
          category,
          date: initialContact.date || new Date().toISOString(),
        });
      } else {
        // Add new contact
        await addEmergencyContact({
          name,
          phone,
          email,
          relationship,
          category,
          date: new Date().toISOString(),
        });
      }
      
      setIsSaving(false);
      setIsSuccess(true);
      
      // Animate success icon
      Animated.sequence([
        Animated.timing(successScale, {
          toValue: 1.2,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(successScale, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
      
      // Close form after success animation
      setTimeout(() => {
        onClose();
      }, 1000);
      
    } catch (_error) {
      console.error('Error saving contact:', _error);
      setIsSaving(false);
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
    }
  };
  
  const isFormValid = name.trim() !== '' && phone.trim() !== '';
  const categoriesWithInfo = contactCategories.map(cat => {
    const info = getContactCategoryInfo(cat.id, colors, language || 'en');
    return {
      id: cat.id,
      label: language === 'nl' ? cat.labelNL : cat.label,
      IconComponent: info.IconComponent,
      color: info.color,
      gradient: info.gradient,
    };
  });
  
  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={[modalStyles.modalHeader, { borderBottomColor: colors.border }]}>
        <TouchableOpacity 
          onPress={onClose}
          style={[styles.headerButton, { backgroundColor: colors.background }]}
          disabled={isSaving}
        >
          <XIcon size={24} color={isSaving ? colors.muted : colors.text} strokeWidth={2.5} />
        </TouchableOpacity>
        
        <Text style={[modalStyles.modalTitle, { color: colors.text }]}>
          {initialContact 
            ? (language === 'nl' ? 'Contact bewerken' : 'Edit Contact')
            : (language === 'nl' ? 'Contact toevoegen' : 'Add Contact')}
        </Text>
        
        <TouchableOpacity 
          onPress={handleSave}
          disabled={!isFormValid || isSaving || isSuccess}
          style={[
            styles.headerButton,
            { 
              backgroundColor: isFormValid && !isSaving && !isSuccess ? colors.primary : colors.border
            },
            (!isFormValid || isSaving || isSuccess) && styles.headerButtonDisabled
          ]}
        >
          <Save size={24} color="#fff" strokeWidth={2.5} />
        </TouchableOpacity>
      </View>
      
      {isSuccess ? (
        <View style={styles.successContainer}>
          <Animated.View style={{ transform: [{ scale: successScale }] }}>
            <View style={[styles.successIconContainer, { backgroundColor: colors.success }]}>
              <Check size={40} color="#fff" strokeWidth={2.5} />
            </View>
          </Animated.View>
          <Text style={[styles.successText, { color: colors.text }]}>
            {language === 'nl' 
              ? 'Contact succesvol opgeslagen!' 
              : 'Contact saved successfully!'}
          </Text>
        </View>
      ) : (
        <ScrollView 
          style={modalStyles.modalBodyScrollable}
          showsVerticalScrollIndicator={true}
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {language === 'nl' ? 'Naam' : 'Name'}*
            </Text>
            <TextInput
              style={[
                styles.input, 
                { 
                  backgroundColor: colors.background,
                  color: colors.text,
                  borderColor: errors.name ? colors.danger : colors.border
                }
              ]}
              value={name}
              onChangeText={(text) => {
                setName(text);
                if (errors.name) {
                  setErrors({...errors, name: undefined});
                }
              }}
              placeholder={language === 'nl' ? 'Voer naam in' : 'Enter name'}
              placeholderTextColor={colors.muted}
              returnKeyType="next"
              onSubmitEditing={() => phoneInputRef.current?.focus()}
              editable={!isSaving}
            />
            {errors.name && (
              <Text style={[styles.errorText, { color: colors.danger }]}>
                {errors.name}
              </Text>
            )}
          </View>
          
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {language === 'nl' ? 'Telefoonnummer' : 'Phone Number'}*
            </Text>
            <TextInput
              ref={phoneInputRef}
              style={[
                styles.input, 
                { 
                  backgroundColor: colors.background,
                  color: colors.text,
                  borderColor: errors.phone ? colors.danger : colors.border
                }
              ]}
              value={phone}
              onChangeText={(text) => {
                setPhone(text);
                if (errors.phone) {
                  setErrors({...errors, phone: undefined});
                }
              }}
              placeholder={language === 'nl' ? 'Voer telefoonnummer in' : 'Enter phone number'}
              placeholderTextColor={colors.muted}
              keyboardType="phone-pad"
              returnKeyType="next"
              onSubmitEditing={() => emailInputRef.current?.focus()}
              editable={!isSaving}
            />
            {errors.phone && (
              <Text style={[styles.errorText, { color: colors.danger }]}>
                {errors.phone}
              </Text>
            )}
          </View>
          
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {language === 'nl' ? 'E-mail' : 'Email'}
            </Text>
            <TextInput
              ref={emailInputRef}
              style={[styles.input, { 
                backgroundColor: colors.background,
                color: colors.text,
                borderColor: colors.border
              }]}
              value={email}
              onChangeText={setEmail}
              placeholder={language === 'nl' ? 'Voer e-mail in' : 'Enter email'}
              placeholderTextColor={colors.muted}
              keyboardType="email-address"
              autoCapitalize="none"
              returnKeyType="next"
              onSubmitEditing={() => relationshipInputRef.current?.focus()}
              editable={!isSaving}
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {language === 'nl' ? 'Relatie' : 'Relationship'}
            </Text>
            <TextInput
              ref={relationshipInputRef}
              style={[styles.input, { 
                backgroundColor: colors.background,
                color: colors.text,
                borderColor: colors.border
              }]}
              value={relationship}
              onChangeText={setRelationship}
              placeholder={language === 'nl' ? 'Bijv. vriend, familie, sponsor' : 'E.g. friend, family, sponsor'}
              placeholderTextColor={colors.muted}
              returnKeyType="done"
              editable={!isSaving}
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {language === 'nl' ? 'Categorie' : 'Category'}
            </Text>
            <View style={styles.categoryOptions}>
              {categoriesWithInfo.map((cat) => {
                const isSelected = category === cat.id;
                
                return (
                  <TouchableOpacity
                    key={cat.id}
                    style={[
                      styles.categoryOption,
                      { 
                        backgroundColor: isSelected ? cat.color + '15' : colors.background,
                        borderColor: isSelected ? cat.color : colors.border
                      }
                    ]}
                    onPress={() => {
                      if (!isSaving) {
                        setCategory(cat.id);
                        if (Platform.OS !== 'web') {
                          Haptics.selectionAsync();
                        }
                      }
                    }}
                    disabled={isSaving}
                  >
                    {isSelected ? (
                      <LinearGradient
                        colors={cat.gradient}
                        style={styles.categoryGradient}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                      >
                        <cat.IconComponent 
                          size={18} 
                          color="#fff" 
                          style={styles.categoryIcon}
                          strokeWidth={2.5}
                        />
                        <Text 
                          style={[styles.categoryText, styles.categoryTextWhite]}
                        >
                          {cat.label}
                        </Text>
                      </LinearGradient>
                    ) : (
                      <>
                        <cat.IconComponent 
                          size={18} 
                          color={cat.color} 
                          style={styles.categoryIcon}
                          strokeWidth={2}
                        />
                        <Text 
                          style={[styles.categoryText, { color: colors.text }]}
                        >
                          {cat.label}
                        </Text>
                      </>
                    )}
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
          
          <View style={[styles.infoContainer, { backgroundColor: colors.primary + '10' }]}>
            <Phone size={20} color={colors.primary} strokeWidth={2} />
            <Text style={[styles.infoText, { color: colors.text }]}>
              {language === 'nl' 
                ? 'Noodcontacten zijn direct toegankelijk vanaf het dashboard.'
                : 'Emergency contacts are directly accessible from the dashboard.'}
            </Text>
          </View>
          
          <TouchableOpacity
            style={[
              styles.saveButton,
              { backgroundColor: colors.primary },
              (!isFormValid || isSaving) && styles.saveButtonDisabled
            ]}
            onPress={handleSave}
            disabled={!isFormValid || isSaving}
          >
            <LinearGradient
              colors={[colors.primary, colors.primaryDark || colors.primary] as const}
              style={styles.saveButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              {isSaving ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.saveButtonText}>
                  {language === 'nl' ? 'Contact opslaan' : 'Save Contact'}
                </Text>
              )}
            </LinearGradient>
          </TouchableOpacity>
          
          <Text style={[styles.requiredFieldsNote, { color: colors.muted }]}>
            * {language === 'nl' ? 'Verplichte velden' : 'Required fields'}
          </Text>
        </ScrollView>
      )}
    </View>
  );
};
const styles = StyleSheet.create({


  categoryGradient: {
    alignItems: 'center',
    borderRadius: 12,
    flexDirection: 'row',
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  categoryIcon: {
    marginRight: 8,
  },
  categoryOption: {
    alignItems: 'center',
    borderRadius: 16,
    borderWidth: 2,
    flexDirection: 'row',
    overflow: 'hidden',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  categoryOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: -0.1,
  },
  categoryTextWhite: {
    color: '#fff',
  },
  container: {
    flex: 1,
    minHeight: 600,
  },
  errorText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    marginTop: 6,
  },
  formGroup: {
    marginBottom: 20,
  },
  headerButton: {
    alignItems: 'center',
    borderRadius: 22,
    height: 44,
    justifyContent: 'center',
    width: 44,
  },
  headerButtonDisabled: {
    opacity: 0.5,
  },
  infoContainer: {
    alignItems: 'center',
    borderRadius: 16,
    flexDirection: 'row',
    marginBottom: 24,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
    marginLeft: 12,
  },
  input: {
    borderRadius: 16,
    borderWidth: 2,
    fontSize: 16,
    fontWeight: '500',
    padding: 16,
  },
  label: {
    fontSize: 17,
    fontWeight: '600',
    letterSpacing: -0.2,
    marginBottom: 8,
  },
  requiredFieldsNote: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 16,
    textAlign: 'center',
  },
  saveButton: {
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  saveButtonGradient: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  successIconContainer: {
    alignItems: 'center',
    borderRadius: 44,
    height: 88,
    justifyContent: 'center',
    marginBottom: 20,
    width: 88,
  },
  successText: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
    textAlign: 'center',
  },


});