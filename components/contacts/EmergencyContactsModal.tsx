import React from "react";
import { useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Linking,
  Platform,
  Animated,
} from "react-native";
import { X, Phone, Plus, AlertTriangle } from "lucide-react-native";
import { useUserStore } from "@/store/user/user-store";
import * as Haptics from "expo-haptics";
import { router } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useModalAnimation } from "@/hooks";
interface EmergencyContactsModalProps {
  onClose: () => void;
  colors: {
    primary: string;
    danger: string;
    card: string;
    border: string;
    text: string;
    muted: string;
  };
  language: string;
}
export const EmergencyContactsModal: React.FC<EmergencyContactsModalProps> = ({
  onClose,
  colors,
  language,
}) => {
  const { profile } = useUserStore();
  const insets = useSafeAreaInsets();
  const { animatedStyle, showModal, hideModal } = useModalAnimation();
  useEffect(() => {
    showModal();
  }, [showModal]);
  // Filter only emergency contacts
  const emergencyContacts =
    profile?.emergencyContacts?.filter(
      (contact) => contact.category === "emergency"
    ) || [];
  const handleCallContact = (phone: string) => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    const phoneUrl = `tel:${phone}`;
    Linking.canOpenURL(phoneUrl)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(phoneUrl);
        }
      })
      .catch((_err) => console.error("Error opening phone app:", _err));
  };
  const handleEmailContact = (email: string) => {
    if (!email) return;
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
    const emailUrl = `mailto:${email}`;
    Linking.canOpenURL(emailUrl)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(emailUrl);
        }
      })
      .catch((_err) => console.error("Error opening email app:", _err));
  };
  const handleAddContact = async () => {
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
    await hideModal();
    onClose();
    router.push("/(tabs)/contacts");
  };
  const handleCloseModal = async () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    await hideModal();
    onClose();
  };
  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <Animated.View
        style={[
          styles.modalContent,
          {
            backgroundColor: colors.card,
          },
          animatedStyle,
        ]}
      >
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <View style={styles.headerTitleContainer}>
            <AlertTriangle
              size={20}
              color={colors.danger}
              style={styles.headerIcon}
            />
            <Text style={[styles.title, { color: colors.text }]}>
              {language === "nl" ? "Noodcontacten" : "Emergency Contacts"}
            </Text>
          </View>
          <TouchableOpacity
            onPress={handleCloseModal}
            style={styles.closeButton}
          >
            <X size={24} color={colors.text} />
          </TouchableOpacity>
        </View>
        {emergencyContacts.length === 0 ? (
          <View style={styles.emptyContainer}>
            <View
              style={[
                styles.emptyIconContainer,
                { backgroundColor: colors.danger + "20" },
              ]}
            >
              <AlertTriangle size={32} color={colors.danger} />
            </View>
            <Text style={[styles.emptyTitle, { color: colors.text }]}>
              {language === "nl"
                ? "Geen noodcontacten"
                : "No Emergency Contacts"}
            </Text>
            <Text style={[styles.emptyText, { color: colors.muted }]}>
              {language === "nl"
                ? "Voeg noodcontacten toe voor snelle toegang in geval van nood."
                : "Add emergency contacts for quick access in case of emergency."}
            </Text>
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: colors.primary }]}
              onPress={handleAddContact}
            >
              <Plus size={18} color="#fff" style={styles.addIcon} />
              <Text style={styles.addButtonText}>
                {language === "nl" ? "Contact toevoegen" : "Add Contact"}
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={emergencyContacts}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View
                style={[
                  styles.contactItem,
                  { borderBottomColor: colors.border },
                ]}
              >
                <View style={styles.contactInfo}>
                  <Text style={[styles.contactName, { color: colors.text }]}>
                    {item.name}
                  </Text>
                  <Text style={[styles.contactPhone, { color: colors.muted }]}>
                    {item.phone}
                  </Text>
                  {item.email && (
                    <TouchableOpacity
                      onPress={() => handleEmailContact(item.email!)}
                    >
                      <Text
                        style={[styles.contactEmail, { color: colors.primary }]}
                      >
                        {item.email}
                      </Text>
                    </TouchableOpacity>
                  )}
                  {item.relationship && (
                    <Text
                      style={[
                        styles.contactRelationship,
                        { color: colors.text },
                      ]}
                    >
                      {item.relationship}
                    </Text>
                  )}
                </View>
                <TouchableOpacity
                  style={[
                    styles.callButton,
                    { backgroundColor: colors.primary },
                  ]}
                  onPress={() => handleCallContact(item.phone)}
                >
                  <Phone size={20} color="#fff" />
                </TouchableOpacity>
              </View>
            )}
            contentContainerStyle={styles.contactsList}
            ListFooterComponent={
              <TouchableOpacity
                style={[styles.manageButton, { borderColor: colors.primary }]}
                onPress={handleAddContact}
              >
                <Text
                  style={[styles.manageButtonText, { color: colors.primary }]}
                >
                  {language === "nl" ? "Contacten beheren" : "Manage Contacts"}
                </Text>
              </TouchableOpacity>
            }
          />
        )}
      </Animated.View>
    </View>
  );
};
const styles = StyleSheet.create({


  addButton: {
    alignItems: "center",
    borderRadius: 8,
    flexDirection: "row",
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  addButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  addIcon: {
    marginRight: 8,
  },
  callButton: {
    alignItems: "center",
    borderRadius: 24,
    height: 48,
    justifyContent: "center",
    marginLeft: 16,
    width: 48,
  },
  closeButton: {
    alignItems: "center",
    borderRadius: 20,
    height: 40,
    justifyContent: "center",
    width: 40,
  },
  contactEmail: {
    fontSize: 14,
    marginBottom: 2,
    textDecorationLine: "underline",
  },
  contactInfo: {
    flex: 1,
  },
  contactItem: {
    alignItems: "center",
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 16,
  },
  contactName: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  contactPhone: {
    fontSize: 16,
    marginBottom: 2,
  },
  contactRelationship: {
    fontSize: 14,
    fontStyle: "italic",
  },
  contactsList: {
    padding: 16,
  },
  container: {
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
    flex: 1,
    justifyContent: "center",
    padding: 20,
  },
  emptyContainer: {
    alignItems: "center",
    padding: 24,
  },
  emptyIconContainer: {
    alignItems: "center",
    borderRadius: 32,
    height: 64,
    justifyContent: "center",
    marginBottom: 16,
    width: 64,
  },
  emptyText: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: "center",
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 8,
    textAlign: "center",
  },
  header: {
    alignItems: "center",
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
  },
  headerIcon: {
    marginRight: 8,
  },
  headerTitleContainer: {
    alignItems: "center",
    flexDirection: "row",
  },
  manageButton: {
    alignItems: "center",
    borderRadius: 8,
    borderWidth: 1,
    marginTop: 24,
    paddingVertical: 14,
  },
  manageButtonText: {
    fontSize: 16,
    fontWeight: "500",
  },
  modalContent: {
    borderRadius: 16,
    maxHeight: "80%",
    overflow: "hidden",
    width: "100%",
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
  },


});