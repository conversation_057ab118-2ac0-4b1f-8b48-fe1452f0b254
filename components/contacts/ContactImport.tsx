import React, { useState, useEffect, useCallback } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  Platform,
  KeyboardAvoidingView,
  TextInput,
} from "react-native";
import {
  X,
  Check,
  Users,
  Search,
  Download,
  AlertTriangle,
} from "lucide-react-native";
import * as Contacts from "expo-contacts";
import * as Haptics from "expo-haptics";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useUserStore } from "@/store/user/user-store";

interface ContactImportProps {
  onClose: () => void;
  colors: {
    primary: string;
    warning: string;
    card: string;
    border: string;
    text: string;
    muted: string;
    background: string;
  };
  language: string;
}

interface DeviceContact {
  id: string;
  name: string;
  phoneNumbers?: { label: string; number: string }[];
  emails?: { label: string; email: string }[];
  selected?: boolean;
}

export const ContactImport: React.FC<ContactImportProps> = ({
  onClose,
  colors,
  language,
}) => {
  const { addEmergencyContact } = useUserStore();
  const insets = useSafeAreaInsets();
  const [deviceContacts, setDeviceContacts] = useState<DeviceContact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<DeviceContact[]>([]);
  const [loading, setLoading] = useState(true);
  const [importing, setImporting] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [permissionDenied, setPermissionDenied] = useState(false);
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);

  const loadContacts = useCallback(async () => {
    try {
      setLoading(true);
      const { status } = await Contacts.requestPermissionsAsync();
      if (status !== "granted") {
        setPermissionDenied(true);
        setLoading(false);
        return;
      }
      const { data } = await Contacts.getContactsAsync({
        fields: [
          Contacts.Fields.Name,
          Contacts.Fields.PhoneNumbers,
          Contacts.Fields.Emails,
        ],
        sort: Contacts.SortTypes.FirstName,
      });
      if (data.length > 0) {
        // Format contacts to our app's format and filter out contacts without IDs
        const formattedContacts = data
          .filter(
            (contact) =>
              contact.name &&
              contact.phoneNumbers &&
              contact.phoneNumbers.length > 0 &&
              contact.id
          )
          .map((contact) => ({
            id: contact.id || `temp-${Date.now()}-${Math.random()}`, // Ensure ID is always a string
            name: contact.name || "Unknown",
            phoneNumbers: contact.phoneNumbers?.map((phone) => ({
              label: phone.label || "mobile",
              number: phone.number || "",
            })),
            emails: contact.emails?.map((email) => ({
              label: email.label || "home",
              email: email.email || "",
            })),
            selected: false,
          }));
        setDeviceContacts(formattedContacts);
        setFilteredContacts(formattedContacts);
      }
      setLoading(false);
    } catch (_error) {
      console.error("Error loading contacts:", _error);
      setLoading(false);
      Alert.alert(
        language === "nl" ? "Fout" : "Error",
        language === "nl"
          ? "Er is een fout opgetreden bij het laden van contacten."
          : "An error occurred while loading contacts."
      );
    }
  }, [language]);

  const filterContacts = useCallback(() => {
    if (!searchQuery.trim()) {
      setFilteredContacts(deviceContacts);
      return;
    }
    const query = searchQuery.toLowerCase().trim();
    const filtered = deviceContacts.filter((contact) => {
      const nameMatch = contact.name.toLowerCase().includes(query);
      const phoneMatch = contact.phoneNumbers?.some((phone) =>
        phone.number.toLowerCase().includes(query)
      );
      const emailMatch = contact.emails?.some((email) =>
        email.email.toLowerCase().includes(query)
      );
      return nameMatch || phoneMatch || emailMatch;
    });
    setFilteredContacts(filtered);
  }, [searchQuery, deviceContacts]);

  useEffect(() => {
    loadContacts();
  }, [loadContacts]);

  useEffect(() => {
    if (deviceContacts.length > 0) {
      filterContacts();
    }
  }, [filterContacts, deviceContacts.length]);

  const toggleContactSelection = (id: string) => {
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
    setSelectedContacts((prev) => {
      if (prev.includes(id)) {
        return prev.filter((contactId) => contactId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  const selectAllContacts = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    if (selectedContacts.length === filteredContacts.length) {
      // Deselect all
      setSelectedContacts([]);
    } else {
      // Select all
      setSelectedContacts(filteredContacts.map((contact) => contact.id));
    }
  };

  const importSelectedContacts = async () => {
    if (selectedContacts.length === 0) {
      Alert.alert(
        language === "nl"
          ? "Geen contacten geselecteerd"
          : "No Contacts Selected",
        language === "nl"
          ? "Selecteer ten minste één contact om te importeren."
          : "Please select at least one contact to import."
      );
      return;
    }
    if (Platform.OS !== "web") {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
    setImporting(true);
    try {
      // Get selected contacts
      const contactsToImport = deviceContacts.filter((contact) =>
        selectedContacts.includes(contact.id)
      );
      // Import each contact
      for (const contact of contactsToImport) {
        if (contact.phoneNumbers && contact.phoneNumbers.length > 0) {
          const primaryPhone = contact.phoneNumbers[0].number;
          const primaryEmail =
            contact.emails && contact.emails.length > 0
              ? contact.emails[0].email
              : undefined;
          await addEmergencyContact({
            name: contact.name,
            phone: primaryPhone,
            email: primaryEmail,
            relationship: "",
            category: "emergency", // Default to emergency
            date: new Date().toISOString(),
          });
        }
      }
      // Show success message
      Alert.alert(
        language === "nl" ? "Contacten geïmporteerd" : "Contacts Imported",
        language === "nl"
          ? `${contactsToImport.length} contacten succesvol geïmporteerd.`
          : `${contactsToImport.length} contacts successfully imported.`,
        [
          {
            text: "OK",
            onPress: onClose,
          },
        ]
      );
      setImporting(false);
    } catch (_error) {
      console.error("Error importing contacts:", _error);
      setImporting(false);
      Alert.alert(
        language === "nl" ? "Fout" : "Error",
        language === "nl"
          ? "Er is een fout opgetreden bij het importeren van contacten."
          : "An error occurred while importing contacts."
      );
    }
  };

  const renderContactItem = ({ item }: { item: DeviceContact }) => {
    const isSelected = selectedContacts.includes(item.id);
    const primaryPhone =
      item.phoneNumbers && item.phoneNumbers.length > 0
        ? item.phoneNumbers[0].number
        : "";
    return (
      <TouchableOpacity
        style={[
          styles.contactItem,
          {
            backgroundColor: colors.card,
            borderColor: isSelected ? colors.primary : colors.border,
          },
        ]}
        onPress={() => toggleContactSelection(item.id)}
      >
        <View style={styles.contactInfo}>
          <Text style={[styles.contactName, { color: colors.text }]}>
            {item.name}
          </Text>
          {primaryPhone && (
            <Text style={[styles.contactPhone, { color: colors.muted }]}>
              {primaryPhone}
            </Text>
          )}
        </View>
        <View
          style={[
            styles.checkbox,
            {
              borderColor: isSelected ? colors.primary : colors.border,
            },
            isSelected && { backgroundColor: colors.primary },
          ]}
        >
          {isSelected && <Check size={16} color="#fff" />}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { paddingBottom: insets.bottom }]}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
    >
      <View style={[styles.content, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <TouchableOpacity
            onPress={onClose}
            style={styles.closeButton}
            disabled={importing}
          >
            <X size={24} color={importing ? colors.muted : colors.text} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.text }]}>
            {language === "nl" ? "Contacten importeren" : "Import Contacts"}
          </Text>
          <View style={styles.spacer} />
        </View>
        <View style={styles.searchContainer}>
          <View
            style={[
              styles.searchInputContainer,
              { backgroundColor: colors.card, borderColor: colors.border },
            ]}
          >
            <Search size={18} color={colors.muted} style={styles.searchIcon} />
            <TextInput
              style={[styles.searchInput, { color: colors.text }]}
              placeholder={language === "nl" ? "Zoeken..." : "Search..."}
              placeholderTextColor={colors.muted}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery("")}>
                <X size={18} color={colors.muted} />
              </TouchableOpacity>
            )}
          </View>
        </View>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.text }]}>
              {language === "nl" ? "Contacten laden..." : "Loading contacts..."}
            </Text>
          </View>
        ) : permissionDenied ? (
          <View style={styles.permissionContainer}>
            <AlertTriangle size={48} color={colors.warning} />
            <Text style={[styles.permissionTitle, { color: colors.text }]}>
              {language === "nl" ? "Toegang geweigerd" : "Permission Denied"}
            </Text>
            <Text style={[styles.permissionText, { color: colors.muted }]}>
              {language === "nl"
                ? "We hebben toegang tot je contacten nodig om ze te kunnen importeren. Ga naar je apparaatinstellingen om toegang te verlenen."
                : "We need access to your contacts to import them. Please go to your device settings to grant permission."}
            </Text>
            <TouchableOpacity
              style={[
                styles.permissionButton,
                { backgroundColor: colors.primary },
              ]}
              onPress={loadContacts}
            >
              <Text style={styles.permissionButtonText}>
                {language === "nl" ? "Opnieuw proberen" : "Try Again"}
              </Text>
            </TouchableOpacity>
          </View>
        ) : filteredContacts.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Users size={48} color={colors.muted} />
            <Text style={[styles.emptyText, { color: colors.muted }]}>
              {searchQuery.length > 0
                ? language === "nl"
                  ? "Geen contacten gevonden voor je zoekopdracht."
                  : "No contacts found for your search."
                : language === "nl"
                ? "Geen contacten gevonden op dit apparaat."
                : "No contacts found on this device."}
            </Text>
            {searchQuery.length > 0 && (
              <TouchableOpacity
                style={[
                  styles.clearSearchButton,
                  { borderColor: colors.primary },
                ]}
                onPress={() => setSearchQuery("")}
              >
                <Text
                  style={[styles.clearSearchText, { color: colors.primary }]}
                >
                  {language === "nl" ? "Zoekopdracht wissen" : "Clear search"}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <>
            <View
              style={[
                styles.selectionHeader,
                { borderBottomColor: colors.border },
              ]}
            >
              <TouchableOpacity
                style={styles.selectAllButton}
                onPress={selectAllContacts}
              >
                <View
                  style={[
                    styles.checkbox,
                    {
                      borderColor:
                        selectedContacts.length === filteredContacts.length
                          ? colors.primary
                          : colors.border,
                    },
                    selectedContacts.length === filteredContacts.length && { backgroundColor: colors.primary },
                  ]}
                >
                  {selectedContacts.length === filteredContacts.length && (
                    <Check size={16} color="#fff" />
                  )}
                </View>
                <Text style={[styles.selectAllText, { color: colors.text }]}>
                  {language === "nl" ? "Selecteer alles" : "Select All"}
                </Text>
              </TouchableOpacity>
              <Text style={[styles.selectionCount, { color: colors.muted }]}>
                {selectedContacts.length} / {filteredContacts.length}
              </Text>
            </View>
            <FlatList
              data={filteredContacts}
              renderItem={renderContactItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.contactsList}
              showsVerticalScrollIndicator={false}
            />
          </>
        )}
        {!loading && !permissionDenied && filteredContacts.length > 0 && (
          <View style={[styles.footer, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[
                styles.importButton,
                { backgroundColor: colors.primary },
                (selectedContacts.length === 0 || importing) && styles.importButtonDisabled,
              ]}
              onPress={importSelectedContacts}
              disabled={selectedContacts.length === 0 || importing}
            >
              {importing ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <>
                  <Download size={20} color="#fff" style={styles.importIcon} />
                  <Text style={styles.importButtonText}>
                    {language === "nl"
                      ? `Importeer ${selectedContacts.length} contacten`
                      : `Import ${selectedContacts.length} Contacts`}
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  checkbox: {
    alignItems: "center",
    borderRadius: 12,
    borderWidth: 2,
    height: 24,
    justifyContent: "center",
    width: 24,
  },


  clearSearchButton: {
    borderRadius: 8,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  clearSearchText: {
    fontWeight: "500",
  },
  closeButton: {
    alignItems: "center",
    borderRadius: 20,
    height: 40,
    justifyContent: "center",
    width: 40,
  },
  contactInfo: {
    flex: 1,
  },
  contactItem: {
    alignItems: "center",
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
    padding: 12,
  },
  contactName: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  contactPhone: {
    fontSize: 14,
  },
  contactsList: {
    padding: 16,
  },
  container: {
    backgroundColor: "rgba(0,0,0,0.5)",
    flex: 1,
    justifyContent: "flex-end",
  },
  content: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: "90%",
    overflow: "hidden",
  },
  emptyContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    marginBottom: 16,
    marginTop: 16,
    textAlign: "center",
  },
  footer: {
    borderTopWidth: 1,
    padding: 16,
  },
  header: {
    alignItems: "center",
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
  },
  importButton: {
    alignItems: "center",
    borderRadius: 12,
    flexDirection: "row",
    justifyContent: "center",
    paddingVertical: 14,
  },
  importButtonDisabled: {
    opacity: 0.5,
  },
  importButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  importIcon: {
    marginRight: 8,
  },
  loadingContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  permissionButton: {
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  permissionButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  permissionContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    padding: 24,
  },
  permissionText: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: "center",
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 8,
    marginTop: 16,
  },
  searchContainer: {
    padding: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    height: 40,
  },
  searchInputContainer: {
    alignItems: "center",
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: "row",
    height: 40,
    paddingHorizontal: 12,
  },
  selectAllButton: {
    alignItems: "center",
    flexDirection: "row",
  },
  selectAllText: {
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 8,
  },
  selectionCount: {
    fontSize: 14,
  },
  selectionHeader: {
    alignItems: "center",
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  spacer: {
    width: 40,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
  },
});