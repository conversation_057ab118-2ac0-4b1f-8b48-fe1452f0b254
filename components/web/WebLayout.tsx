import React from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { StatusBar } from 'expo-status-bar';

export function WebLayout({ children }: { children: React.ReactNode }) {
  const colorScheme = useColorScheme();
  
  // Only render this wrapper on web platform
  if (Platform.OS !== 'web') {
    return <>{children}</>;
  }
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.headerText, { color: colorScheme === 'dark' ? '#fff' : '#000' }]}>
          SobrixHealth
        </Text>
      </View>
      <View style={styles.content}>
        {children}
      </View>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    maxWidth: 1200,
    marginHorizontal: 'auto',
    width: '100%',
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  headerText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  }
});