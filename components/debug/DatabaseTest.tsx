import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '@/context/theme-context';
import { useDatabaseContext } from '@/context/database-context';
import Colors from '@/constants/colors';

export const DatabaseTest: React.FC = () => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { isInitialized, service } = useDatabaseContext();
  
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);
  const [stats, setStats] = useState<{ tables: number; totalRecords: number } | null>(null);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, message]);
  };

  const runDatabaseTests = async () => {
    if (!isInitialized) {
      Alert.alert('Error', 'Database not initialized');
      return;
    }

    setIsRunning(true);
    setTestResults([]);

    try {
      addResult('🔄 Starting database tests...');

      // Test user profile operations
      addResult('🔄 Testing user profile operations...');
      const testProfile = {
        name: 'Test User',
        sobrietyDate: '2024-01-01',
        addiction: 'alcohol',
        hasCompletedOnboarding: true,
        testTimestamp: new Date().toISOString()
      };

      await service.saveUserProfile(testProfile);
      addResult('✅ User profile saved');

      const retrievedProfile = await service.getUserProfile();
      addResult(`✅ User profile retrieved: ${retrievedProfile?.name || 'No name'}`);

      // Test health data operations
      addResult('🔄 Testing health data operations...');
      const testHealthData = {
        sleep: 8.5,
        pills: 1,
        hydration: 2.5,
        exercise: 1
      };

      const testDate = new Date().toISOString().split('T')[0]; // Today's date
      await service.saveHealthData(testDate, testHealthData);
      addResult('✅ Health data saved');

      const retrievedHealthData = await service.getHealthData(testDate);
      addResult(`✅ Health data retrieved: Sleep ${retrievedHealthData?.sleep || 0}h`);

      // Test mood entry operations
      addResult('🔄 Testing mood entry operations...');
      const testMoodEntry = {
        id: `mood-test-${Date.now()}`,
        date: testDate,
        mood: 4,
        cravingIntensity: 2,
        notes: 'Test mood entry'
      };

      await service.addMoodEntry(testMoodEntry);
      addResult('✅ Mood entry added');

      const moodEntries = await service.getMoodEntries(5);
      addResult(`✅ Mood entries retrieved: ${moodEntries.length} entries`);

      // Test emergency contact operations
      addResult('🔄 Testing emergency contact operations...');
      const testContact = {
        id: `contact-test-${Date.now()}`,
        name: 'Test Emergency Contact',
        phone: '+**********',
        email: '<EMAIL>',
        relationship: 'friend',
        notes: 'Test contact'
      };

      await service.addEmergencyContact(testContact);
      addResult('✅ Emergency contact added');

      const contacts = await service.getEmergencyContacts();
      addResult(`✅ Emergency contacts retrieved: ${contacts.length} contacts`);

      // Test document operations
      addResult('🔄 Testing document operations...');
      const testDocument = {
        id: `doc-test-${Date.now()}`,
        title: 'Test Document',
        content: 'This is a test document content',
        category: 'test',
        date: testDate
      };

      await service.addDocument(testDocument);
      addResult('✅ Document added');

      const documents = await service.getDocuments();
      addResult(`✅ Documents retrieved: ${documents.length} documents`);

      // Test media file operations
      addResult('🔄 Testing media file operations...');
      const testMediaFile = {
        id: `media-test-${Date.now()}`,
        title: 'Test Media File',
        description: 'Test media description',
        type: 'image',
        category: 'test',
        fileName: 'test.jpg',
        fileSize: 1024,
        date: testDate
      };

      await service.addMediaFile(testMediaFile);
      addResult('✅ Media file added');

      const mediaFiles = await service.getMediaFiles();
      addResult(`✅ Media files retrieved: ${mediaFiles.length} files`);

      // Get database stats
      addResult('🔄 Getting database stats...');
      const dbStats = await service.getDatabaseStats();
      setStats(dbStats);
      addResult(`✅ Database stats: ${dbStats.tables} tables, ${dbStats.totalRecords} records`);

      addResult('🎉 All database tests completed successfully!');

    } catch (error) {
      addResult(`❌ Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('Database test error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const clearTestData = async () => {
    if (!isInitialized) return;

    try {
      setIsRunning(true);
      addResult('🔄 Clearing test data...');
      
      // Note: This clears ALL data, use with caution
      await service.clearAllData();
      setStats(null);
      addResult('✅ Test data cleared');
      
      const newStats = await service.getDatabaseStats();
      setStats(newStats);
      addResult(`✅ New stats: ${newStats.tables} tables, ${newStats.totalRecords} records`);
    } catch (error) {
      addResult(`❌ Clear failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <View style={{ flex: 1, padding: 20, backgroundColor: colors.background }}>
      <Text style={{ 
        fontSize: 24, 
        fontWeight: 'bold', 
        color: colors.text, 
        marginBottom: 20,
        textAlign: 'center'
      }}>
        Database Test Suite
      </Text>

      {/* Database Status */}
      <View style={{
        backgroundColor: colors.card,
        padding: 15,
        borderRadius: 10,
        marginBottom: 20
      }}>
        <Text style={{ fontSize: 16, fontWeight: '600', color: colors.text, marginBottom: 10 }}>
          Database Status
        </Text>
        <Text style={{ color: isInitialized ? colors.success : colors.danger }}>
          {isInitialized ? '✅ Database Initialized' : '❌ Database Not Ready'}
        </Text>
        {stats && (
          <View style={{ marginTop: 10 }}>
            <Text style={{ color: colors.muted }}>Tables: {stats.tables}</Text>
            <Text style={{ color: colors.muted }}>Total Records: {stats.totalRecords}</Text>
          </View>
        )}
      </View>

      {/* Action Buttons */}
      <View style={{ flexDirection: 'row', gap: 10, marginBottom: 20 }}>
        <TouchableOpacity
          onPress={runDatabaseTests}
          disabled={!isInitialized || isRunning}
          style={{
            flex: 1,
            backgroundColor: isInitialized && !isRunning ? colors.primary : colors.muted,
            padding: 15,
            borderRadius: 10,
            alignItems: 'center'
          }}
        >
          {isRunning ? (
            <ActivityIndicator color={colors.background} />
          ) : (
            <Text style={{ color: colors.background, fontWeight: '600' }}>
              Run Tests
            </Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          onPress={clearTestData}
          disabled={!isInitialized || isRunning}
          style={{
            flex: 1,
            backgroundColor: isInitialized && !isRunning ? colors.danger : colors.muted,
            padding: 15,
            borderRadius: 10,
            alignItems: 'center'
          }}
        >
          <Text style={{ color: colors.background, fontWeight: '600' }}>
            Clear Data
          </Text>
        </TouchableOpacity>
      </View>

      {/* Test Results */}
      <View style={{
        backgroundColor: colors.card,
        borderRadius: 10,
        flex: 1
      }}>
        <Text style={{ 
          fontSize: 16, 
          fontWeight: '600', 
          color: colors.text, 
          padding: 15,
          borderBottomWidth: 1,
          borderBottomColor: colors.border
        }}>
          Test Results
        </Text>
        <ScrollView style={{ flex: 1, padding: 15 }}>
          {testResults.length === 0 ? (
            <Text style={{ color: colors.muted, fontStyle: 'italic' }}>
                             No tests run yet. Tap &quot;Run Tests&quot; to start.
            </Text>
          ) : (
            testResults.map((result, index) => (
              <Text 
                key={index} 
                style={{ 
                  color: colors.text, 
                  marginBottom: 5,
                  fontFamily: 'monospace',
                  fontSize: 12
                }}
              >
                {result}
              </Text>
            ))
          )}
        </ScrollView>
      </View>
    </View>
  );
}; 