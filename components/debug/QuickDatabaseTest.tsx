import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { useDatabaseContext } from '@/context/database-context';
import { useTheme } from '@/context/theme-context';
import Colors from '@/constants/colors';

export const QuickDatabaseTest: React.FC = () => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { isInitialized, service } = useDatabaseContext();
  const [isRunning, setIsRunning] = useState(false);
  const [lastResult, setLastResult] = useState<string>('');

  const runQuickTest = async () => {
    if (!isInitialized) {
      Alert.alert('Database Error', 'Database not initialized');
      return;
    }

    setIsRunning(true);
    try {
      // Quick test: save and retrieve a simple profile
      const testData = {
        name: 'Quick Test User',
        testTimestamp: new Date().toISOString(),
        quickTest: true
      };

      await service.saveUserProfile(testData);
      const retrieved = await service.getUserProfile();
      
      if (retrieved && retrieved.name === 'Quick Test User') {
        setLastResult('✅ Database working correctly!');
        Alert.alert('Success', 'Database is working correctly!');
      } else {
        setLastResult('❌ Database test failed');
        Alert.alert('Error', 'Database test failed');
      }
    } catch (error) {
      setLastResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      Alert.alert('Error', `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <View style={{
      backgroundColor: colors.card,
      padding: 15,
      borderRadius: 10,
      margin: 10,
      borderWidth: 1,
      borderColor: colors.border
    }}>
      <Text style={{
        fontSize: 16,
        fontWeight: '600',
        color: colors.text,
        marginBottom: 10
      }}>
        Quick Database Test
      </Text>
      
      <Text style={{
        color: isInitialized ? colors.success : colors.danger,
        marginBottom: 10,
        fontSize: 14
      }}>
        Status: {isInitialized ? 'Database Ready' : 'Database Not Ready'}
      </Text>

      {lastResult ? (
        <Text style={{
          color: lastResult.includes('✅') ? colors.success : colors.danger,
          marginBottom: 10,
          fontSize: 12
        }}>
          {lastResult}
        </Text>
      ) : null}

      <TouchableOpacity
        onPress={runQuickTest}
        disabled={!isInitialized || isRunning}
        style={{
          backgroundColor: isInitialized && !isRunning ? colors.primary : colors.muted,
          padding: 12,
          borderRadius: 8,
          alignItems: 'center'
        }}
      >
        <Text style={{
          color: colors.background,
          fontWeight: '600'
        }}>
          {isRunning ? 'Testing...' : 'Run Quick Test'}
        </Text>
      </TouchableOpacity>
    </View>
  );
}; 