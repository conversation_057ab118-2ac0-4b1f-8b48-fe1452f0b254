import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Modal,
  TextInput,
} from "react-native";
import { useTheme } from "@/context/theme-context";
import { useUserStore } from "@/store/user/user-store";
import Colors from "@/constants/colors";
import {
  ArrowLeft,
  Plus,
  FileText,
  Edit,
  Trash2,
  Save,
  X,
  Shield,
} from "lucide-react-native";
import * as DocumentPicker from "expo-document-picker";
import { Document } from "@/types/media";
import { useTranslation } from "@/hooks/useTranslation";

interface EmergencyPlanManagerProps {
  onClose: () => void;
}

export const EmergencyPlanManager: React.FC<EmergencyPlanManagerProps> = ({
  onClose,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { profile, addDocument, editDocument, removeDocument } = useUserStore();
  const { t } = useTranslation();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingDocument, setEditingDocument] = useState<Document | null>(null);
  const [newDocumentTitle, setNewDocumentTitle] = useState("");
  const [newDocumentContent, setNewDocumentContent] = useState("");



  const emergencyPlans = profile?.documents?.filter(
    (doc) => doc.category === "EmergencyPlan"
  ) || [];

  const styles = StyleSheet.create({
    actionButton: {
      alignItems: "center",
      borderRadius: 8,
      height: 32,
      justifyContent: "center",
      marginLeft: 8,
      width: 32,
    },
    actionCard: {
      alignItems: "center",
      borderRadius: 12,
      flex: 1,
      flexDirection: "row",
      gap: 12,
      justifyContent: "center",
      marginHorizontal: 4,
      paddingVertical: 16,
    },
    actionText: {
      fontSize: 14,
      fontWeight: "600",
    },
    actionsCard: {
      flexDirection: "row",
      marginBottom: 24,
    },
    addButton: {
      alignItems: "center",
      borderRadius: 20,
      height: 40,
      justifyContent: "center",
      width: 40,
    },
    backButton: {
      alignItems: "center",
      borderRadius: 20,
      height: 40,
      justifyContent: "center",
      width: 40,
    },
    cancelButton: {
      alignItems: "center",
      borderRadius: 12,
      borderWidth: 1,
      flex: 1,
      justifyContent: "center",
      marginRight: 8,
      padding: 16,
    },
    cancelButtonText: {
      fontSize: 16,
      fontWeight: "500",
    },
    container: {
      flex: 1,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
    },
    documentActions: {
      flexDirection: "row",
    },
    documentCard: {
      backgroundColor: colors.card,
      borderColor: colors.border,
      borderRadius: 12,
      borderWidth: 1,
      marginBottom: 12,
      padding: 16,
    },
    documentDate: {
      fontSize: 12,
      marginTop: 2,
    },
    documentHeader: {
      alignItems: "center",
      flexDirection: "row",
      marginBottom: 8,
    },
    documentIcon: {
      alignItems: "center",
      backgroundColor: colors.danger + "20",
      borderRadius: 8,
      height: 32,
      justifyContent: "center",
      marginRight: 12,
      width: 32,
    },
    documentInfo: {
      flex: 1,
    },
    documentPreview: {
      fontSize: 13,
      lineHeight: 18,
    },
    documentTitle: {
      fontSize: 16,
      fontWeight: "600",
    },
    documentsSection: {
      marginBottom: 20,
    },
    emptyState: {
      alignItems: "center",
      paddingVertical: 40,
    },
    emptyText: {
      fontSize: 14,
      marginTop: 8,
      textAlign: "center",
    },
    emptyTitle: {
      fontSize: 18,
      fontWeight: "600",
      marginTop: 16,
    },
    header: {
      alignItems: "center",
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
      flexDirection: "row",
      paddingBottom: 16,
      paddingHorizontal: 20,
      paddingTop: 10,
    },
    headerActions: {
      flexDirection: "row",
    },
    headerTitle: {
      flex: 1,
      fontSize: 20,
      fontWeight: "700",
      marginLeft: 16,
    },
    infoCard: {
      backgroundColor: colors.card,
      borderColor: colors.border,
      borderRadius: 12,
      borderWidth: 1,
      marginBottom: 20,
      marginTop: 20,
      padding: 16,
    },
    infoText: {
      fontSize: 14,
      lineHeight: 20,
      marginTop: 8,
    },
    infoTitle: {
      fontSize: 16,
      fontWeight: "600",
    },
    inputGroup: {
      marginBottom: 20,
    },
    inputLabel: {
      fontSize: 16,
      fontWeight: "500",
      marginBottom: 8,
    },
    modalBody: {
      maxHeight: 400,
      padding: 20,
    },
    modalContent: {
      borderRadius: 16,
      maxHeight: "80%",
      overflow: "hidden",
      width: "100%",
    },
    modalFooter: {
      borderTopWidth: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      padding: 20,
    },
    modalHeader: {
      alignItems: "center",
      borderBottomWidth: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      padding: 20,
    },
    modalOverlay: {
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      flex: 1,
      justifyContent: "center",
      padding: 20,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: "600",
    },
    saveButton: {
      alignItems: "center",
      borderRadius: 12,
      flex: 1,
      justifyContent: "center",
      marginLeft: 8,
      padding: 16,
    },
    saveButtonText: {
      fontSize: 16,
      fontWeight: "600",
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: "600",
      marginBottom: 16,
    },
    textArea: {
      borderRadius: 8,
      borderWidth: 1,
      fontSize: 16,
      minHeight: 120,
      padding: 12,
      textAlignVertical: "top",
    },
    textInput: {
      borderRadius: 8,
      borderWidth: 1,
      fontSize: 16,
      padding: 12,
    },
  });

  const handleCreateDocument = () => {
    if (!newDocumentTitle.trim()) {
      Alert.alert(
        t('common.error'),
        t('crisis.emergencyPlans.enterTitle')
      );
      return;
    }

    const newDocument: Omit<Document, "id"> = {
      title: newDocumentTitle.trim(),
      content: newDocumentContent.trim() || getDefaultEmergencyPlan(),
      date: new Date().toISOString(),
      category: "EmergencyPlan",
    };

    addDocument(newDocument);
    setNewDocumentTitle("");
    setNewDocumentContent("");
    setShowCreateModal(false);

    Alert.alert(
      t('common.success'),
      t('crisis.emergencyPlans.planSaved')
    );
  };

  const handleImportDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ["text/plain", "application/pdf", "text/*"],
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const file = result.assets[0];
        const newDocument: Omit<Document, "id"> = {
          title: file.name.replace(/\.[^/.]+$/, ""), // Remove file extension
          content: t('crisis.emergencyPlans.importedDocument'),
          date: new Date().toISOString(),
          category: "EmergencyPlan",
          fileUri: file.uri,
          fileName: file.name,
          fileType: file.mimeType || "text/plain",
          fileSize: file.size,
        };

        addDocument(newDocument);
      }
    } catch {
      Alert.alert(
        t('common.error'),
        t('crisis.emergencyPlans.importError')
      );
    }
  };

  const handleEditDocument = (document: Document) => {
    setEditingDocument(document);
    setNewDocumentTitle(document.title);
    setNewDocumentContent(document.content);
    setShowCreateModal(true);
  };

  const handleSaveEdit = () => {
    if (!editingDocument || !newDocumentTitle.trim()) return;

    editDocument(editingDocument.id, {
      title: newDocumentTitle.trim(),
      content: newDocumentContent.trim(),
    });

    setEditingDocument(null);
    setNewDocumentTitle("");
    setNewDocumentContent("");
    setShowCreateModal(false);
  };

  const handleDeleteDocument = (document: Document) => {
    Alert.alert(
      t('common.delete'),
      t('crisis.emergencyPlans.confirmDelete'),
      [
        { text: t('common.cancel'), style: "cancel" },
        {
          text: t('common.delete'),
          style: "destructive",
          onPress: () => {
            removeDocument(document.id);
            Alert.alert(
              t('common.success'),
              t('crisis.emergencyPlans.planDeleted')
            );
          },
        },
      ]
    );
  };

  const getDefaultEmergencyPlan = () => {
    return t('crisis.emergencyPlans.defaultPlan');
  };

  const renderDocumentCard = (document: Document) => (
    <View key={document.id} style={styles.documentCard}>
      <View style={styles.documentHeader}>
        <View style={styles.documentIcon}>
          <Shield size={20} color={colors.danger} />
        </View>
        <View style={styles.documentInfo}>
          <Text style={[styles.documentTitle, { color: colors.text }]}>
            {document.title}
          </Text>
          <Text style={[styles.documentDate, { color: colors.muted }]}>
            {new Date(document.date).toLocaleDateString(
              profile?.language === "nl" ? "nl-NL" : "en-US",
              { year: "numeric", month: "short", day: "numeric" }
            )}
          </Text>
        </View>
        <View style={styles.documentActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEditDocument(document)}
          >
            <Edit size={16} color={colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteDocument(document)}
          >
            <Trash2 size={16} color={colors.danger} />
          </TouchableOpacity>
        </View>
      </View>
      {document.content && (
        <Text style={[styles.documentPreview, { color: colors.muted }]} numberOfLines={3}>
          {document.content}
        </Text>
      )}
    </View>
  );

  const renderCreateModal = () => (
    <Modal
      visible={showCreateModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowCreateModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {editingDocument
                ? t('crisis.emergencyPlans.editPlan')
                : t('crisis.emergencyPlans.newPlan')}
            </Text>
            <TouchableOpacity onPress={() => setShowCreateModal(false)}>
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                {t('crisis.emergencyPlans.planTitle')}
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  {
                    borderColor: colors.border,
                    backgroundColor: colors.background,
                    color: colors.text,
                  },
                ]}
                value={newDocumentTitle}
                onChangeText={setNewDocumentTitle}
                placeholder={t('crisis.emergencyPlans.titlePlaceholder')}
                placeholderTextColor={colors.muted}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                {t('crisis.emergencyPlans.content')}
              </Text>
              <TextInput
                style={[
                  styles.textArea,
                  {
                    borderColor: colors.border,
                    backgroundColor: colors.background,
                    color: colors.text,
                  },
                ]}
                value={newDocumentContent}
                onChangeText={setNewDocumentContent}
                placeholder={t('crisis.emergencyPlans.contentPlaceholder')}
                placeholderTextColor={colors.muted}
                multiline
                numberOfLines={10}
                textAlignVertical="top"
              />
            </View>
          </ScrollView>

          <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.cancelButton, { borderColor: colors.border }]}
              onPress={() => setShowCreateModal(false)}
            >
              <Text style={[styles.cancelButtonText, { color: colors.muted }]}>
                {t('common.cancel')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.saveButton, { backgroundColor: colors.primary }]}
              onPress={editingDocument ? handleSaveEdit : handleCreateDocument}
            >
              <Save size={16} color={colors.background} />
              <Text style={[styles.saveButtonText, { color: colors.background }]}>
                {t('common.save')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onClose}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          {t('crisis.emergencyPlans.emergencyPlansTitle')}
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowCreateModal(true)}
          >
            <Plus size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.infoCard}>
          <Text style={[styles.infoTitle, { color: colors.text }]}>
            {t('crisis.emergencyPlans.about')}
          </Text>
          <Text style={[styles.infoText, { color: colors.muted }]}>
            {t('crisis.emergencyPlans.aboutDescription')}
          </Text>
        </View>

        <View style={styles.actionsCard}>
          <TouchableOpacity
            style={[styles.actionCard, { backgroundColor: colors.primary + "10" }]}
            onPress={() => setShowCreateModal(true)}
          >
            <Plus size={24} color={colors.primary} />
            <Text style={[styles.actionText, { color: colors.primary }]}>
              {t('crisis.emergencyPlans.createNew')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionCard, { backgroundColor: colors.muted + "10" }]}
            onPress={handleImportDocument}
          >
            <FileText size={24} color={colors.muted} />
            <Text style={[styles.actionText, { color: colors.muted }]}>
              {t('crisis.emergencyPlans.importDocument')}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.documentsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {t('crisis.emergencyPlans.myPlans')} ({emergencyPlans.length})
          </Text>

          {emergencyPlans.length === 0 ? (
            <View style={styles.emptyState}>
              <Shield size={48} color={colors.muted} />
              <Text style={[styles.emptyTitle, { color: colors.text }]}>
                {t('crisis.emergencyPlans.noPlans')}
              </Text>
              <Text style={[styles.emptyText, { color: colors.muted }]}>
                {t('crisis.emergencyPlans.noPlanDescription')}
              </Text>
            </View>
          ) : (
            emergencyPlans.map(renderDocumentCard)
          )}
        </View>
      </ScrollView>

      {renderCreateModal()}
    </SafeAreaView>
  );
};

 