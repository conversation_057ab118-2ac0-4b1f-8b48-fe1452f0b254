import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from "react-native";
import { useTheme } from "@/context/theme-context";
import { useUserStore } from "@/store/user/user-store";
import Colors from "@/constants/colors";
import {
  Shield,
  RotateCcw,
  IdCard,
  AlertTriangle,
  ChevronRight,
} from "lucide-react-native";
import { EmergencyPlanManager } from "@/components/crisis/EmergencyPlanManager";
import { RelapsePlanManager } from "@/components/crisis/RelapsePlanManager";
import { EmergencyCardManager } from "@/components/crisis/EmergencyCardManager";
import { useDebug } from "@/hooks/useDebug";

interface CrisisSupportProps {
  onClose?: () => void;
}

export const CrisisSupport: React.FC<CrisisSupportProps> = ({ onClose: _onClose }) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { profile } = useUserStore();
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const debug = useDebug();

  const language = profile?.language || "en";

  // Debug logging to track data persistence
  useEffect(() => {
    debug.log("CrisisSupport: Component mounted/updated");
    debug.log("CrisisSupport: Profile exists:", !!profile);
    debug.log("CrisisSupport: Documents count:", profile?.documents?.length || 0);
    debug.log("CrisisSupport: Media files count:", profile?.mediaFiles?.length || 0);
    debug.log("CrisisSupport: Emergency plans:", profile?.documents?.filter(doc => doc.category === "EmergencyPlan").length || 0);
    debug.log("CrisisSupport: Relapse plans:", profile?.documents?.filter(doc => doc.category === "RelapsePlan").length || 0);
    debug.log("CrisisSupport: Emergency cards:", profile?.mediaFiles?.filter(file => file.category === "EmergencyCard").length || 0);
  }, [profile?.documents, profile?.mediaFiles, debug]);

  const getEmergencyPlanCount = () => {
    return profile?.documents?.filter(doc => doc.category === "EmergencyPlan").length || 0;
  };

  const getRelapsePlanCount = () => {
    return profile?.documents?.filter(doc => doc.category === "RelapsePlan").length || 0;
  };

  const getEmergencyCardCount = () => {
    return profile?.mediaFiles?.filter(file => file.category === "EmergencyCard").length || 0;
  };

  const crisisSections = [
    {
      id: "emergency-plan",
      title: language === "nl" ? "Noodplan" : "Emergency Plan",
      description: language === "nl" 
        ? "Documenten met stappen voor noodsituaties"
        : "Documents with steps for emergency situations",
      icon: Shield,
      count: getEmergencyPlanCount(),
      color: colors.danger,
    },
    {
      id: "relapse-plan",
      title: language === "nl" ? "Terugvalplan" : "Relapse Plan", 
      description: language === "nl"
        ? "Strategieën om terugval te voorkomen"
        : "Strategies to prevent relapse",
      icon: RotateCcw,
      count: getRelapsePlanCount(),
      color: colors.warning,
    },
    {
      id: "emergency-card",
      title: language === "nl" ? "Noodkaart" : "Emergency Card",
      description: language === "nl"
        ? "Afbeelding van je noodcontactkaart"
        : "Image of your emergency contact card",
      icon: IdCard,
      count: getEmergencyCardCount(),
      color: colors.primary,
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
    },
    countBadge: {
      alignItems: "center",
      borderRadius: 16,
      height: 32,
      justifyContent: "center",
      minWidth: 32,
      paddingHorizontal: 12,
    },
    countText: {
      fontSize: 14,
      fontWeight: "700",
      letterSpacing: -0.3,
    },
    emergencyInfo: {
      backgroundColor: colors.danger + "10",
      borderColor: colors.danger + "30",
      borderRadius: 12,
      borderWidth: 1,
      marginBottom: 20,
      marginTop: 20,
      padding: 16,
    },
    emergencyNumber: {
      fontSize: 16,
      fontWeight: "500",
      marginVertical: 2,
    },
    emergencyNumbers: {
      marginTop: 12,
    },
    emergencyText: {
      fontSize: 14,
      lineHeight: 20,
      marginTop: 8,
    },
    emergencyTitle: {
      fontSize: 16,
      fontWeight: "600",
    },
    header: {
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
      paddingBottom: 20,
      paddingHorizontal: 20,
      paddingTop: 10,
    },
    headerContent: {
      alignItems: "center",
      flexDirection: "row",
    },
    headerIcon: {
      alignItems: "center",
      borderRadius: 16,
      height: 56,
      justifyContent: "center",
      marginRight: 16,
      width: 56,
    },
    headerSubtitle: {
      fontSize: 14,
      marginTop: 4,
    },
    headerText: {
      flex: 1,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: "700",
    },
    iconContainer: {
      alignItems: "center",
      borderRadius: 16,
      height: 56,
      justifyContent: "center",
      marginRight: 16,
      width: 56,
    },
    infoCard: {
      backgroundColor: colors.card,
      borderColor: colors.border,
      borderRadius: 12,
      borderWidth: 1,
      marginBottom: 20,
      marginTop: 20,
      padding: 16,
    },
    infoText: {
      fontSize: 14,
      lineHeight: 20,
      marginTop: 8,
    },
    infoTitle: {
      fontSize: 16,
      fontWeight: "600",
    },
    sectionCard: {
      backgroundColor: colors.card,
      borderColor: colors.border,
      borderRadius: 16,
      borderWidth: 1,
      elevation: 3,
      marginBottom: 16,
      padding: 20,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 8,
    },
    sectionDescription: {
      fontSize: 14,
      lineHeight: 20,
      marginTop: 4,
    },
    sectionHeader: {
      alignItems: "center",
      flexDirection: "row",
    },
    sectionInfo: {
      flex: 1,
      marginLeft: 4,
    },
    sectionMeta: {
      alignItems: "center",
      flexDirection: "row",
      gap: 8,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: "700",
      letterSpacing: -0.5,
    },
    sectionsContainer: {
      marginBottom: 20,
    },
  });

  const renderSectionCard = (section: typeof crisisSections[0]) => (
    <TouchableOpacity
      key={section.id}
      style={styles.sectionCard}
      onPress={() => setActiveSection(section.id)}
      activeOpacity={0.7}
    >
      <View style={styles.sectionHeader}>
        <View style={[styles.iconContainer, { backgroundColor: section.color + "15" }]}>
          <section.icon size={28} color={section.color} strokeWidth={2.5} />
        </View>
        <View style={styles.sectionInfo}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {section.title}
          </Text>
          <Text style={[styles.sectionDescription, { color: colors.muted }]}>
            {section.description}
          </Text>
        </View>
        <View style={styles.sectionMeta}>
          <View style={[styles.countBadge, { backgroundColor: section.color + "15" }]}>
            <Text style={[styles.countText, { color: section.color }]}>
              {section.count}
            </Text>
          </View>
          <ChevronRight size={24} color={colors.muted} strokeWidth={2} />
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderActiveSection = () => {
    switch (activeSection) {
      case "emergency-plan":
        return (
          <EmergencyPlanManager
            onClose={() => setActiveSection(null)}
          />
        );
      case "relapse-plan":
        return (
          <RelapsePlanManager
            onClose={() => setActiveSection(null)}
          />
        );
      case "emergency-card":
        return (
          <EmergencyCardManager
            onClose={() => setActiveSection(null)}
          />
        );
      default:
        return null;
    }
  };

  if (activeSection) {
    return renderActiveSection();
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={[styles.headerIcon, { backgroundColor: colors.danger + "20" }]}>
            <AlertTriangle size={28} color={colors.danger} />
          </View>
          <View style={styles.headerText}>
            <Text style={[styles.headerTitle, { color: colors.text }]}>
              {language === "nl" ? "Crisis Ondersteuning" : "Crisis Support"}
            </Text>
            <Text style={[styles.headerSubtitle, { color: colors.muted }]}>
              {language === "nl" 
                ? "Beheer je noodplannen en contacten"
                : "Manage your emergency plans and contacts"}
            </Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.infoCard}>
          <Text style={[styles.infoTitle, { color: colors.text }]}>
            {language === "nl" ? "Waarom Crisis Ondersteuning?" : "Why Crisis Support?"}
          </Text>
          <Text style={[styles.infoText, { color: colors.muted }]}>
            {language === "nl"
              ? "Het hebben van een goed voorbereid noodplan kan het verschil maken in moeilijke momenten. Deze tools helpen je om voorbereid te zijn."
              : "Having a well-prepared emergency plan can make all the difference in difficult moments. These tools help you stay prepared."}
          </Text>
        </View>

        <View style={styles.sectionsContainer}>
          {crisisSections.map(renderSectionCard)}
        </View>

        <View style={styles.emergencyInfo}>
          <Text style={[styles.emergencyTitle, { color: colors.danger }]}>
            {language === "nl" ? "In Noodgeval" : "In Emergency"}
          </Text>
          <Text style={[styles.emergencyText, { color: colors.text }]}>
            {language === "nl"
              ? "Als je in een crisis bent, bel onmiddellijk je noodcontacten of de hulpdiensten."
              : "If you're in crisis, immediately call your emergency contacts or emergency services."}
          </Text>
          <View style={styles.emergencyNumbers}>
            <Text style={[styles.emergencyNumber, { color: colors.text }]}>
              {language === "nl" ? "🇳🇱 Hulplijn: 0800-0113" : "🇺🇸 Crisis Line: 988"}
            </Text>
            <Text style={[styles.emergencyNumber, { color: colors.text }]}>
              {language === "nl" ? "🚨 Spoed: 112" : "🚨 Emergency: 911"}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}; 