/* eslint-disable react-native/no-unused-styles */
import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Modal,
  TextInput,
} from "react-native";
import { useTheme } from "@/context/theme-context";
import { useUserStore } from "@/store/user/user-store";
import Colors from "@/constants/colors";
import {
  ArrowLeft,
  Plus,
  FileText,
  Edit,
  Trash2,
  Save,
  X,
  RotateCcw,
} from "lucide-react-native";
import * as DocumentPicker from "expo-document-picker";
import { Document } from "@/types/media";

interface RelapsePlanManagerProps {
  onClose: () => void;
}

export const RelapsePlanManager: React.FC<RelapsePlanManagerProps> = ({
  onClose,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { profile, addDocument, editDocument, removeDocument } = useUserStore();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingDocument, setEditingDocument] = useState<Document | null>(null);
  const [newDocumentTitle, setNewDocumentTitle] = useState("");
  const [newDocumentContent, setNewDocumentContent] = useState("");

  const language = profile?.language || "en";
  const styles = createStyles(colors);

  const relapsePlans = profile?.documents?.filter(
    (doc) => doc.category === "RelapsePlan"
  ) || [];

  const handleCreateDocument = () => {
    if (!newDocumentTitle.trim()) {
      Alert.alert(
        language === "nl" ? "Fout" : "Error",
        language === "nl" ? "Voer een titel in" : "Please enter a title"
      );
      return;
    }

    const newDocument: Omit<Document, "id"> = {
      title: newDocumentTitle.trim(),
      content: newDocumentContent.trim() || getDefaultRelapsePlan(),
      date: new Date().toISOString(),
      category: "RelapsePlan",
    };

    addDocument(newDocument);
    setNewDocumentTitle("");
    setNewDocumentContent("");
    setShowCreateModal(false);
  };

  const handleImportDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ["text/plain", "application/pdf", "text/*"],
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const file = result.assets[0];
        const newDocument: Omit<Document, "id"> = {
          title: file.name.replace(/\.[^/.]+$/, ""), // Remove file extension
          content: language === "nl" 
            ? "Geïmporteerd document - bekijk het bestand voor details"
            : "Imported document - view file for details",
          date: new Date().toISOString(),
          category: "RelapsePlan",
          fileUri: file.uri,
          fileName: file.name,
          fileType: file.mimeType || "text/plain",
          fileSize: file.size,
        };

        addDocument(newDocument);
      }
    } catch {
      Alert.alert(
        language === "nl" ? "Fout" : "Error",
        language === "nl" 
          ? "Kon document niet importeren"
          : "Could not import document"
      );
    }
  };

  const handleEditDocument = (document: Document) => {
    setEditingDocument(document);
    setNewDocumentTitle(document.title);
    setNewDocumentContent(document.content);
    setShowCreateModal(true);
  };

  const handleSaveEdit = () => {
    if (!editingDocument || !newDocumentTitle.trim()) return;

    editDocument(editingDocument.id, {
      title: newDocumentTitle.trim(),
      content: newDocumentContent.trim(),
    });

    setEditingDocument(null);
    setNewDocumentTitle("");
    setNewDocumentContent("");
    setShowCreateModal(false);
  };

  const handleDeleteDocument = (document: Document) => {
    Alert.alert(
      language === "nl" ? "Verwijderen" : "Delete",
      language === "nl" 
        ? `Weet je zeker dat je "${document.title}" wilt verwijderen?`
        : `Are you sure you want to delete "${document.title}"?`,
      [
        { text: language === "nl" ? "Annuleren" : "Cancel", style: "cancel" },
        {
          text: language === "nl" ? "Verwijderen" : "Delete",
          style: "destructive",
          onPress: () => removeDocument(document.id),
        },
      ]
    );
  };

  const getDefaultRelapsePlan = () => {
    return language === "nl"
      ? `# TERUGVALPREVENTIEPLAN

## Mijn Triggers Herkennen:

### Emotionele Triggers:
- Stress
- Eenzaamheid
- Boosheid
- Verdriet
- [Voeg je eigen triggers toe]

### Situationele Triggers:
- Bepaalde locaties
- Sociale situaties
- Vrije tijd
- [Voeg je eigen triggers toe]

## Waarschuwingssignalen:

### Vroege Signalen:
- Veranderingen in slaappatroon
- Isolatie van anderen
- Negatieve gedachten
- [Voeg je eigen signalen toe]

### Late Signalen:
- Sterke verlangens
- Contact zoeken met oude gebruikscontacten
- Rationaliseren van gebruik
- [Voeg je eigen signalen toe]

## Copingstrategieën:

### Onmiddellijke Acties:
1. **Bel mijn sponsor**: [Telefoonnummer]
2. **Ga naar een veilige plek**
3. **Gebruik de HALT check**: Ben ik Hongerig, Boos, Eenzaam, of Moe?
4. **Doe een mindfulness oefening**

### Langetermijn Strategieën:
- Regelmatige bijeenkomsten bijwonen
- Dagelijkse meditatie/gebed
- Lichaamsbeweging
- Gezonde routine onderhouden

## Steunnetwerk:

- **Sponsor**: [Naam] - [Telefoonnummer]
- **Therapeut**: [Naam] - [Telefoonnummer]
- **Steungroep**: [Details]
- **Familie/Vrienden**: [Namen en nummers]

## Noodplan bij Terugval:

1. **Stop het gebruik onmiddellijk**
2. **Bel mijn sponsor of therapeut**
3. **Ga naar een veilige omgeving**
4. **Plan een spoedafspraak**
5. **Herstart mijn herstelplan**

*Onthoud: Een terugval betekent niet falen. Het is een kans om te leren en sterker terug te komen.*`
      : `# RELAPSE PREVENTION PLAN

## Recognizing My Triggers:

### Emotional Triggers:
- Stress
- Loneliness
- Anger
- Sadness
- [Add your own triggers]

### Situational Triggers:
- Certain locations
- Social situations
- Free time
- [Add your own triggers]

## Warning Signs:

### Early Signs:
- Changes in sleep patterns
- Isolating from others
- Negative thinking
- [Add your own signs]

### Late Signs:
- Strong cravings
- Contacting old using contacts
- Rationalizing use
- [Add your own signs]

## Coping Strategies:

### Immediate Actions:
1. **Call my sponsor**: [Phone Number]
2. **Go to a safe place**
3. **Use the HALT check**: Am I Hungry, Angry, Lonely, or Tired?
4. **Do a mindfulness exercise**

### Long-term Strategies:
- Attend regular meetings
- Daily meditation/prayer
- Exercise
- Maintain healthy routine

## Support Network:

- **Sponsor**: [Name] - [Phone Number]
- **Therapist**: [Name] - [Phone Number]
- **Support Group**: [Details]
- **Family/Friends**: [Names and numbers]

## Emergency Plan for Relapse:

1. **Stop using immediately**
2. **Call my sponsor or therapist**
3. **Go to a safe environment**
4. **Schedule an emergency appointment**
5. **Restart my recovery plan**

*Remember: A relapse doesn't mean failure. It's an opportunity to learn and come back stronger.*`;
  };

  const renderDocumentCard = (document: Document) => (
    <View key={document.id} style={styles.documentCard}>
      <View style={styles.documentHeader}>
        <View style={styles.documentIcon}>
          <RotateCcw size={20} color={colors.warning} />
        </View>
        <View style={styles.documentInfo}>
          <Text style={[styles.documentTitle, { color: colors.text }]}>
            {document.title}
          </Text>
          <Text style={[styles.documentDate, { color: colors.muted }]}>
            {new Date(document.date).toLocaleDateString(
              language === "nl" ? "nl-NL" : "en-US",
              { year: "numeric", month: "short", day: "numeric" }
            )}
          </Text>
        </View>
        <View style={styles.documentActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEditDocument(document)}
          >
            <Edit size={16} color={colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteDocument(document)}
          >
            <Trash2 size={16} color={colors.danger} />
          </TouchableOpacity>
        </View>
      </View>
      {document.content && (
        <Text style={[styles.documentPreview, { color: colors.muted }]} numberOfLines={3}>
          {document.content}
        </Text>
      )}
    </View>
  );

  const renderCreateModal = () => (
    <Modal
      visible={showCreateModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowCreateModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {editingDocument
                ? (language === "nl" ? "Bewerk Plan" : "Edit Plan")
                : (language === "nl" ? "Nieuw Terugvalplan" : "New Relapse Plan")}
            </Text>
            <TouchableOpacity onPress={() => setShowCreateModal(false)}>
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                {language === "nl" ? "Titel" : "Title"}
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  {
                    borderColor: colors.border,
                    backgroundColor: colors.background,
                    color: colors.text,
                  },
                ]}
                value={newDocumentTitle}
                onChangeText={setNewDocumentTitle}
                placeholder={language === "nl" ? "Voer titel in..." : "Enter title..."}
                placeholderTextColor={colors.muted}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                {language === "nl" ? "Inhoud" : "Content"}
              </Text>
              <TextInput
                style={[
                  styles.textArea,
                  {
                    borderColor: colors.border,
                    backgroundColor: colors.background,
                    color: colors.text,
                  },
                ]}
                value={newDocumentContent}
                onChangeText={setNewDocumentContent}
                placeholder={language === "nl" ? "Voer plan inhoud in..." : "Enter plan content..."}
                placeholderTextColor={colors.muted}
                multiline
                numberOfLines={10}
                textAlignVertical="top"
              />
            </View>
          </ScrollView>

          <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.cancelButton, { borderColor: colors.border }]}
              onPress={() => setShowCreateModal(false)}
            >
              <Text style={[styles.cancelButtonText, { color: colors.muted }]}>
                {language === "nl" ? "Annuleren" : "Cancel"}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.saveButton, { backgroundColor: colors.primary }]}
              onPress={editingDocument ? handleSaveEdit : handleCreateDocument}
            >
              <Save size={16} color={colors.background} />
              <Text style={[styles.saveButtonText, { color: colors.background }]}>
                {language === "nl" ? "Opslaan" : "Save"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onClose}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          {language === "nl" ? "Terugvalplannen" : "Relapse Plans"}
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowCreateModal(true)}
          >
            <Plus size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.infoCard}>
          <Text style={[styles.infoTitle, { color: colors.text }]}>
            {language === "nl" ? "Over Terugvalpreventie" : "About Relapse Prevention"}
          </Text>
          <Text style={[styles.infoText, { color: colors.muted }]}>
            {language === "nl"
              ? "Een terugvalpreventieplan helpt je om triggers te herkennen en strategieën te ontwikkelen om nuchter te blijven."
              : "A relapse prevention plan helps you recognize triggers and develop strategies to stay sober."}
          </Text>
        </View>

        <View style={styles.actionsCard}>
          <TouchableOpacity
            style={[styles.actionCard, { backgroundColor: colors.warning + "10" }]}
            onPress={() => setShowCreateModal(true)}
          >
            <Plus size={24} color={colors.warning} />
            <Text style={[styles.actionText, { color: colors.warning }]}>
              {language === "nl" ? "Nieuw Plan Maken" : "Create New Plan"}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionCard, { backgroundColor: colors.muted + "10" }]}
            onPress={handleImportDocument}
          >
            <FileText size={24} color={colors.muted} />
            <Text style={[styles.actionText, { color: colors.muted }]}>
              {language === "nl" ? "Document Importeren" : "Import Document"}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.documentsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {language === "nl" ? "Mijn Terugvalplannen" : "My Relapse Plans"} ({relapsePlans.length})
          </Text>

          {relapsePlans.length === 0 ? (
            <View style={styles.emptyState}>
              <RotateCcw size={48} color={colors.muted} />
              <Text style={[styles.emptyTitle, { color: colors.text }]}>
                {language === "nl" ? "Geen Terugvalplannen" : "No Relapse Plans"}
              </Text>
              <Text style={[styles.emptyText, { color: colors.muted }]}>
                {language === "nl"
                  ? "Maak je eerste terugvalpreventieplan om voorbereid te zijn."
                  : "Create your first relapse prevention plan to stay prepared."}
              </Text>
            </View>
          ) : (
            relapsePlans.map(renderDocumentCard)
          )}
        </View>
      </ScrollView>

      {renderCreateModal()}
    </SafeAreaView>
  );
};

const createStyles = (colors: typeof Colors.light) =>
  StyleSheet.create({
    actionButton: {
      alignItems: "center",
      borderRadius: 8,
      height: 32,
      justifyContent: "center",
      marginLeft: 8,
      width: 32,
    },
    actionCard: {
      alignItems: "center",
      borderRadius: 12,
      flex: 1,
      flexDirection: "row",
      gap: 12,
      justifyContent: "center",
      marginHorizontal: 4,
      paddingVertical: 16,
    },
    actionText: {
      fontSize: 14,
      fontWeight: "600",
    },
    actionsCard: {
      flexDirection: "row",
      marginBottom: 24,
    },
    addButton: {
      alignItems: "center",
      borderRadius: 20,
      height: 40,
      justifyContent: "center",
      width: 40,
    },
    backButton: {
      alignItems: "center",
      borderRadius: 20,
      height: 40,
      justifyContent: "center",
      width: 40,
    },
    cancelButton: {
      alignItems: "center",
      borderRadius: 12,
      borderWidth: 1,
      flex: 1,
      justifyContent: "center",
      marginRight: 8,
      padding: 16,
    },
    cancelButtonText: {
      fontSize: 16,
      fontWeight: "500",
    },
    container: {
      flex: 1,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
    },
    documentActions: {
      flexDirection: "row",
    },
    documentCard: {
      backgroundColor: colors.card,
      borderColor: colors.border,
      borderRadius: 12,
      borderWidth: 1,
      marginBottom: 12,
      padding: 16,
    },
    documentDate: {
      fontSize: 12,
      marginTop: 2,
    },
    documentHeader: {
      alignItems: "center",
      flexDirection: "row",
      marginBottom: 8,
    },
    documentIcon: {
      alignItems: "center",
      backgroundColor: colors.warning + "20",
      borderRadius: 8,
      height: 32,
      justifyContent: "center",
      marginRight: 12,
      width: 32,
    },
    documentInfo: {
      flex: 1,
    },
    documentPreview: {
      fontSize: 13,
      lineHeight: 18,
    },
    documentTitle: {
      fontSize: 16,
      fontWeight: "600",
    },
    documentsSection: {
      marginBottom: 20,
    },
    emptyState: {
      alignItems: "center",
      paddingVertical: 40,
    },
    emptyText: {
      fontSize: 14,
      marginTop: 8,
      textAlign: "center",
    },
    emptyTitle: {
      fontSize: 18,
      fontWeight: "600",
      marginTop: 16,
    },
    header: {
      alignItems: "center",
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
      flexDirection: "row",
      paddingBottom: 16,
      paddingHorizontal: 20,
      paddingTop: 10,
    },
    headerActions: {
      flexDirection: "row",
    },
    headerTitle: {
      flex: 1,
      fontSize: 20,
      fontWeight: "700",
      marginLeft: 16,
    },
    infoCard: {
      backgroundColor: colors.card,
      borderColor: colors.border,
      borderRadius: 12,
      borderWidth: 1,
      marginBottom: 20,
      marginTop: 20,
      padding: 16,
    },
    infoText: {
      fontSize: 14,
      lineHeight: 20,
      marginTop: 8,
    },
    infoTitle: {
      fontSize: 16,
      fontWeight: "600",
    },
    inputGroup: {
      marginBottom: 20,
    },
    inputLabel: {
      fontSize: 16,
      fontWeight: "500",
      marginBottom: 8,
    },
    modalBody: {
      maxHeight: 400,
      padding: 20,
    },
    modalContent: {
      borderRadius: 16,
      maxHeight: "80%",
      overflow: "hidden",
      width: "100%",
    },
    modalFooter: {
      borderTopWidth: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      padding: 20,
    },
    modalHeader: {
      alignItems: "center",
      borderBottomWidth: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      padding: 20,
    },
    modalOverlay: {
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      flex: 1,
      justifyContent: "center",
      padding: 20,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: "600",
    },
    saveButton: {
      alignItems: "center",
      borderRadius: 12,
      flex: 1,
      flexDirection: "row",
      justifyContent: "center",
      marginLeft: 8,
      padding: 16,
    },
    saveButtonText: {
      fontSize: 16,
      fontWeight: "500",
      marginLeft: 8,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: "600",
      marginBottom: 16,
    },
    textArea: {
      borderRadius: 12,
      borderWidth: 1,
      fontSize: 16,
      minHeight: 120,
      padding: 12,
    },
    textInput: {
      borderRadius: 12,
      borderWidth: 1,
      fontSize: 16,
      padding: 12,
    },
  }); 