/* eslint-disable react-native/no-unused-styles */
import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Image,
} from "react-native";
import { useTheme } from "@/context/theme-context";
import { useUserStore } from "@/store/user/user-store";
import Colors from "@/constants/colors";
import {
  ArrowLeft,
  Plus,
  Camera,
  Image as ImageIcon,
  Edit,
  Trash2,
  IdCard,
  Eye,
} from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";
import { MediaFile } from "@/types/media";

const createStyles = (colors: typeof Colors.light) =>
  StyleSheet.create({
    actionButton: {
      alignItems: "center",
      borderRadius: 8,
      height: 32,
      justifyContent: "center",
      marginLeft: 8,
      width: 32,
    },
    actionCard: {
      alignItems: "center",
      borderRadius: 12,
      flex: 1,
      flexDirection: "row",
      gap: 12,
      justifyContent: "center",
      marginHorizontal: 4,
      paddingVertical: 16,
    },
    actionText: {
      fontSize: 14,
      fontWeight: "600",
    },
    actionsCard: {
      flexDirection: "row",
      marginBottom: 24,
    },
    addButton: {
      alignItems: "center",
      borderRadius: 20,
      height: 40,
      justifyContent: "center",
      width: 40,
    },
    backButton: {
      alignItems: "center",
      borderRadius: 20,
      height: 40,
      justifyContent: "center",
      width: 40,
    },
    cardActions: {
      flexDirection: "row",
    },
    cardDate: {
      fontSize: 12,
      marginTop: 2,
    },
    cardImage: {
      borderRadius: 8,
      height: 80,
      width: 128, // 16:10 aspect ratio
    },
    cardImageContainer: {
      position: "relative",
    },
    cardInfo: {
      flex: 1,
      marginLeft: 12,
    },
    cardItem: {
      alignItems: "center",
      backgroundColor: colors.card,
      borderColor: colors.border,
      borderRadius: 12,
      borderWidth: 1,
      flexDirection: "row",
      marginBottom: 12,
      padding: 16,
    },
    cardOverlay: {
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      borderRadius: 8,
      bottom: 0,
      justifyContent: "center",
      left: 0,
      opacity: 0,
      position: "absolute",
      right: 0,
      top: 0,
    },
    cardTitle: {
      fontSize: 16,
      fontWeight: "600",
    },
    cardsSection: {
      marginBottom: 20,
    },
    container: {
      flex: 1,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
    },
    emptyState: {
      alignItems: "center",
      paddingVertical: 40,
    },
    emptyText: {
      fontSize: 14,
      marginTop: 8,
      textAlign: "center",
    },
    emptyTitle: {
      fontSize: 18,
      fontWeight: "600",
      marginTop: 16,
    },
    fullScreenClose: {
      left: 20,
      position: "absolute",
      top: 60,
      zIndex: 2,
    },
    fullScreenContainer: {
      alignItems: "center",
      flex: 1,
      justifyContent: "center",
      paddingHorizontal: 20,
    },
    fullScreenDate: {
      color: "rgba(255, 255, 255, 0.8)",
      fontSize: 14,
      marginTop: 4,
    },
    fullScreenImage: {
      height: "80%",
      width: "100%",
    },
    fullScreenInfo: {
      alignItems: "center",
      bottom: 60,
      left: 0,
      position: "absolute",
      right: 0,
    },
    fullScreenOverlay: {
      backgroundColor: "black",
      flex: 1,
    },
    fullScreenTitle: {
      color: "white",
      fontSize: 18,
      fontWeight: "600",
    },
    header: {
      alignItems: "center",
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
      flexDirection: "row",
      paddingBottom: 16,
      paddingHorizontal: 20,
      paddingTop: 10,
    },
    headerActions: {
      flexDirection: "row",
    },
    headerTitle: {
      flex: 1,
      fontSize: 20,
      fontWeight: "700",
      marginLeft: 16,
    },
    infoCard: {
      backgroundColor: colors.card,
      borderColor: colors.border,
      borderRadius: 12,
      borderWidth: 1,
      marginBottom: 20,
      marginTop: 20,
      padding: 16,
    },
    infoText: {
      fontSize: 14,
      lineHeight: 20,
      marginTop: 8,
    },
    infoTitle: {
      fontSize: 16,
      fontWeight: "600",
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: "600",
      marginBottom: 16,
    },
  });

interface EmergencyCardManagerProps {
  onClose: () => void;
}

export const EmergencyCardManager: React.FC<EmergencyCardManagerProps> = ({
  onClose,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { profile, addMediaFile, editMediaFile, removeMediaFile } = useUserStore();
  const [selectedCard, setSelectedCard] = useState<MediaFile | null>(null);

  const language = profile?.language || "en";
  const styles = createStyles(colors);

  const emergencyCards = profile?.mediaFiles?.filter(
    (file) => file.category === "EmergencyCard"
  ) || [];

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        language === "nl" ? "Toestemming vereist" : "Permission Required",
        language === "nl"
          ? "We hebben toegang tot je foto's nodig om afbeeldingen te selecteren."
          : "We need access to your photos to select images."
      );
      return false;
    }
    return true;
  };

  const handleTakePhoto = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        language === "nl" ? "Camera toestemming vereist" : "Camera Permission Required",
        language === "nl"
          ? "We hebben toegang tot je camera nodig om foto's te maken."
          : "We need access to your camera to take photos."
      );
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 10], // Credit card aspect ratio
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const newCard: Omit<MediaFile, "id"> = {
          title: language === "nl" ? "Nieuwe Noodkaart" : "New Emergency Card",
          description: language === "nl" 
            ? "Noodcontactkaart gemaakt met camera"
            : "Emergency contact card taken with camera",
          date: new Date().toISOString(),
          type: "image",
          uri: asset.uri,
          fileName: `emergency_card_${Date.now()}.jpg`,
          fileType: "image/jpeg",
          fileSize: asset.fileSize,
          width: asset.width,
          height: asset.height,
          category: "EmergencyCard",
        };

        await addMediaFile(newCard);
      }
    } catch {
      Alert.alert(
        language === "nl" ? "Fout" : "Error",
        language === "nl" 
          ? "Kon foto niet maken"
          : "Could not take photo"
      );
    }
  };

  const handleSelectImage = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 10], // Credit card aspect ratio
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const newCard: Omit<MediaFile, "id"> = {
          title: language === "nl" ? "Nieuwe Noodkaart" : "New Emergency Card",
          description: language === "nl" 
            ? "Noodcontactkaart uit fotobibliotheek"
            : "Emergency contact card from photo library",
          date: new Date().toISOString(),
          type: "image",
          uri: asset.uri,
          fileName: `emergency_card_${Date.now()}.jpg`,
          fileType: asset.type || "image/jpeg",
          fileSize: asset.fileSize,
          width: asset.width,
          height: asset.height,
          category: "EmergencyCard",
        };

        await addMediaFile(newCard);
      }
    } catch {
      Alert.alert(
        language === "nl" ? "Fout" : "Error",
        language === "nl" 
          ? "Kon afbeelding niet selecteren"
          : "Could not select image"
      );
    }
  };

  const handleEditCard = (card: MediaFile) => {
    Alert.alert(
      language === "nl" ? "Bewerk Kaart" : "Edit Card",
      language === "nl" 
        ? "Wat wil je doen met deze noodkaart?"
        : "What would you like to do with this emergency card?",
      [
        { text: language === "nl" ? "Annuleren" : "Cancel", style: "cancel" },
        {
          text: language === "nl" ? "Vervangen" : "Replace",
          onPress: () => replaceCard(card),
        },
        {
          text: language === "nl" ? "Bekijken" : "View",
          onPress: () => setSelectedCard(card),
        },
      ]
    );
  };

  const replaceCard = async (card: MediaFile) => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    Alert.alert(
      language === "nl" ? "Vervang Kaart" : "Replace Card",
      language === "nl" 
        ? "Hoe wil je de nieuwe afbeelding toevoegen?"
        : "How would you like to add the new image?",
      [
        { text: language === "nl" ? "Annuleren" : "Cancel", style: "cancel" },
        {
          text: language === "nl" ? "Camera" : "Camera",
          onPress: () => replaceWithCamera(card),
        },
        {
          text: language === "nl" ? "Fotobibliotheek" : "Photo Library",
          onPress: () => replaceWithLibrary(card),
        },
      ]
    );
  };

  const replaceWithCamera = async (card: MediaFile) => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== "granted") return;

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 10],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        editMediaFile(card.id, {
          uri: asset.uri,
          fileName: `emergency_card_${Date.now()}.jpg`,
          fileType: "image/jpeg",
          fileSize: asset.fileSize,
          width: asset.width,
          height: asset.height,
          date: new Date().toISOString(),
        });
      }
    } catch {
      Alert.alert(
        language === "nl" ? "Fout" : "Error",
        language === "nl" ? "Kon foto niet maken" : "Could not take photo"
      );
    }
  };

  const replaceWithLibrary = async (card: MediaFile) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 10],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        editMediaFile(card.id, {
          uri: asset.uri,
          fileName: `emergency_card_${Date.now()}.jpg`,
          fileType: asset.type || "image/jpeg",
          fileSize: asset.fileSize,
          width: asset.width,
          height: asset.height,
          date: new Date().toISOString(),
        });
      }
    } catch {
      Alert.alert(
        language === "nl" ? "Fout" : "Error",
        language === "nl" ? "Kon afbeelding niet selecteren" : "Could not select image"
      );
    }
  };

  const handleDeleteCard = (card: MediaFile) => {
    Alert.alert(
      language === "nl" ? "Verwijderen" : "Delete",
      language === "nl" 
        ? `Weet je zeker dat je "${card.title}" wilt verwijderen?`
        : `Are you sure you want to delete "${card.title}"?`,
      [
        { text: language === "nl" ? "Annuleren" : "Cancel", style: "cancel" },
        {
          text: language === "nl" ? "Verwijderen" : "Delete",
          style: "destructive",
          onPress: () => removeMediaFile(card.id),
        },
      ]
    );
  };

  const renderCardItem = (card: MediaFile) => (
    <View key={card.id} style={styles.cardItem}>
      <TouchableOpacity
        style={styles.cardImageContainer}
        onPress={() => setSelectedCard(card)}
      >
        <Image source={{ uri: card.uri }} style={styles.cardImage} resizeMode="cover" />
        <View style={styles.cardOverlay}>
          <Eye size={24} color="white" />
        </View>
      </TouchableOpacity>
      
      <View style={styles.cardInfo}>
        <Text style={[styles.cardTitle, { color: colors.text }]}>
          {card.title}
        </Text>
        <Text style={[styles.cardDate, { color: colors.muted }]}>
          {new Date(card.date).toLocaleDateString(
            language === "nl" ? "nl-NL" : "en-US",
            { year: "numeric", month: "short", day: "numeric" }
          )}
        </Text>
      </View>

      <View style={styles.cardActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleEditCard(card)}
        >
          <Edit size={16} color={colors.primary} />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleDeleteCard(card)}
        >
          <Trash2 size={16} color={colors.danger} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderFullScreenCard = () => {
    if (!selectedCard) return null;

    return (
      <View style={styles.fullScreenOverlay}>
        <TouchableOpacity
          style={styles.fullScreenClose}
          onPress={() => setSelectedCard(null)}
        >
          <ArrowLeft size={24} color="white" />
        </TouchableOpacity>
        
        <View style={styles.fullScreenContainer}>
          <Image
            source={{ uri: selectedCard.uri }}
            style={styles.fullScreenImage}
            resizeMode="contain"
          />
        </View>
        
        <View style={styles.fullScreenInfo}>
          <Text style={styles.fullScreenTitle}>{selectedCard.title}</Text>
          <Text style={styles.fullScreenDate}>
            {new Date(selectedCard.date).toLocaleDateString(
              language === "nl" ? "nl-NL" : "en-US",
              { year: "numeric", month: "long", day: "numeric" }
            )}
          </Text>
        </View>
      </View>
    );
  };

  if (selectedCard) {
    return renderFullScreenCard();
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onClose}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          {language === "nl" ? "Noodkaarten" : "Emergency Cards"}
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              Alert.alert(
                language === "nl" ? "Nieuwe Noodkaart" : "New Emergency Card",
                language === "nl" 
                  ? "Hoe wil je de noodkaart toevoegen?"
                  : "How would you like to add the emergency card?",
                [
                  { text: language === "nl" ? "Annuleren" : "Cancel", style: "cancel" },
                  {
                    text: language === "nl" ? "Camera" : "Camera",
                    onPress: handleTakePhoto,
                  },
                  {
                    text: language === "nl" ? "Fotobibliotheek" : "Photo Library",
                    onPress: handleSelectImage,
                  },
                ]
              );
            }}
          >
            <Plus size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.infoCard}>
          <Text style={[styles.infoTitle, { color: colors.text }]}>
            {language === "nl" ? "Over Noodkaarten" : "About Emergency Cards"}
          </Text>
          <Text style={[styles.infoText, { color: colors.muted }]}>
            {language === "nl"
              ? "Een noodkaart bevat belangrijke contactinformatie die je altijd bij je kunt hebben. Maak een foto van je kaart om deze digitaal op te slaan."
              : "An emergency card contains important contact information that you can always carry with you. Take a photo of your card to store it digitally."}
          </Text>
        </View>

        <View style={styles.actionsCard}>
          <TouchableOpacity
            style={[styles.actionCard, { backgroundColor: colors.primary + "10" }]}
            onPress={handleTakePhoto}
          >
            <Camera size={24} color={colors.primary} />
            <Text style={[styles.actionText, { color: colors.primary }]}>
              {language === "nl" ? "Foto Maken" : "Take Photo"}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionCard, { backgroundColor: colors.muted + "10" }]}
            onPress={handleSelectImage}
          >
            <ImageIcon size={24} color={colors.muted} />
            <Text style={[styles.actionText, { color: colors.muted }]}>
              {language === "nl" ? "Selecteer Afbeelding" : "Select Image"}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.cardsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {language === "nl" ? "Mijn Noodkaarten" : "My Emergency Cards"} ({emergencyCards.length})
          </Text>

          {emergencyCards.length === 0 ? (
            <View style={styles.emptyState}>
              <IdCard size={48} color={colors.muted} />
              <Text style={[styles.emptyTitle, { color: colors.text }]}>
                {language === "nl" ? "Geen Noodkaarten" : "No Emergency Cards"}
              </Text>
              <Text style={[styles.emptyText, { color: colors.muted }]}>
                {language === "nl"
                  ? "Voeg je eerste noodkaart toe om belangrijke contactinformatie bij de hand te hebben."
                  : "Add your first emergency card to have important contact information at hand."}
              </Text>
            </View>
          ) : (
            emergencyCards.map(renderCardItem)
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};