/* eslint-disable react-native/no-unused-styles */
import React, { useState, useRef } from "react";
import {
  View,
  Text,
  Modal,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  FlatList,
  StatusBar,
} from "react-native";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { LibraryItem } from "@/store/media/library-store";
import {
  X,
  ChevronLeft,
  ChevronRight,
  Share,
  Trash2,
} from "lucide-react-native";

const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

interface ImageViewerProps {
  visible: boolean;
  images: LibraryItem[];
  initialIndex: number;
  onClose: () => void;
  onDelete?: (item: LibraryItem) => void;
  onShare?: (item: LibraryItem) => void;
}

export const ImageViewer: React.FC<ImageViewerProps> = ({
  visible,
  images,
  initialIndex,
  onClose,
  onDelete,
  onShare,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const flatListRef = useRef<FlatList>(null);

  const styles = createStyles(colors);

  const currentImage = images[currentIndex];

  const handlePrevious = () => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      flatListRef.current?.scrollToIndex({ index: newIndex, animated: true });
    }
  };

  const handleNext = () => {
    if (currentIndex < images.length - 1) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
      flatListRef.current?.scrollToIndex({ index: newIndex, animated: true });
    }
  };

  const onViewableItemsChanged = ({ viewableItems }: { viewableItems: Array<{ index: number | null }> }) => {
    if (viewableItems.length > 0 && viewableItems[0].index !== null) {
      setCurrentIndex(viewableItems[0].index);
    }
  };

  const renderImage = ({ item }: { item: LibraryItem }) => (
    <View style={styles.imageContainer}>
      <Image
        source={{ uri: item.uri }}
        style={styles.image}
        resizeMode="contain"
      />
    </View>
  );

  if (!visible || !currentImage) return null;

  return (
    <Modal
      visible={visible}
      transparent={false}
      animationType="fade"
      onRequestClose={onClose}
    >
      <StatusBar hidden />
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerButton} onPress={onClose}>
            <X size={24} color={colors.text} />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle} numberOfLines={1}>
              {currentImage.title}
            </Text>
            <Text style={styles.headerSubtitle}>
              {currentIndex + 1} of {images.length}
            </Text>
          </View>
          <View style={styles.headerActions}>
            {onShare && (
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => onShare(currentImage)}
              >
                <Share size={20} color={colors.text} />
              </TouchableOpacity>
            )}
            {onDelete && (
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => onDelete(currentImage)}
              >
                <Trash2 size={20} color={colors.danger} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Image Gallery */}
        <FlatList
          ref={flatListRef}
          data={images}
          renderItem={renderImage}
          keyExtractor={(item) => item.id}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          initialScrollIndex={initialIndex}
          onViewableItemsChanged={onViewableItemsChanged}
          viewabilityConfig={{ itemVisiblePercentThreshold: 50 }}
          getItemLayout={(data, index) => ({
            length: screenWidth,
            offset: screenWidth * index,
            index,
          })}
        />

        {/* Navigation Controls */}
        {images.length > 1 && (
          <>
            {currentIndex > 0 && (
              <TouchableOpacity
                style={[styles.navButton, styles.navButtonLeft]}
                onPress={handlePrevious}
              >
                <ChevronLeft size={32} color={colors.text} />
              </TouchableOpacity>
            )}
            {currentIndex < images.length - 1 && (
              <TouchableOpacity
                style={[styles.navButton, styles.navButtonRight]}
                onPress={handleNext}
              >
                <ChevronRight size={32} color={colors.text} />
              </TouchableOpacity>
            )}
          </>
        )}

        {/* Image Info */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            {currentImage.width && currentImage.height
              ? `${currentImage.width} × ${currentImage.height}`
              : ""}
            {currentImage.fileSize
              ? ` • ${(currentImage.fileSize / 1024 / 1024).toFixed(1)} MB`
              : ""}
          </Text>
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: typeof Colors.light) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      flex: 1,
    },
    footer: {
      alignItems: "center",
      backgroundColor: colors.background + "E6",
      bottom: 0,
      left: 0,
      paddingBottom: 20,
      paddingTop: 10,
      position: "absolute",
      right: 0,
    },
    footerText: {
      color: colors.muted,
      fontSize: 12,
    },
    header: {
      alignItems: "center",
      backgroundColor: colors.background + "E6",
      flexDirection: "row",
      left: 0,
      paddingHorizontal: 16,
      paddingTop: 50,
      paddingVertical: 16,
      position: "absolute",
      right: 0,
      top: 0,
      zIndex: 1,
    },
    headerActions: {
      alignItems: "center",
      flexDirection: "row",
      gap: 8,
    },
    headerButton: {
      alignItems: "center",
      borderRadius: 20,
      height: 40,
      justifyContent: "center",
      width: 40,
    },
    headerCenter: {
      alignItems: "center",
      flex: 1,
      marginHorizontal: 16,
    },
    headerSubtitle: {
      color: colors.muted,
      fontSize: 12,
      marginTop: 2,
    },
    headerTitle: {
      color: colors.text,
      fontSize: 16,
      fontWeight: "600",
    },
    image: {
      height: "100%",
      width: "100%",
    },
    imageContainer: {
      alignItems: "center",
      height: screenHeight,
      justifyContent: "center",
      width: screenWidth,
    },
    navButton: {
      alignItems: "center",
      backgroundColor: colors.background + "80",
      borderRadius: 25,
      height: 50,
      justifyContent: "center",
      position: "absolute",
      top: "50%",
      width: 50,
      zIndex: 1,
    },
    navButtonLeft: {
      left: 20,
    },
    navButtonRight: {
      right: 20,
    },
  }); 