/* eslint-disable react-native/no-unused-styles */
import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Alert,
} from "react-native";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { LibraryItem, useLibraryStore } from "@/store/media/library-store";
import { useAudioPlayerStore } from "@/store/media/audio-player-store";
import {
  FileText,
  Music,
  Image as ImageIcon,
  Play,
  Pause,
  Edit,
  Trash2,
  Share,

} from "lucide-react-native";

interface LibraryItemProps {
  item: LibraryItem;
  viewMode: "grid" | "list";
  isSelected: boolean;
  onPress: () => void;
  onLongPress: () => void;
}

export const LibraryItemComponent: React.FC<LibraryItemProps> = ({
  item,
  viewMode,
  isSelected,
  onPress,
  onLongPress,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { deleteItem, updateItem } = useLibraryStore();
  const { playTrack, currentTrack, isPlaying, pause, play } = useAudioPlayerStore();

  const styles = createStyles(colors, viewMode, isSelected);

  const getFileIcon = () => {
    switch (item.type) {
      case "image":
        return <ImageIcon size={viewMode === "grid" ? 24 : 20} color={colors.primary} />;
      case "document":
        return <FileText size={viewMode === "grid" ? 24 : 20} color={colors.primary} />;
      case "music":
        return <Music size={viewMode === "grid" ? 24 : 20} color={colors.primary} />;
      default:
        return <FileText size={viewMode === "grid" ? 24 : 20} color={colors.primary} />;
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "";
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handlePlayMusic = async () => {
    if (item.type === "music") {
      const track = {
        id: item.id,
        title: item.title,
        uri: item.uri,
        duration: item.duration,
      };

      if (currentTrack?.id === item.id) {
        if (isPlaying) {
          pause();
        } else {
          play();
        }
      } else {
        await playTrack(track);
      }
    }
  };

  const handleEdit = () => {
    Alert.prompt(
      "Edit Item",
      "Enter new title:",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Save",
          onPress: (newTitle) => {
            if (newTitle && newTitle.trim()) {
              updateItem(item.id, { title: newTitle.trim() });
            }
          },
        },
      ],
      "plain-text",
      item.title
    );
  };

  const handleDelete = () => {
    Alert.alert(
      "Delete Item",
      `Are you sure you want to delete "${item.title}"?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => deleteItem(item.id),
        },
      ]
    );
  };

  const handleShare = () => {
    // In a real app, you would implement sharing functionality
    Alert.alert("Share", `Sharing "${item.title}" is not implemented yet.`);
  };

  const renderThumbnail = () => {
    if (item.type === "image" && item.uri) {
      return (
        <Image
          source={{ uri: item.uri }}
          style={styles.thumbnail}
          resizeMode="cover"
        />
      );
    }
    return (
      <View style={styles.iconContainer}>
        {getFileIcon()}
      </View>
    );
  };

  const renderActions = () => {
    return (
      <View style={styles.actionsContainer}>
        {item.type === "music" && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handlePlayMusic}
          >
            {currentTrack?.id === item.id && isPlaying ? (
              <Pause size={16} color={colors.primary} />
            ) : (
              <Play size={16} color={colors.primary} />
            )}
          </TouchableOpacity>
        )}
        <TouchableOpacity style={styles.actionButton} onPress={handleEdit}>
          <Edit size={16} color={colors.muted} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
          <Share size={16} color={colors.muted} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>
          <Trash2 size={16} color={colors.danger} />
        </TouchableOpacity>
      </View>
    );
  };

  if (viewMode === "grid") {
    return (
      <TouchableOpacity
        style={styles.gridContainer}
        onPress={onPress}
        onLongPress={onLongPress}
        activeOpacity={0.7}
      >
        {renderThumbnail()}
        <View style={styles.gridContent}>
          <Text style={styles.gridTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <Text style={styles.gridSubtitle}>
            {formatFileSize(item.fileSize)}
          </Text>
        </View>
        {renderActions()}
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={styles.listContainer}
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.7}
    >
      {renderThumbnail()}
      <View style={styles.listContent}>
        <Text style={styles.listTitle} numberOfLines={1}>
          {item.title}
        </Text>
        <Text style={styles.listSubtitle}>
          {item.fileType} • {formatFileSize(item.fileSize)} • {formatDate(item.dateAdded)}
        </Text>
      </View>
      {renderActions()}
    </TouchableOpacity>
  );
};

const createStyles = (
  colors: typeof Colors.light,
  viewMode: "grid" | "list",
  isSelected: boolean
) =>
  StyleSheet.create({
    actionButton: {
      alignItems: "center",
      borderRadius: 16,
      height: 32,
      justifyContent: "center",
      width: 32,
    },
    actionsContainer: {
      alignItems: "center",
      flexDirection: "row",
      gap: 4,
    },
    gridContainer: {
      backgroundColor: isSelected ? colors.primary + "20" : colors.card,
      borderColor: isSelected ? colors.primary : colors.border,
      borderRadius: 12,
      borderWidth: 1,
      flex: 1,
      margin: 4,
      overflow: "hidden",
      padding: 12,
    },
    gridContent: {
      flex: 1,
      marginTop: 8,
    },
    gridSubtitle: {
      color: colors.muted,
      fontSize: 12,
      marginTop: 4,
    },
    gridTitle: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "600",
    },
    iconContainer: {
      alignItems: "center",
      backgroundColor: colors.background,
      borderRadius: 8,
      height: viewMode === "grid" ? 80 : 48,
      justifyContent: "center",
      width: "100%",
    },
    listContainer: {
      alignItems: "center",
      backgroundColor: isSelected ? colors.primary + "20" : colors.card,
      borderColor: isSelected ? colors.primary : colors.border,
      borderRadius: 12,
      borderWidth: 1,
      flexDirection: "row",
      marginHorizontal: 16,
      marginVertical: 4,
      padding: 12,
    },
    listContent: {
      flex: 1,
      marginLeft: 12,
    },
    listSubtitle: {
      color: colors.muted,
      fontSize: 12,
      marginTop: 2,
    },
    listTitle: {
      color: colors.text,
      fontSize: 16,
      fontWeight: "600",
    },
    thumbnail: {
      borderRadius: 8,
      height: viewMode === "grid" ? 80 : 48,
      width: viewMode === "grid" ? "100%" : 48,
    },
  }); 