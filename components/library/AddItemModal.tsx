/* eslint-disable react-native/no-unused-styles */
import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Alert,
} from "react-native";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { useLibraryStore } from "@/store/media/library-store";
import {
  X,
  Image as ImageIcon,
  FileText,
  Music,
  Upload,
} from "lucide-react-native";

interface AddItemModalProps {
  visible: boolean;
  onClose: () => void;
}

export const AddItemModal: React.FC<AddItemModalProps> = ({
  visible,
  onClose,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { importFromDevice, isLoading } = useLibraryStore();

  const styles = createStyles(colors);

  const handleImport = async (type: "image" | "document" | "music") => {
    try {
      await importFromDevice(type);
      onClose();
    } catch {
      Alert.alert("Error", "Failed to import file. Please try again.");
    }
  };

  const importOptions = [
    {
      type: "image" as const,
      title: "Images",
      description: "Import photos and images",
      icon: <ImageIcon size={32} color={colors.primary} />,
      color: colors.primary,
    },
    {
      type: "document" as const,
      title: "Documents",
      description: "Import PDFs, text files, and more",
      icon: <FileText size={32} color={colors.accent} />,
      color: colors.accent,
    },
    {
      type: "music" as const,
      title: "Music",
      description: "Import audio files and music",
      icon: <Music size={32} color={colors.secondary} />,
      color: colors.secondary,
    },
  ];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Add to Library</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <X size={24} color={colors.muted} />
            </TouchableOpacity>
          </View>

          {/* Content */}
          <View style={styles.content}>
            <Text style={styles.subtitle}>
              Choose what type of file you want to add to your library
            </Text>

            <View style={styles.optionsContainer}>
              {importOptions.map((option) => (
                <TouchableOpacity
                  key={option.type}
                  style={[
                    styles.optionButton,
                    { borderColor: option.color + "30" },
                  ]}
                  onPress={() => handleImport(option.type)}
                  disabled={isLoading}
                >
                  <View style={styles.optionIcon}>{option.icon}</View>
                  <View style={styles.optionContent}>
                    <Text style={styles.optionTitle}>{option.title}</Text>
                    <Text style={styles.optionDescription}>
                      {option.description}
                    </Text>
                  </View>
                  <Upload size={20} color={colors.muted} />
                </TouchableOpacity>
              ))}
            </View>

            {isLoading && (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>Importing files...</Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: typeof Colors.light) =>
  StyleSheet.create({
    closeButton: {
      alignItems: "center",
      height: 40,
      justifyContent: "center",
      width: 40,
    },
    container: {
      backgroundColor: colors.card,
      borderRadius: 20,
      margin: 20,
      maxHeight: "80%",
      overflow: "hidden",
    },
    content: {
      padding: 20,
    },
    header: {
      alignItems: "center",
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      padding: 20,
    },
    loadingContainer: {
      alignItems: "center",
      marginTop: 20,
      padding: 20,
    },
    loadingText: {
      color: colors.muted,
      fontSize: 16,
    },
    optionButton: {
      alignItems: "center",
      backgroundColor: colors.background,
      borderRadius: 16,
      borderWidth: 2,
      flexDirection: "row",
      marginBottom: 12,
      padding: 16,
    },
    optionContent: {
      flex: 1,
      marginLeft: 16,
    },
    optionDescription: {
      color: colors.muted,
      fontSize: 14,
      marginTop: 4,
    },
    optionIcon: {
      alignItems: "center",
      height: 48,
      justifyContent: "center",
      width: 48,
    },
    optionTitle: {
      color: colors.text,
      fontSize: 18,
      fontWeight: "600",
    },
    optionsContainer: {
      marginTop: 20,
    },
    overlay: {
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      flex: 1,
      justifyContent: "center",
    },
    subtitle: {
      color: colors.muted,
      fontSize: 16,
      lineHeight: 24,
      textAlign: "center",
    },
    title: {
      color: colors.text,
      fontSize: 24,
      fontWeight: "700",
    },
  }); 