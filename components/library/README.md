# Library Feature

The Library feature allows users to manage their personal collection of images, documents, and music files within the SobrixHealth app.

## Features

### File Management
- **Import Files**: Add images, documents, and music from device storage
- **View Files**: Display files in grid or list view with thumbnails
- **Edit Files**: Rename files and update metadata
- **Delete Files**: Remove individual files or multiple selected files
- **Search & Filter**: Find files by name, type, or tags
- **Sort Options**: Sort by name, date, size, or file type
- **Persistent Storage**: Files are saved and persist between app sessions

### File Types Supported
- **Images**: JPG, PNG, GIF, and other image formats
- **Documents**: PDF, TXT, MD, JSON, and other document formats  
- **Music**: MP3, WAV, AAC, and other audio formats

### Image Viewing
- **Full-Screen Viewer**: View images in full-screen mode
- **Swipe Navigation**: Swipe between multiple images
- **Zoom & Pan**: Pinch to zoom and pan around images
- **Image Gallery**: Navigate through all images in the library
- **Image Actions**: Share, delete, and view image details

### Document Viewing & Editing
- **Text File Editor**: Edit TXT and MD files directly in the app
- **JSON File Viewer**: View and edit JSON files with automatic formatting
- **PDF Viewer**: View PDF documents (opens in external app via sharing)
- **Print Support**: Print documents, text files, and JSON files
- **Save Changes**: Save edited text and JSON files back to storage
- **External App Support**: Open files in external apps via system sharing
- **File Sharing**: Improved file sharing for better external app compatibility

### Music Playback
- Play music files directly from the library
- Integration with the existing audio player store
- Play/pause controls for each music file
- Queue management for continuous playback

## Components

### LibraryHeader
- Search bar for finding files
- Filter buttons for file types (All, Images, Documents, Music)
- Sort controls with ascending/descending order
- View mode toggle (Grid/List)
- Add button to import new files

### LibraryItemComponent
- Displays individual library items
- Supports both grid and list view modes
- Shows file thumbnails for images
- File type icons for documents and music
- Action buttons for play (music), edit, share, and delete
- Selection support for bulk operations

### AddItemModal
- Modal interface for importing files
- Separate options for images, documents, and music
- Uses expo-image-picker for images
- Uses expo-document-picker for documents and music
- Loading states during import process

### ImageViewer
- Full-screen image viewing modal
- Horizontal swipe navigation between images
- Image information display (dimensions, file size)
- Action buttons for share and delete
- Navigation controls for previous/next image
- Supports viewing all images in the library

### DocumentViewer
- Document viewing and editing modal
- Text file editor with save functionality
- JSON file viewer with automatic formatting and editing
- PDF viewer with improved external app integration
- Print support for documents, text files, and JSON files
- Edit mode toggle for text and JSON files
- File sharing functionality for external app compatibility
- Loading states and error handling

### LibraryScreen (Main Tab)
- Main library interface with all components
- Handles file selection and bulk operations
- Empty state when no files are present
- Selection bar for bulk actions
- Integration with image and document viewers
- Integration with tab navigation

## State Management

### LibraryStore (Zustand)
- Manages library items collection with persistent storage
- Handles CRUD operations
- Search and filter functionality
- Selection state management
- Import/export operations
- Sorting and view preferences
- AsyncStorage integration for data persistence

### Data Structure
```typescript
interface LibraryItem {
  id: string;
  title: string;
  type: "image" | "document" | "music";
  uri: string;
  fileName: string;
  fileType: string;
  fileSize?: number;
  dateAdded: string;
  dateModified?: string;
  thumbnailUri?: string;
  duration?: number; // for music files
  width?: number; // for images
  height?: number; // for images
  description?: string;
  tags?: string[];
}
```

## Usage

### Adding Files
1. Tap the "+" button in the header
2. Select file type (Images, Documents, or Music)
3. Choose files from device storage
4. Files are automatically imported to the library

### Viewing Files
- Switch between grid and list view using the view toggle
- Use search to find specific files
- Filter by file type using filter buttons
- Sort files by different criteria
- Tap images to open full-screen viewer with swipe navigation
- Tap documents to open viewer/editor based on file type

### Managing Files
- Tap a file to open appropriate viewer (images/documents)
- Long press to enter selection mode
- Use action buttons to edit, share, or delete
- Select multiple files for bulk operations
- Edit text and JSON files directly in the document viewer
- Print documents using the built-in print functionality
- Open PDFs and other files in external apps via improved sharing

### Playing Music
- Tap the play button on music files
- Music integrates with the app's audio player
- Control playback from the library or audio player

## Dependencies

- `expo-image-picker`: For importing images from device
- `expo-document-picker`: For importing documents and music
- `expo-file-system`: For file operations and metadata
- `expo-print`: For printing documents
- `expo-sharing`: For sharing files
- `zustand`: For state management with persistence
- `@react-native-async-storage/async-storage`: For persistent storage
- `lucide-react-native`: For icons

## Navigation

The Library is accessible as a tab in the main navigation:
- Tab icon: FolderOpen (Lucide)
- Tab title: "Library" (English) / "Bibliotheek" (Dutch)
- Position: Between Mindfulness and Contacts tabs 