/* eslint-disable react-native/no-unused-styles */
import React from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { useLibraryStore } from "@/store/media/library-store";
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Grid,
  List,
  Plus,
} from "lucide-react-native";

interface LibraryHeaderProps {
  viewMode: "grid" | "list";
  onViewModeChange: (mode: "grid" | "list") => void;
  onAddPress: () => void;
}

export const LibraryHeader: React.FC<LibraryHeaderProps> = ({
  viewMode,
  onViewModeChange,
  onAddPress,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const {
    searchQuery,
    filterType,
    sortBy,
    sortOrder,
    setSearchQuery,
    setFilterType,
    setSortBy,
    setSortOrder,
  } = useLibraryStore();

  const styles = createStyles(colors);

  const filterOptions = [
    { value: "all", label: "All" },
    { value: "image", label: "Images" },
    { value: "document", label: "Documents" },
    { value: "music", label: "Music" },
  ];

  const sortOptions = [
    { value: "name", label: "Name" },
    { value: "date", label: "Date" },
    { value: "size", label: "Size" },
    { value: "type", label: "Type" },
  ];

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Search size={20} color={colors.muted} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search library..."
          placeholderTextColor={colors.muted}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Filter and Sort Controls */}
      <View style={styles.controlsContainer}>
        {/* Filter */}
        <View style={styles.filterContainer}>
          <Filter size={16} color={colors.muted} />
          <Text style={styles.controlLabel}>Filter:</Text>
          {filterOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.filterButton,
                filterType === option.value && styles.activeFilterButton,
              ]}
              onPress={() => setFilterType(option.value as "all" | "image" | "document" | "music")}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  filterType === option.value && styles.activeFilterButtonText,
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Sort */}
        <View style={styles.sortContainer}>
          <TouchableOpacity
            style={styles.sortButton}
            onPress={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
          >
            {sortOrder === "asc" ? (
              <SortAsc size={16} color={colors.muted} />
            ) : (
              <SortDesc size={16} color={colors.muted} />
            )}
          </TouchableOpacity>
          <Text style={styles.controlLabel}>Sort:</Text>
          {sortOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.sortOptionButton,
                sortBy === option.value && styles.activeSortOptionButton,
              ]}
              onPress={() => setSortBy(option.value as "name" | "date" | "size" | "type")}
            >
              <Text
                style={[
                  styles.sortOptionButtonText,
                  sortBy === option.value && styles.activeSortOptionButtonText,
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={styles.viewModeButton}
          onPress={() => onViewModeChange(viewMode === "grid" ? "list" : "grid")}
        >
          {viewMode === "grid" ? (
            <List size={20} color={colors.muted} />
          ) : (
            <Grid size={20} color={colors.muted} />
          )}
        </TouchableOpacity>

        <TouchableOpacity style={styles.addButton} onPress={onAddPress}>
          <Plus size={20} color={colors.background} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const createStyles = (colors: typeof Colors.light) =>
  StyleSheet.create({
    actionContainer: {
      alignItems: "center",
      flexDirection: "row",
      gap: 12,
    },
    activeFilterButton: {
      backgroundColor: colors.primary,
    },
    activeFilterButtonText: {
      color: colors.background,
      fontWeight: "600",
    },
    activeSortOptionButton: {
      backgroundColor: colors.primary,
    },
    activeSortOptionButtonText: {
      color: colors.background,
      fontWeight: "600",
    },
    addButton: {
      alignItems: "center",
      backgroundColor: colors.primary,
      borderRadius: 20,
      height: 40,
      justifyContent: "center",
      width: 40,
    },
    container: {
      backgroundColor: colors.card,
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
      gap: 16,
      padding: 16,
    },
    controlLabel: {
      color: colors.muted,
      fontSize: 12,
      fontWeight: "500",
    },
    controlsContainer: {
      gap: 12,
    },
    filterButton: {
      borderColor: colors.border,
      borderRadius: 16,
      borderWidth: 1,
      paddingHorizontal: 12,
      paddingVertical: 6,
    },
    filterButtonText: {
      color: colors.text,
      fontSize: 12,
      fontWeight: "500",
    },
    filterContainer: {
      alignItems: "center",
      flexDirection: "row",
      flexWrap: "wrap",
      gap: 8,
    },
    searchContainer: {
      alignItems: "center",
      backgroundColor: colors.background,
      borderColor: colors.border,
      borderRadius: 12,
      borderWidth: 1,
      flexDirection: "row",
      paddingHorizontal: 12,
      paddingVertical: 8,
    },
    searchIcon: {
      marginRight: 8,
    },
    searchInput: {
      color: colors.text,
      flex: 1,
      fontSize: 16,
    },
    sortButton: {
      alignItems: "center",
      borderColor: colors.border,
      borderRadius: 8,
      borderWidth: 1,
      height: 32,
      justifyContent: "center",
      width: 32,
    },
    sortContainer: {
      alignItems: "center",
      flexDirection: "row",
      flexWrap: "wrap",
      gap: 8,
    },
    sortOptionButton: {
      borderColor: colors.border,
      borderRadius: 16,
      borderWidth: 1,
      paddingHorizontal: 12,
      paddingVertical: 6,
    },
    sortOptionButtonText: {
      color: colors.text,
      fontSize: 12,
      fontWeight: "500",
    },
    viewModeButton: {
      alignItems: "center",
      borderColor: colors.border,
      borderRadius: 8,
      borderWidth: 1,
      height: 40,
      justifyContent: "center",
      width: 40,
    },
  }); 