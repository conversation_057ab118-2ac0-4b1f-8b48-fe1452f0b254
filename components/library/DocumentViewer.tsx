/* eslint-disable react-native/no-unused-styles */
import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
  Platform,
} from "react-native";
import { WebView } from "react-native-webview";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { LibraryItem, useLibraryStore } from "@/store/media/library-store";
import * as FileSystem from "expo-file-system";
import * as Print from "expo-print";
import * as Sharing from "expo-sharing";
import {
  X,
  Edit,
  Save,
  Printer,
  Share,
  FileText,
  ExternalLink,
} from "lucide-react-native";

interface DocumentViewerProps {
  visible: boolean;
  document: LibraryItem | null;
  onClose: () => void;
  onDelete?: (item: LibraryItem) => void;
  onShare?: (item: LibraryItem) => void;
}

export const DocumentViewer: React.FC<DocumentViewerProps> = ({
  visible,
  document,
  onClose,
  onDelete: _onDelete,
  onShare,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { updateItem } = useLibraryStore();
  
  const [content, setContent] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [editedContent, setEditedContent] = useState("");
  const [webViewError, setWebViewError] = useState<string | null>(null);
  const [pdfDataUri, setPdfDataUri] = useState<string | null>(null);

  const [pdfFileValid, setPdfFileValid] = useState<boolean | null>(null);

  const styles = createStyles(colors);

  const isTextFile = document?.fileType?.includes("text") || 
                    document?.fileName?.endsWith(".txt") ||
                    document?.fileName?.endsWith(".md");
  
  const isJsonFile = document?.fileType?.includes("json") || 
                    document?.fileName?.endsWith(".json");
  
  const isPdfFile = document?.fileType?.includes("pdf") || 
                   document?.fileName?.endsWith(".pdf");

  const loadTextContent = useCallback(async () => {
    if (!document || (!isTextFile && !isJsonFile)) return;
    
    try {
      setIsLoading(true);
      let fileContent = await FileSystem.readAsStringAsync(document.uri);
      
      // Format JSON files for better readability
      if (isJsonFile) {
        try {
          const jsonObject = JSON.parse(fileContent);
          fileContent = JSON.stringify(jsonObject, null, 2);
        } catch {
          // If JSON parsing fails, keep original content
          console.warn("Invalid JSON format, displaying as-is");
        }
      }
      
      setContent(fileContent);
      setEditedContent(fileContent);
    } catch {
      console.error("Error reading file");
      Alert.alert("Error", "Could not read the file content.");
    } finally {
      setIsLoading(false);
    }
  }, [document, isTextFile, isJsonFile]);

  useEffect(() => {
    if (visible && document && (isTextFile || isJsonFile)) {
      loadTextContent();
    }
  }, [visible, document, isTextFile, isJsonFile, loadTextContent]);

  const handleSave = async () => {
    if (!document) return;

    try {
      setIsLoading(true);
      await FileSystem.writeAsStringAsync(document.uri, editedContent);
      setContent(editedContent);
      setIsEditing(false);
      
      // Update the modification date
      updateItem(document.id, {
        dateModified: new Date().toISOString(),
      });
      
      Alert.alert("Success", "Document saved successfully.");
    } catch (error) {
      console.error("Error saving file:", error);
      Alert.alert("Error", "Could not save the file.");
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrint = async () => {
    if (!document) return;

    try {
      let htmlContent = "";
      
      if (isTextFile || isJsonFile) {
        // Convert text content to HTML for printing
        const textContent = content || editedContent;
        htmlContent = `
          <html>
            <head>
              <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { color: #333; }
                pre { white-space: pre-wrap; font-family: monospace; }
              </style>
            </head>
            <body>
              <h1>${document.title}</h1>
              <pre>${textContent}</pre>
            </body>
          </html>
        `;
      } else {
        htmlContent = `
          <html>
            <body>
              <h1>${document.title}</h1>
              <p>Document type: ${document.fileType}</p>
              <p>File size: ${document.fileSize ? (document.fileSize / 1024 / 1024).toFixed(2) + " MB" : "Unknown"}</p>
              <p>This document cannot be displayed in the print preview.</p>
            </body>
          </html>
        `;
      }

      await Print.printAsync({
        html: htmlContent,
      });
    } catch (error) {
      console.error("Error printing:", error);
      Alert.alert("Error", "Could not print the document.");
    }
  };

  const handleOpenExternal = async () => {
    if (!document) return;

    try {
      // Check if sharing is available
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        Alert.alert("Error", "Sharing is not available on this device.");
        return;
      }

      // For files imported from device picker, we need to copy them to a shareable location
      const fileName = document.fileName;
      const newUri = `${FileSystem.documentDirectory}${fileName}`;
      
      // Check if source file exists
      const fileInfo = await FileSystem.getInfoAsync(document.uri);
      if (!fileInfo.exists) {
        Alert.alert("Error", "The original file could not be found. It may have been moved or deleted.");
        return;
      }
      
      // Copy the file to document directory
      await FileSystem.copyAsync({
        from: document.uri,
        to: newUri,
      });

      // Use sharing to open the file in external apps
      await Sharing.shareAsync(newUri, {
        mimeType: document.fileType,
        dialogTitle: `Open ${document.title}`,
        UTI: document.fileType, // iOS Universal Type Identifier
      });
    } catch (error) {
      console.error("Error opening external:", error);
      Alert.alert("Error", "Could not open the file. Please try again.");
    }
  };

  // Convert PDF file to data URI for better WebView compatibility
  const loadPdfAsDataUri = useCallback(async () => {
    if (!document || !isPdfFile) return;
    
    try {
      // Check file size first
      const fileInfo = await FileSystem.getInfoAsync(document.uri);
      console.log('File info:', fileInfo);
      
      if (!fileInfo.exists) {
        console.error('PDF file does not exist at URI:', document.uri);
        setPdfDataUri(null);
        setPdfFileValid(false);
        return;
      }
      
      // Always mark as valid if file exists - let WebView handle the actual rendering
      setPdfFileValid(true);
      
      const fileSizeInMB = (fileInfo.exists && 'size' in fileInfo) ? fileInfo.size / (1024 * 1024) : 0;
      console.log(`PDF file size: ${fileSizeInMB.toFixed(2)} MB`);
      
      // Only convert files smaller than 5MB to data URI to prevent memory issues
      if (fileSizeInMB > 5) {
        console.log('PDF file too large for data URI conversion, using direct file URI');
        setPdfDataUri(null);
        return;
      }
      
      // Skip data URI conversion on iOS as it doesn't work well
      if (Platform.OS === 'ios') {
        console.log('iOS detected, skipping data URI conversion for better compatibility');
        setPdfDataUri(null);
        return;
      }
      
      console.log('Converting PDF to data URI for WebView compatibility');
      
      const base64 = await FileSystem.readAsStringAsync(document.uri, {
        encoding: FileSystem.EncodingType.Base64,
      });
      
      if (!base64 || base64.length === 0) {
        console.error('PDF conversion resulted in empty base64 string');
        setPdfDataUri(null);
        return;
      }
      
      const dataUri = `data:application/pdf;base64,${base64}`;
      setPdfDataUri(dataUri);
      console.log('PDF converted to data URI successfully, length:', base64.length);
    } catch (error) {
      console.error('Error loading PDF:', error);
      setPdfDataUri(null);
      // Still mark as valid to allow fallback to external viewer
      setPdfFileValid(true);
    }
  }, [document, isPdfFile]);

  useEffect(() => {
    if (visible && document && isPdfFile && Platform.OS !== 'web') {
      // Reset state when opening a new document
      setPdfDataUri(null);
      setWebViewError(null);
      setPdfFileValid(null); // Reset validation state
      
      // Use setTimeout to prevent blocking the UI thread
      setTimeout(() => {
        loadPdfAsDataUri();
      }, 100);
    }
  }, [visible, document, isPdfFile, loadPdfAsDataUri]);

  const renderTextEditor = () => (
    <ScrollView style={styles.contentContainer}>
      <TextInput
        style={[styles.textInput, isEditing && styles.textInputEditing]}
        value={isEditing ? editedContent : content}
        onChangeText={setEditedContent}
        multiline
        editable={isEditing}
        placeholder="Document content..."
        placeholderTextColor={colors.muted}
      />
    </ScrollView>
  );

  const renderPdfViewer = () => {
    // Check if PDF file validation is complete and file is invalid
    if (pdfFileValid === false) {
      return (
        <View style={styles.pdfContainer}>
          <FileText size={64} color={colors.muted} />
          <Text style={styles.pdfTitle}>Corrupted PDF</Text>
          <Text style={styles.pdfSubtitle}>
            This PDF file appears to be corrupted or empty and cannot be displayed. 
            Try opening it in an external app or re-importing the file.
          </Text>
          <TouchableOpacity style={styles.openButton} onPress={handleOpenExternal}>
            <Text style={styles.openButtonText}>Try External App</Text>
          </TouchableOpacity>
        </View>
      );
    }

    // Show loading while validation is in progress
    if (pdfFileValid === null && Platform.OS !== 'web') {
      return (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Validating PDF file...</Text>
        </View>
      );
    }

    // For iOS, WebView doesn't support PDF viewing well, so show a simpler UI
    if (Platform.OS === 'ios' && document?.uri && pdfFileValid === true) {
      console.log('PDF Viewer - iOS specific handling');
      console.log('PDF Viewer - Document URI:', document.uri);
      
      // iOS WebView has limited PDF support, so provide a better UX
      return (
        <View style={styles.pdfContainer}>
          <FileText size={64} color={colors.primary} />
          <Text style={styles.pdfTitle}>PDF Document</Text>
          <Text style={styles.pdfSubtitle}>
            {document.title}
          </Text>
          <Text style={styles.pdfInfo}>
            {document.fileSize ? `Size: ${(document.fileSize / 1024 / 1024).toFixed(2)} MB` : ''}
          </Text>
          <Text style={styles.pdfDescription}>
            Due to iOS limitations, PDFs cannot be displayed inline. 
            Tap the button below to open this PDF in your preferred app.
          </Text>
          <TouchableOpacity style={styles.openButton} onPress={handleOpenExternal}>
            <ExternalLink size={20} color={colors.background} />
            <Text style={styles.openButtonText}>Open in PDF Viewer</Text>
          </TouchableOpacity>
          
          {/* Additional actions */}
          <View style={styles.secondaryActions}>
            <TouchableOpacity style={styles.secondaryButton} onPress={handlePrint}>
              <Printer size={16} color={colors.primary} />
              <Text style={styles.secondaryButtonText}>Print</Text>
            </TouchableOpacity>
            {onShare && (
              <TouchableOpacity style={styles.secondaryButton} onPress={() => onShare(document)}>
                <Share size={16} color={colors.primary} />
                <Text style={styles.secondaryButtonText}>Share</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      );
    }
    
    // Android-specific handling
    if (Platform.OS === 'android' && document?.uri && pdfFileValid === true) {
      console.log('PDF Viewer - Android specific handling');
      console.log('PDF Viewer - Document URI:', document.uri);
      
      return (
        <View style={styles.webViewContainer}>
          <WebView
            source={{ uri: pdfDataUri || document.uri }}
            style={styles.webView}
            startInLoadingState={true}
            renderLoading={() => (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>Loading PDF...</Text>
              </View>
            )}
            onLoadStart={() => {
              console.log('PDF WebView - Load started');
              setWebViewError(null);
            }}
            onLoadEnd={() => {
              console.log('PDF WebView - Load ended successfully');
            }}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.error('PDF WebView error:', nativeEvent);
              setWebViewError(nativeEvent.description || 'Unknown error');
            }}
            originWhitelist={['*']}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            allowFileAccess={true}
            allowUniversalAccessFromFileURLs={true}
            mixedContentMode="always"
            scalesPageToFit={true}
          />
          
          {webViewError && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>Error: {webViewError}</Text>
              <Text style={styles.errorSubtext}>Try opening in external app</Text>
            </View>
          )}
          
          <View style={styles.pdfActions}>
            <TouchableOpacity style={styles.externalButton} onPress={handleOpenExternal}>
              <ExternalLink size={16} color={colors.primary} />
              <Text style={styles.externalButtonText}>Open External</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // Fallback for web or when WebView fails
    return (
      <View style={styles.pdfContainer}>
        <FileText size={64} color={colors.muted} />
        <Text style={styles.pdfTitle}>PDF Document</Text>
        <Text style={styles.pdfSubtitle}>
          {Platform.OS === 'web' 
            ? "PDF preview is not available on web. Download to view the document."
            : "Tap 'Open External' to share this PDF and open it in your preferred app"
          }
        </Text>
        <TouchableOpacity style={styles.openButton} onPress={handleOpenExternal}>
          <Text style={styles.openButtonText}>
            {Platform.OS === 'web' ? 'Download' : 'Open External'}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderUnsupportedFile = () => (
    <View style={styles.pdfContainer}>
      <FileText size={64} color={colors.muted} />
      <Text style={styles.pdfTitle}>Document</Text>
      <Text style={styles.pdfSubtitle}>
        This file type is not supported for preview. Tap &quot;Open External&quot; to share it and open in a compatible app.
      </Text>
      <TouchableOpacity style={styles.openButton} onPress={handleOpenExternal}>
        <Text style={styles.openButtonText}>Open External</Text>
      </TouchableOpacity>
    </View>
  );

  if (!visible || !document) return null;

  return (
    <Modal
      visible={visible}
      transparent={false}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerButton} onPress={onClose}>
            <X size={24} color={colors.text} />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle} numberOfLines={1}>
              {document.title}
            </Text>
            <Text style={styles.headerSubtitle}>
              {document.fileType} • {document.fileSize ? (document.fileSize / 1024 / 1024).toFixed(2) + " MB" : "Unknown size"}
            </Text>
          </View>
          <View style={styles.headerActions}>
            {(isTextFile || isJsonFile) && (
              <>
                {isEditing ? (
                  <TouchableOpacity
                    style={styles.headerButton}
                    onPress={handleSave}
                    disabled={isLoading}
                  >
                    <Save size={20} color={colors.primary} />
                  </TouchableOpacity>
                ) : (
                  <TouchableOpacity
                    style={styles.headerButton}
                    onPress={() => setIsEditing(true)}
                  >
                    <Edit size={20} color={colors.text} />
                  </TouchableOpacity>
                )}
              </>
            )}
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handlePrint}
            >
              <Printer size={20} color={colors.text} />
            </TouchableOpacity>
            {onShare && (
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => onShare(document)}
              >
                <Share size={20} color={colors.text} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Content */}
        <View style={styles.content}>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Loading document...</Text>
            </View>
          ) : (isTextFile || isJsonFile) ? (
            renderTextEditor()
          ) : isPdfFile ? (
            renderPdfViewer()
          ) : (
            renderUnsupportedFile()
          )}
        </View>

        {/* Edit Mode Footer */}
        {isEditing && (
          <View style={styles.editFooter}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                setIsEditing(false);
                setEditedContent(content);
              }}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSave}
              disabled={isLoading}
            >
              <Text style={styles.saveButtonText}>Save Changes</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
};

const createStyles = (colors: typeof Colors.light) =>
  StyleSheet.create({
    cancelButton: {
      alignItems: "center",
      backgroundColor: colors.muted + "20",
      borderRadius: 8,
      flex: 1,
      justifyContent: "center",
      marginRight: 8,
      paddingVertical: 12,
    },
    cancelButtonText: {
      color: colors.muted,
      fontSize: 16,
      fontWeight: "600",
    },
    container: {
      backgroundColor: colors.background,
      flex: 1,
    },
    content: {
      flex: 1,
      marginTop: 80,
    },
    contentContainer: {
      flex: 1,
      padding: 16,
    },
    editFooter: {
      backgroundColor: colors.card,
      borderTopColor: colors.border,
      borderTopWidth: 1,
      flexDirection: "row",
      padding: 16,
    },
    errorContainer: {
      alignItems: "center",
      backgroundColor: colors.danger + "10",
      borderColor: colors.danger + "30",
      borderRadius: 8,
      borderWidth: 1,
      margin: 16,
      padding: 16,
    },
    errorSubtext: {
      color: colors.muted,
      fontSize: 12,
      marginTop: 4,
      textAlign: "center",
    },
    errorText: {
      color: colors.danger,
      fontSize: 14,
      fontWeight: "500",
      textAlign: "center",
    },
    externalButton: {
      alignItems: "center",
      backgroundColor: colors.card,
      borderColor: colors.primary,
      borderRadius: 6,
      borderWidth: 1,
      flexDirection: "row",
      gap: 6,
      paddingHorizontal: 12,
      paddingVertical: 8,
    },
    externalButtonText: {
      color: colors.primary,
      fontSize: 14,
      fontWeight: "500",
    },
    header: {
      alignItems: "center",
      backgroundColor: colors.background,
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
      flexDirection: "row",
      paddingHorizontal: 16,
      paddingTop: 50,
      paddingVertical: 16,
      position: "absolute",
      top: 0,
      width: "100%",
      zIndex: 1,
    },
    headerActions: {
      alignItems: "center",
      flexDirection: "row",
      gap: 8,
    },
    headerButton: {
      alignItems: "center",
      borderRadius: 20,
      height: 40,
      justifyContent: "center",
      width: 40,
    },
    headerCenter: {
      alignItems: "center",
      flex: 1,
      marginHorizontal: 16,
    },
    headerSubtitle: {
      color: colors.muted,
      fontSize: 12,
      marginTop: 2,
    },
    headerTitle: {
      color: colors.text,
      fontSize: 16,
      fontWeight: "600",
    },
    loadingContainer: {
      alignItems: "center",
      flex: 1,
      justifyContent: "center",
    },
    loadingText: {
      color: colors.muted,
      fontSize: 16,
    },
    openButton: {
      alignItems: "center",
      backgroundColor: colors.primary,
      borderRadius: 8,
      flexDirection: "row",
      gap: 8,
      justifyContent: "center",
      marginTop: 20,
      paddingHorizontal: 24,
      paddingVertical: 12,
    },
    openButtonText: {
      color: colors.background,
      fontSize: 16,
      fontWeight: "600",
    },
    pdfActions: {
      alignItems: "center",
      backgroundColor: colors.background,
      borderTopColor: colors.border,
      borderTopWidth: 1,
      flexDirection: "row",
      justifyContent: "center",
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    pdfContainer: {
      alignItems: "center",
      flex: 1,
      justifyContent: "center",
      padding: 32,
    },
    pdfDescription: {
      color: colors.muted,
      fontSize: 14,
      marginBottom: 20,
      marginTop: 16,
      paddingHorizontal: 32,
      textAlign: "center",
    },
    pdfInfo: {
      color: colors.muted,
      fontSize: 14,
      marginTop: 4,
    },
    pdfSubtitle: {
      color: colors.muted,
      fontSize: 14,
      marginTop: 8,
      textAlign: "center",
    },
    pdfTitle: {
      color: colors.text,
      fontSize: 18,
      fontWeight: "600",
      marginTop: 16,
    },
    saveButton: {
      alignItems: "center",
      backgroundColor: colors.primary,
      borderRadius: 8,
      flex: 1,
      justifyContent: "center",
      marginLeft: 8,
      paddingVertical: 12,
    },
    saveButtonText: {
      color: colors.background,
      fontSize: 16,
      fontWeight: "600",
    },
    secondaryActions: {
      flexDirection: "row",
      gap: 16,
      marginTop: 20,
    },
    secondaryButton: {
      alignItems: "center",
      backgroundColor: colors.card,
      borderColor: colors.border,
      borderRadius: 6,
      borderWidth: 1,
      flexDirection: "row",
      gap: 6,
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    secondaryButtonText: {
      color: colors.primary,
      fontSize: 14,
      fontWeight: "500",
    },

    textInput: {
      color: colors.text,
      fontSize: 14,
      lineHeight: 20,
      minHeight: 400,
      textAlignVertical: "top",
    },
    textInputEditing: {
      backgroundColor: colors.card,
      borderColor: colors.primary,
      borderRadius: 8,
      borderWidth: 1,
      padding: 12,
    },
    webView: {
      backgroundColor: colors.background,
      flex: 1,
    },
    webViewContainer: {
      flex: 1,
    },
  }); 