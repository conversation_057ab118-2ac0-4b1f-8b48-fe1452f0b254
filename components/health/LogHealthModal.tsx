import React, { useState, useEffect, useCallback } from "react";
import {
  Modal,
  View,
  Text,
  TextInput,
  StyleSheet,
  Platform,
  TouchableOpacity,
  Alert,
  ScrollView,
} from "react-native";
import DateTimePicker, {
  DateTimePickerEvent,
} from "@react-native-community/datetimepicker";
import { format } from "date-fns";
import {
  saveUserHealthData,
  UserHealthData,
  loadUserHealthData,
} from "@/utils/dashboard/dashboard-utils";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { Bed, Droplet, Bike, X, CalendarDays, Pill } from "lucide-react-native";
import { useUserStore } from "@/store/user/user-store";
import { useTranslation } from "@/hooks/useTranslation";

interface LogHealthModalProps {
  visible: boolean;
  onClose: () => void;
  selectedDate?: Date;
  onDataSaved?: () => void;
}

const LogHealthModal: React.FC<LogHealthModalProps> = ({
  visible,
  onClose,
  selectedDate,
  onDataSaved,
}) => {
  const { currentTheme } = useTheme();
  const themeColors = currentTheme === "light" ? Colors.light : Colors.dark;
  const { profile, addHealthMetric, editHealthMetric } = useUserStore();
  const { t } = useTranslation();
  const [date, setDate] = useState(selectedDate || new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [sleep, setSleep] = useState(""); // hours
  const [pills, setPills] = useState(""); // number of pills
  const [hydration, setHydration] = useState(""); // glasses
  const [exercise, setExercise] = useState(""); // minutes

  // Get goals from user profile instead of hardcoded values
  const getGoalsFromProfile = () => {
    const defaultGoals = {
      sleep: 8,
      pills: 2,
      hydration: 8,
      exercise: 30,
    };
    const defaultUnits = {
      sleep: "hours",
      pills: "pills",
      hydration: "glasses", // Changed back to "glasses" as user prefers
      exercise: "minutes",
    };
    if (!profile?.healthGoals) {
      return { goals: defaultGoals, units: defaultUnits };
    }
    const goals = { ...defaultGoals };
    const units = { ...defaultUnits };
    profile.healthGoals.forEach((goal) => {
      if (goal.enabled) {
        // Check if enabled, target can be null
        switch (goal.id) {
          case "sleep":
            goals.sleep = goal.target ?? defaultGoals.sleep;
            units.sleep = goal.unit || "hours";
            break;
          case "pills":
            goals.pills = goal.target ?? defaultGoals.pills;
            units.pills = goal.unit || "pills";
            break;
          case "waterIntake":
            goals.hydration = goal.target ?? defaultGoals.hydration;
            units.hydration = "glasses"; // Force to glasses regardless of onboarding setting
            break;
          case "exercise":
            goals.exercise = goal.target ?? defaultGoals.exercise;
            units.exercise = goal.unit || "minutes";
            break;
        }
      }
    });
    return { goals, units };
  };
  const { goals, units } = getGoalsFromProfile();

  const loadDataForDate = useCallback(async (date: Date) => {
    try {
      const dateStr = date.toISOString().split('T')[0];
      
      // Load existing data for this date
      const existingData = await loadUserHealthData(dateStr);
      
      if (existingData) {
        setSleep(existingData.sleep?.toString() || '');
        setPills(existingData.pills?.toString() || '');
        setHydration(existingData.hydration?.toString() || '');
        setExercise(existingData.exercise?.toString() || '');
      } else {
        // Reset to defaults
        setSleep('');
        setPills('');
        setHydration('');
        setExercise('');
      }
    } catch (error) {
      console.error('Error loading health data:', error);
    }
  }, []);

  useEffect(() => {
    if (visible) {
      const initialDate = selectedDate || new Date();
      setDate(initialDate);
      loadDataForDate(initialDate);
    }
  }, [visible, selectedDate, loadDataForDate]);

  const handleDateChange = (event: DateTimePickerEvent, newDate?: Date) => {
    setShowDatePicker(Platform.OS === "ios");
    if (newDate) {
      setDate(newDate);
      loadDataForDate(newDate);
    }
  };

  const handleSave = async () => {
    const dateString = format(date, "yyyy-MM-dd");
    const healthData: UserHealthData = {
      sleep: sleep ? parseFloat(sleep) : undefined,
      pills: pills ? parseFloat(pills) : undefined,
      hydration: hydration ? parseFloat(hydration) : undefined,
      exercise: exercise ? parseFloat(exercise) : undefined,
    };
    // Save to AsyncStorage (for dashboard)
    await saveUserHealthData(dateString, healthData);
    // Save to user store (for progress tabs)
    // Check if health metrics already exist for this date and update/add accordingly
    const existingMetrics = profile?.healthMetrics || [];
    // Helper function to save or update a metric
    const saveMetricToStore = (
      metricType: string,
      value: number | undefined,
      unit: string
    ) => {
      if (value === undefined) return;
      const existingMetric = existingMetrics.find(
        (metric) =>
          metric.date === dateString && metric.metric === metricType
      );
      if (existingMetric) {
        // Update existing metric
        editHealthMetric(existingMetric.id, {
          name: metricType,
          value: value,
          unit: unit,
          date: dateString,
          metric: metricType,
          notes: "",
        });
      } else {
        // Add new metric
        addHealthMetric({
          name: metricType,
          value: value,
          unit: unit,
          date: dateString,
          metric: metricType,
          notes: "",
        });
      }
    };
    // Save each metric type
    saveMetricToStore("sleep", healthData.sleep, "hours");
    saveMetricToStore("pills", healthData.pills, "pills");
    saveMetricToStore("hydration", healthData.hydration, "glasses");
    saveMetricToStore("exercise", healthData.exercise, "minutes");
    Alert.alert(t('common.success'), t('health.modal.saveSuccessMessage'));
    // Call the callback to refresh dashboard data
    if (onDataSaved) {
      onDataSaved();
    }
    onClose();
  };

  const renderMetricItem = (
    IconComponent: React.ElementType,
    iconBgColor: string,
    metricName: string,
    currentValue: string,
    setValue: (text: string) => void,
    unit: string,
    goal: number,
    goalUnitText: string, // e.g., "/9h", "/10/10"
    progressColor: string,
    keyboardType: "numeric" | "default" = "numeric"
  ) => {
    const numericValue = parseFloat(currentValue) || 0;
    const progress = goal > 0 ? Math.min(numericValue / goal, 1) * 100 : 0;
    return (
      <View style={styles.metricItemContainer}>
        <View
          style={[
            styles.metricIconOuterContainer,
            { backgroundColor: iconBgColor },
          ]}
        >
          <IconComponent color={progressColor} size={24} />
        </View>
        <View style={styles.metricDetailsContainer}>
          <Text style={[styles.metricName, { color: themeColors.text }]}>
            {metricName}
          </Text>
          <View style={styles.metricInputRow}>
            <TextInput
              style={[
                styles.metricInputBox,
                {
                  borderColor: themeColors.border,
                  color: themeColors.text,
                  backgroundColor: themeColors.background,
                },
              ]} // Use background for input box
              value={currentValue}
              onChangeText={setValue}
              keyboardType={keyboardType}
              maxLength={
                metricName === "Energy" || metricName === "Pills" ? 2 : 4
              } // Limit 0-10 to 2 digits
            />
            <Text
              style={[
                styles.metricUnitText,
                { color: themeColors.textSecondary },
              ]}
            >
              {unit}{" "}
              <Text style={{ color: themeColors.textTertiary }}>
                {goalUnitText}
              </Text>
            </Text>
          </View>
          <View
            style={[
              styles.progressBarBackground,
              { backgroundColor: themeColors.border },
            ]}
          >
            <View
              style={[
                styles.progressBarFill,
                { width: `${progress}%`, backgroundColor: progressColor },
              ]}
            />
          </View>
        </View>
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View
          style={[styles.modalContainer, { backgroundColor: themeColors.card }]}
        >
          <View style={styles.headerContainer}>
            <Text style={[styles.title, { color: themeColors.text }]}>
              {t('health.modal.title')}
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={themeColors.text} />
            </TouchableOpacity>
          </View>
          {/* Date picker - keeping it simple for now */}
          <TouchableOpacity
            onPress={() => setShowDatePicker(true)}
            style={[styles.dateDisplay, { borderColor: themeColors.border }]}
          >
            <CalendarDays
              size={18}
              color={themeColors.textSecondary}
              style={styles.calendarIcon}
            />
            <Text
              style={[styles.dateText, { color: themeColors.textSecondary }]}
            >
              {format(date, "MMMM dd, yyyy")}
            </Text>
          </TouchableOpacity>
          {showDatePicker && (
            <DateTimePicker
              value={date}
              mode="date"
              display={Platform.OS === "ios" ? "spinner" : "default"}
              onChange={handleDateChange}
              maximumDate={new Date()}
            />
          )}
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollViewContent}
            showsVerticalScrollIndicator={false}
          >
            {renderMetricItem(
              Bed,
              themeColors.primaryLight,
              t('health.modal.sleep'),
              sleep,
              setSleep,
              units.sleep,
              goals.sleep,
              `/ ${goals.sleep} ${units.sleep}`,
              themeColors.primary
            )}
            {renderMetricItem(
              Pill,
              themeColors.dangerLight,
              t('health.modal.pills'),
              pills,
              setPills,
              units.pills,
              goals.pills,
              `/ ${goals.pills} ${units.pills}`,
              themeColors.danger
            )}
            {renderMetricItem(
              Droplet,
              themeColors.infoLight,
              t('health.modal.hydration'),
              hydration,
              setHydration,
              units.hydration,
              goals.hydration,
              `/ ${goals.hydration} ${units.hydration}`,
              themeColors.info
            )}
            {renderMetricItem(
              Bike,
              themeColors.successLight,
              t('health.modal.exercise'),
              exercise,
              setExercise,
              units.exercise,
              goals.exercise,
              `/ ${goals.exercise} ${units.exercise}`,
              themeColors.success
            )}
          </ScrollView>
          <TouchableOpacity
            onPress={handleSave}
            style={[
              styles.saveButton,
              { backgroundColor: themeColors.primary },
            ]}
          >
            <Text style={[styles.saveButtonText, { color: themeColors.card }]}>
              {t('common.save')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  calendarIcon: {
    marginRight: 8,
  },
  closeButton: {
    padding: 4, // Smaller touch target for 'X'
  },
  dateDisplay: {
    alignItems: "center",
    alignSelf: "flex-start",
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: "row",
    marginBottom: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  dateText: {
    fontSize: 14,
    fontWeight: "500",
  },
  headerContainer: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
    width: "100%",
  },
  metricDetailsContainer: {
    flex: 1,
  },
  metricIconOuterContainer: {
    alignItems: "center",
    borderRadius: 24, // Circular
    height: 48,
    justifyContent: "center",
    marginRight: 15,
    width: 48,
  },
  metricInputBox: {
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
    fontWeight: "500",
    height: 40,
    marginRight: 10,
    paddingVertical: 0, // Adjust padding for centering text
    textAlign: "center",
    width: 60,
  },
  metricInputRow: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: 8,
  },
  metricItemContainer: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: 20,
    width: "100%",
  },
  metricName: {
    fontSize: 17, // Larger metric name
    fontWeight: "600",
    marginBottom: 8,
  },
  metricUnitText: {
    flexShrink: 1,
    fontSize: 14, // Allow text to shrink if needed
  },
  modalContainer: {
    alignItems: "center",
    borderRadius: 20, // More rounded
    elevation: 8,
    maxHeight: "90%",
    paddingHorizontal: 20,
    paddingVertical: 25,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    width: "90%",
  },
  modalOverlay: {
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    flex: 1,
    justifyContent: "center", // Darker overlay
  },
  progressBarBackground: {
    borderRadius: 4,
    height: 8, // Thicker progress bar
    overflow: "hidden",
    width: "100%",
  },
  progressBarFill: {
    borderRadius: 4,
    height: "100%",
  },
  saveButton: {
    alignItems: "center",
    borderRadius: 12,
    justifyContent: "center",
    marginTop: 15,
    paddingVertical: 16,
    width: "100%", // Space above save button
  },
  saveButtonText: {
    fontSize: 18,
    fontWeight: "bold",
  },
  scrollView: {
    width: "100%",
  },
  scrollViewContent: {
    paddingBottom: 10, // Space for last item's shadow/margin
  },
  title: {
    fontSize: 22,
    fontWeight: "600",
  },
});

export default LogHealthModal;