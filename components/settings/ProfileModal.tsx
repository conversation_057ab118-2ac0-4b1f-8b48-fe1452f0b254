import React from "react";
import { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Image,
  Platform,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { Camera, Upload, Calendar } from 'lucide-react-native';
import { useUserStore } from '@/store/user/user-store';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  ModalHeader, 
  ButtonGroup,
  FormInput
} from '@/components/shared';
import {
  SettingsColorScheme,
  triggerHapticFeedback,
  validateEmail,
  validatePhone,
  validateRequired
} from '@/utils';
import { useTranslation } from '@/hooks/useTranslation';
interface ProfileModalProps {
  visible: boolean;
  onClose: () => void;
  colors: SettingsColorScheme;
  language: 'en' | 'nl';
}
export const ProfileModal: React.FC<ProfileModalProps> = ({
  visible,
  onClose,
  colors,
  language
}) => {
  const { profile, updateProfile } = useUserStore();
  const { t } = useTranslation();
  
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [country, setCountry] = useState('');
  const [birthday, setBirthday] = useState<Date | null>(null);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Initialize form data when modal becomes visible or profile changes
  useEffect(() => {
    if (visible && profile) {
      console.log("Setting profile data in modal:", profile.name);
      setName(profile.name || '');
      setEmail(profile.email || '');
      setPhone(profile.phone || '');
      setCountry(profile.country || '');
      setBirthday(profile.birthday ? new Date(profile.birthday) : null);
      setProfileImage(profile.profileImage || null);
    }
  }, [visible, profile]);
  
  const handleSave = () => {
    if (!profile) {
      console.error("No profile to update");
      return;
    }
    
    if (!validateRequired(name)) {
      Alert.alert(
        t('common.error'),
        language === 'nl' ? 'Naam is verplicht' : 'Name is required'
      );
      return;
    }
    if (email && !validateEmail(email)) {
      Alert.alert(
        t('common.error'),
        language === 'nl' ? 'Voer een geldig e-mailadres in' : 'Please enter a valid email address'
      );
      return;
    }
    if (phone && !validatePhone(phone)) {
      Alert.alert(
        t('common.error'),
        language === 'nl' ? 'Voer een geldig telefoonnummer in' : 'Please enter a valid phone number'
      );
      return;
    }
    
    const updates = {
      name,
      email,
      phone,
      country,
      birthday: birthday ? birthday.toISOString() : undefined,
      profileImage
    };
    
    console.log("Updating profile with:", updates);
    updateProfile(updates);
    
    triggerHapticFeedback('notification');
    onClose();
  };
  
  const handleTakePicture = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          language === 'nl' ? 'Toestemming nodig' : 'Permission needed',
          language === 'nl' 
            ? 'We hebben toestemming nodig om toegang te krijgen tot je camera'
            : 'We need permission to access your camera'
        );
        return;
      }
      
      setIsLoading(true);
      
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
        mediaTypes: ['images']
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const uri = result.assets[0].uri;
        const base64 = await FileSystem.readAsStringAsync(uri, {
          encoding: FileSystem.EncodingType.Base64
        });
        setProfileImage(`data:image/jpeg;base64,${base64}`);
      }
    } catch (_error) {
      console.error('Error taking picture:', _error);
      Alert.alert(
        t('common.error'),
        language === 'nl' 
          ? 'Er is een fout opgetreden bij het maken van de foto'
          : 'An error occurred while taking the picture'
      );
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleChooseFromLibrary = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          language === 'nl' ? 'Toestemming nodig' : 'Permission needed',
          language === 'nl' 
            ? 'We hebben toestemming nodig om toegang te krijgen tot je fotobibliotheek'
            : 'We need permission to access your photo library'
        );
        return;
      }
      
      setIsLoading(true);
      
      const result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
        mediaTypes: ['images']
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const uri = result.assets[0].uri;
        const base64 = await FileSystem.readAsStringAsync(uri, {
          encoding: FileSystem.EncodingType.Base64
        });
        setProfileImage(`data:image/jpeg;base64,${base64}`);
      }
    } catch (_error) {
      console.error('Error choosing image:', _error);
      Alert.alert(
        t('common.error'),
        language === 'nl' 
          ? 'Er is een fout opgetreden bij het kiezen van de afbeelding'
          : 'An error occurred while choosing the image'
      );
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleRemovePhoto = () => {
    setProfileImage(null);
    triggerHapticFeedback('impact');
  };
  
  const onDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setBirthday(selectedDate);
    }
  };
  
  if (!profile) {
    return null;
  }
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
      presentationStyle="overFullScreen"
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <LinearGradient
            colors={[colors.card, colors.cardGradient || colors.background]}
            style={styles.modalGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          >
            <ModalHeader
            title={language === 'nl' ? 'Bewerk Profiel' : 'Edit Profile'}
            onClose={onClose}
            colors={colors}
          />
      
      <ScrollView 
        style={styles.scrollView} 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
      >
        <View style={styles.photoContainer}>
          {isLoading ? (
            <View style={[styles.photoPlaceholder, { backgroundColor: colors.card }]}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          ) : profileImage ? (
            <Image source={{ uri: profileImage }} style={styles.profileImage} />
          ) : (
            <View style={[styles.photoPlaceholder, { backgroundColor: colors.card }]}>
              <Text style={[styles.photoPlaceholderText, { color: colors.muted }]}>
                {name.charAt(0).toUpperCase() || '?'}
              </Text>
            </View>
          )}
          
          <View style={styles.photoActions}>
            <TouchableOpacity 
              style={[styles.photoButton, { backgroundColor: colors.primary }]}
              onPress={handleTakePicture}
            >
              <LinearGradient
                colors={[colors.primary, colors.primaryDark || colors.primary]}
                style={styles.photoButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Camera size={24} color="#fff" strokeWidth={2.5} />
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.photoButton, { backgroundColor: colors.success }]}
              onPress={handleChooseFromLibrary}
            >
              <LinearGradient
                colors={[colors.success || colors.primary, colors.successDark || colors.success || colors.primary]}
                style={styles.photoButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Upload size={24} color="#fff" strokeWidth={2.5} />
              </LinearGradient>
            </TouchableOpacity>
            
            {profileImage && (
              <TouchableOpacity 
                style={[styles.photoButton, { backgroundColor: colors.danger }]}
                onPress={handleRemovePhoto}
              >
                <LinearGradient
                  colors={[colors.danger || colors.primary, colors.dangerDark || colors.danger || colors.primary]}
                  style={styles.photoButtonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                >
                  <Text style={styles.removeText}>×</Text>
                </LinearGradient>
              </TouchableOpacity>
            )}
          </View>
        </View>
        
        <View style={styles.formContainer}>
          <FormInput
            label={language === 'nl' ? 'Naam' : 'Name'}
            value={name}
            onChangeText={setName}
            placeholder={language === 'nl' ? 'Jouw naam' : 'Your name'}
            colors={{
              card: colors.card,
              background: colors.background,
              text: colors.text,
              border: colors.border,
              muted: colors.muted,
              danger: colors.danger || colors.primary,
              primary: colors.primary,
            }}
            required
          />
          
          <FormInput
            label={language === 'nl' ? 'E-mail' : 'Email'}
            value={email}
            onChangeText={setEmail}
            placeholder={language === 'nl' ? '<EMAIL>' : '<EMAIL>'}
            colors={{
              card: colors.card,
              background: colors.background,
              text: colors.text,
              border: colors.border,
              muted: colors.muted,
              danger: colors.danger || colors.primary,
              primary: colors.primary,
            }}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          
          <FormInput
            label={language === 'nl' ? 'Telefoonnummer' : 'Phone Number'}
            value={phone}
            onChangeText={setPhone}
            placeholder={language === 'nl' ? 'Jouw telefoonnummer' : 'Your phone number'}
            colors={{
              card: colors.card,
              background: colors.background,
              text: colors.text,
              border: colors.border,
              muted: colors.muted,
              danger: colors.danger || colors.primary,
              primary: colors.primary,
            }}
            keyboardType="phone-pad"
          />
          
          <FormInput
            label={language === 'nl' ? 'Land' : 'Country'}
            value={country}
            onChangeText={setCountry}
            placeholder={language === 'nl' ? 'Jouw land' : 'Your country'}
            colors={{
              card: colors.card,
              background: colors.background,
              text: colors.text,
              border: colors.border,
              muted: colors.muted,
              danger: colors.danger || colors.primary,
              primary: colors.primary,
            }}
          />
          
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {language === 'nl' ? 'Geboortedatum' : 'Birthday'}
            </Text>
            
            <TouchableOpacity
              style={[
                styles.dateButton,
                { 
                  backgroundColor: colors.card,
                  borderColor: colors.border
                }
              ]}
              onPress={() => setShowDatePicker(true)}
            >
              <Text 
                style={[
                  styles.dateButtonText, 
                  { color: birthday ? colors.text : colors.muted }
                ]}
              >
                {birthday 
                  ? birthday.toLocaleDateString(language === 'nl' ? 'nl-NL' : 'en-US')
                  : language === 'nl' ? 'Selecteer geboortedatum' : 'Select birthday'}
              </Text>
              <Calendar size={22} color={colors.primary} strokeWidth={2.5} />
            </TouchableOpacity>
            
            {showDatePicker && (
              <DateTimePicker
                value={birthday || new Date()}
                mode="date"
                display={Platform.OS === "ios" ? "spinner" : "default"}
                onChange={onDateChange}
                maximumDate={new Date()}
              />
            )}
          </View>
        </View>
      </ScrollView>
      
            <ButtonGroup
              cancelText={t('common.cancel')}
              submitText={t('common.save')}
              onCancel={onClose}
              onSubmit={handleSave}
              colors={{
                primary: colors.primary,
                text: colors.text,
                border: colors.border,
              }}
            />
          </LinearGradient>
        </View>
      </View>
    </Modal>
  );
};
const styles = StyleSheet.create({


  dateButton: {
    alignItems: 'center',
    borderRadius: 16,
    borderWidth: 2,
    elevation: 2,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 18,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  dateButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  formContainer: {
    paddingHorizontal: 24,
    paddingVertical: 8,
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 17,
    fontWeight: '600',
    letterSpacing: -0.2,
    marginBottom: 8,
  },
  modalContent: {
    borderRadius: 24,
    elevation: 20,
    height: '90%',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    width: '100%',
  },
  modalGradient: {
    flex: 1,
  },
  modalOverlay: {
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    flex: 1,
    justifyContent: 'center',
    padding: 16,
  },
  photoActions: {
    flexDirection: 'row',
    gap: 16,
    justifyContent: 'center',
  },
  photoButton: {
    borderRadius: 28,
    elevation: 6,
    height: 56,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    width: 56,
  },
  photoButtonGradient: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  photoContainer: {
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 24,
  },
  photoPlaceholder: {
    alignItems: 'center',
    borderRadius: 70,
    elevation: 8,
    height: 140,
    justifyContent: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    width: 140,
  },
  photoPlaceholderText: {
    fontSize: 48,
    fontWeight: 'bold',
  },
  profileImage: {
    borderRadius: 70,
    elevation: 8,
    height: 140,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    width: 140,
  },
  removeText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
    minHeight: 200,
  },
  scrollViewContent: {
    paddingBottom: 20,
  },


});