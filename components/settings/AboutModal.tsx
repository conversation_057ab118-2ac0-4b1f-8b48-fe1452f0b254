import React from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Linking, Modal } from 'react-native';
import { Globe, Mail, Shield, Heart, X, MessageCircle, Phone } from 'lucide-react-native';
import { UserProfile } from "@/types/user";
import { useTranslation } from '@/hooks/useTranslation';

interface SettingsColorScheme {
  background: string;
  card: string;
  border: string;
  text: string;
  primary: string;
  muted: string;
  success?: string;
  info?: string;
  warning?: string;
}

interface AboutModalProps {
  visible: boolean;
  onClose: () => void;
  profile: UserProfile;
  colors: SettingsColorScheme;
}

export const AboutModal: React.FC<AboutModalProps> = ({
  visible,
  onClose,
  profile: _profile, // Keep for backward compatibility but don't use
  colors,
}) => {
  const { t } = useTranslation();
  const appVersion = '1.0.0';
  const contactEmail = '<EMAIL>';
  const contactPhone = '******-SOBRIX';
  
  const openLink = (url: string) => {
    Linking.openURL(url);
  };
  
  const openEmail = () => {
    Linking.openURL(`mailto:${contactEmail}`);
  };
  
  const openPhone = () => {
    Linking.openURL(`tel:${contactPhone}`);
  };
  
  const openChat = () => {
    Linking.openURL('https://sobrixhealth.com/chat');
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
          {/* Header */}
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {t('settings.aboutModal.title')}
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={colors.text} strokeWidth={2.5} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
            <View style={styles.appInfo}>
              <View style={[styles.logoContainer, { backgroundColor: colors.primary }]}>
                <Text style={styles.logoText}>SH</Text>
              </View>
              
              <Text style={[styles.appName, { color: colors.text }]}>SobrixHealth</Text>
              <Text style={[styles.appVersion, { color: colors.muted }]}>Version {appVersion}</Text>
              
              <Text style={[styles.appDescription, { color: colors.text }]}>
                {t('settings.aboutModal.appDescription')}
              </Text>
            </View>
            
            <View style={styles.featuresSection}>
              <Text style={[styles.sectionTitle, { color: colors.primary }]}>
                {t('settings.aboutModal.features')}
              </Text>
              
              <View style={styles.featuresList}>
                <View style={styles.featureItem}>
                  <View style={[styles.featureIcon, { backgroundColor: (colors.success || colors.primary) + '20' }]}>
                    <Heart size={20} color={colors.success || colors.primary} />
                  </View>
                  <View style={styles.featureContent}>
                    <Text style={[styles.featureTitle, { color: colors.text }]}>
                      {t('settings.aboutModal.sobrietyTracker')}
                    </Text>
                    <Text style={[styles.featureDescription, { color: colors.muted }]}>
                      {t('settings.aboutModal.sobrietyTrackerDesc')}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.featureItem}>
                  <View style={[styles.featureIcon, { backgroundColor: (colors.info || colors.primary) + '20' }]}>
                    <Globe size={20} color={colors.info || colors.primary} />
                  </View>
                  <View style={styles.featureContent}>
                    <Text style={[styles.featureTitle, { color: colors.text }]}>
                      {t('settings.aboutModal.mindfulness')}
                    </Text>
                    <Text style={[styles.featureDescription, { color: colors.muted }]}>
                      {t('settings.aboutModal.mindfulnessDesc')}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.featureItem}>
                  <View style={[styles.featureIcon, { backgroundColor: (colors.warning || colors.primary) + '20' }]}>
                    <Shield size={20} color={colors.warning || colors.primary} />
                  </View>
                  <View style={styles.featureContent}>
                    <Text style={[styles.featureTitle, { color: colors.text }]}>
                      {t('settings.aboutModal.crisisSupport')}
                    </Text>
                    <Text style={[styles.featureDescription, { color: colors.muted }]}>
                      {t('settings.aboutModal.crisisSupportDesc')}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
            
            <View style={styles.supportSection}>
              <Text style={[styles.sectionTitle, { color: colors.primary }]}>
                {t('settings.aboutModal.helpSupport')}
              </Text>
              
              <Text style={[styles.sectionDescription, { color: colors.text }]}>
                {t('settings.aboutModal.helpDescription')}
              </Text>
              
              <TouchableOpacity 
                style={[styles.supportOption, { backgroundColor: colors.primary + '10', borderColor: colors.primary + '30' }]}
                onPress={openEmail}
              >
                <View style={[styles.iconContainer, { backgroundColor: colors.primary }]}>
                  <Mail size={20} color="#fff" />
                </View>
                <View style={styles.optionContent}>
                  <Text style={[styles.optionTitle, { color: colors.text }]}>
                    {t('settings.aboutModal.email')}
                  </Text>
                  <Text style={[styles.optionDescription, { color: colors.muted }]}>
                    {contactEmail}
                  </Text>
                </View>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.supportOption, { backgroundColor: colors.primary + '10', borderColor: colors.primary + '30' }]}
                onPress={openPhone}
              >
                <View style={[styles.iconContainer, { backgroundColor: colors.primary }]}>
                  <Phone size={20} color="#fff" />
                </View>
                <View style={styles.optionContent}>
                  <Text style={[styles.optionTitle, { color: colors.text }]}>
                    {t('settings.aboutModal.phone')}
                  </Text>
                  <Text style={[styles.optionDescription, { color: colors.muted }]}>
                    {contactPhone}
                  </Text>
                </View>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.supportOption, { backgroundColor: colors.primary + '10', borderColor: colors.primary + '30' }]}
                onPress={openChat}
              >
                <View style={[styles.iconContainer, { backgroundColor: colors.primary }]}>
                  <MessageCircle size={20} color="#fff" />
                </View>
                <View style={styles.optionContent}>
                  <Text style={[styles.optionTitle, { color: colors.text }]}>
                    {t('settings.aboutModal.liveChat')}
                  </Text>
                  <Text style={[styles.optionDescription, { color: colors.muted }]}>
                    {t('settings.aboutModal.available247')}
                  </Text>
                </View>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.linkItem, { backgroundColor: colors.card, borderColor: colors.border }]}
                onPress={() => openLink('https://sobrixhealth.com')}
              >
                <Globe size={20} color={colors.primary} />
                <Text style={[styles.linkText, { color: colors.text }]}>
                  {t('settings.aboutModal.visitWebsite')}
                </Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.faqSection}>
              <Text style={[styles.sectionTitle, { color: colors.primary }]}>
                {t('settings.aboutModal.faq')}
              </Text>
              
              <View style={styles.faqItem}>
                <Text style={[styles.faqQuestion, { color: colors.text }]}>
                  {t('settings.resetPassword')}
                </Text>
                <Text style={[styles.faqAnswer, { color: colors.muted }]}>
                  {t('settings.resetPasswordAnswer')}
                </Text>
              </View>
              
              <View style={styles.faqItem}>
                <Text style={[styles.faqQuestion, { color: colors.text }]}>
                  {t('settings.exportData')}
                </Text>
                <Text style={[styles.faqAnswer, { color: colors.muted }]}>
                  {t('settings.exportDataAnswer')}
                </Text>
              </View>
              
              <View style={styles.faqItem}>
                <Text style={[styles.faqQuestion, { color: colors.text }]}>
                  {t('settings.dataSafety')}
                </Text>
                <Text style={[styles.faqAnswer, { color: colors.muted }]}>
                  {t('settings.dataSafetyAnswer')}
                </Text>
              </View>
            </View>
            
            <View style={styles.copyrightSection}>
              <Text style={[styles.copyrightText, { color: colors.muted }]}>
                © 2024 SobrixHealth. {t('settings.aboutModal.copyright')}
              </Text>
              <Text style={[styles.copyrightText, { color: colors.muted }]}>
                {t('settings.aboutModal.madeWith')}
              </Text>
            </View>
          </ScrollView>
          
          {/* Footer */}
          <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.closeFooterButton, { backgroundColor: colors.primary }]}
              onPress={onClose}
            >
              <Text style={styles.closeFooterButtonText}>
                {t('common.close')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  appDescription: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
  appInfo: {
    alignItems: 'center',
    marginBottom: 24,
  },
  appName: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  appVersion: {
    fontSize: 16,
    marginBottom: 8,
  },
  closeButton: {
    padding: 4,
  },
  closeFooterButton: {
    alignItems: 'center',
    backgroundColor: '#007AFF',
    borderRadius: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  closeFooterButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  copyrightSection: {
    alignItems: 'center',
    paddingTop: 16,
  },
  copyrightText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
    textAlign: 'center',
  },
  faqAnswer: {
    fontSize: 14,
    lineHeight: 20,
  },
  faqItem: {
    marginBottom: 20,
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  faqSection: {
    marginBottom: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  featureIcon: {
    alignItems: 'center',
    borderRadius: 12,
    height: 40,
    justifyContent: 'center',
    marginRight: 16,
    width: 40,
  },
  featureItem: {
    alignItems: 'flex-start',
    flexDirection: 'row',
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  featuresList: {
    gap: 16,
  },
  featuresSection: {
    marginBottom: 32,
  },
  iconContainer: {
    alignItems: 'center',
    borderRadius: 20,
    height: 40,
    justifyContent: 'center',
    marginRight: 16,
    width: 40,
  },
  linkItem: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 12,
    padding: 16,
  },
  linkText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  logoContainer: {
    alignItems: 'center',
    borderRadius: 30,
    height: 120,
    justifyContent: 'center',
    marginBottom: 20,
    width: 120,
  },
  logoText: {
    color: '#fff',
    fontSize: 48,
    fontWeight: '900',
    letterSpacing: -2,
  },
  modalBody: {
    maxHeight: 500,
    padding: 20,
  },
  modalContent: {
    borderRadius: 24,
    elevation: 8,
    maxHeight: '85%',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    width: '100%',
  },
  modalFooter: {
    borderTopWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 16,
  },
  modalHeader: {
    alignItems: 'center',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
  },
  modalOverlay: {
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  optionContent: {
    flex: 1,
  },
  optionDescription: {
    fontSize: 14,
    marginBottom: 4,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  supportOption: {
    alignItems: 'center',
    borderRadius: 16,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 16,
    padding: 16,
  },
  supportSection: {
    marginBottom: 32,
  },
});