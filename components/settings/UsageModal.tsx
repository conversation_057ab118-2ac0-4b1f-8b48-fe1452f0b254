import React from "react";
import { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Alert,
  Modal,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { DollarSign, Euro, X, Save } from 'lucide-react-native';
import { useUserStore } from '@/store/user/user-store';
import { UserProfile } from '@/types/user';
import { 
  modalStyles, 
  modalButtonStyles, 
  formStyles, 
  selectionStyles 
} from '@/style';
interface SettingsColorScheme {
  background: string;
  card: string;
  border: string;
  text: string;
  primary: string;
  muted: string;
}
interface UsageModalProps {
  visible: boolean;
  onClose: () => void;
  profile: UserProfile;
  colors: SettingsColorScheme;
}
// Usage units for different substance types
const usageUnits = {
  Alcohol: [
    { id: 'drinks', label: { en: 'Drinks', nl: 'Drankjes' } },
    { id: 'units', label: { en: 'Units', nl: 'Eenheden' } },
    { id: 'bottles', label: { en: 'Bottles', nl: 'Flessen' } },
  ],
  Cigarettes: [
    { id: 'cigarettes', label: { en: 'Cigarettes', nl: 'Sigaretten' } },
    { id: 'packs', label: { en: 'Packs', nl: 'Pakjes' } },
  ],
  Cannabis: [
    { id: 'joints', label: { en: 'Joints', nl: 'Joints' } },
    { id: 'grams', label: { en: 'Grams', nl: 'Gram' } },
  ],
  Other: [
    { id: 'times', label: { en: 'Times', nl: 'Keer' } },
    { id: 'doses', label: { en: 'Doses', nl: 'Doses' } },
  ],
};
export const UsageModal: React.FC<UsageModalProps> = ({
  visible,
  onClose,
  profile,
  colors,
}) => {
  const { updateProfile } = useUserStore();
  const language = (profile.language || 'en') as 'en' | 'nl';
  
  const [selectedCurrency, setSelectedCurrency] = useState(profile.currency || 'EUR');
  const [selectedUnit, setSelectedUnit] = useState(profile.usageUnit || 'times');
  const [usageQuantity, setUsageQuantity] = useState(profile.usageQuantity?.toString() || '');
  const [usageCost, setUsageCost] = useState(profile.usageCost?.toString() || '');
  
  // Load values when modal opens
  useEffect(() => {
    if (visible) {
      setSelectedCurrency(profile.currency || 'EUR');
      setSelectedUnit(profile.usageUnit || 'times');
      setUsageQuantity(profile.usageQuantity?.toString() || '');
      setUsageCost(profile.usageCost?.toString() || '');
    }
  }, [visible, profile]);
  
  // Get available units for the selected addiction
  const getUnitsForAddiction = () => {
    const substanceType = profile.substanceType || profile.addiction;
    if (!substanceType) return usageUnits.Other;
    
    // Check if the substance type matches any of our predefined categories
    for (const [category, units] of Object.entries(usageUnits)) {
      if (substanceType.includes(category)) {
        return units;
      }
    }
    
    return usageUnits.Other;
  };
  const availableUnits = getUnitsForAddiction();
  const handleSave = () => {
    const quantity = parseFloat(usageQuantity);
    const cost = parseFloat(usageCost);
    
    // Validate inputs
    if (usageQuantity && isNaN(quantity)) {
      Alert.alert(
        language === 'nl' ? 'Fout' : 'Error',
        language === 'nl' 
          ? 'Voer een geldige hoeveelheid in' 
          : 'Please enter a valid quantity'
      );
      return;
    }
    
    if (usageCost && isNaN(cost)) {
      Alert.alert(
        language === 'nl' ? 'Fout' : 'Error',
        language === 'nl' 
          ? 'Voer geldige kosten in' 
          : 'Please enter a valid cost'
      );
      return;
    }
    
    updateProfile({ 
      currency: selectedCurrency,
      usageUnit: selectedUnit,
      usageQuantity: usageQuantity ? quantity : undefined,
      usageCost: usageCost ? cost : undefined,
    });
    onClose();
  };
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={modalStyles.modalOverlay}>
        <View style={[modalStyles.modalContent, { backgroundColor: colors.card }]}>
          {/* Header */}
          <View style={[modalStyles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[modalStyles.modalTitle, { color: colors.text }]}>
              {language === 'nl' ? 'Gebruiksgegevens' : 'Usage Details'}
            </Text>
            <TouchableOpacity onPress={onClose} style={modalStyles.closeButton}>
              <X size={24} color={colors.text} strokeWidth={2.5} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={modalStyles.modalBody} showsVerticalScrollIndicator={false}>
            {/* Usage Quantity */}
            <View style={modalStyles.section}>
              <Text style={[modalStyles.sectionTitle, { color: colors.text }]}>
                {language === 'nl' ? 'Dagelijks gebruik' : 'Daily Usage'}
              </Text>
              <Text style={[modalStyles.sectionDescription, { color: colors.muted }]}>
                {language === 'nl' 
                  ? 'Hoeveel gebruikte je gemiddeld per dag?'
                  : 'How much did you use on average per day?'}
              </Text>
              
              {/* Quantity Input */}
              <View style={formStyles.fieldContainer}>
                <Text style={[formStyles.fieldLabel, { color: colors.text }]}>
                  {language === 'nl' ? 'Hoeveelheid' : 'Quantity'}
                </Text>
                <TextInput
                  style={[
                    formStyles.quantityInput,
                    {
                      backgroundColor: colors.background,
                      borderColor: colors.border,
                      color: colors.text,
                    }
                  ]}
                  value={usageQuantity}
                  onChangeText={setUsageQuantity}
                  placeholder="0"
                  placeholderTextColor={colors.muted}
                  keyboardType="numeric"
                />
              </View>
              
              {/* Unit Selection */}
              <View style={formStyles.fieldContainer}>
                <Text style={[formStyles.fieldLabel, { color: colors.text }]}>
                  {language === 'nl' ? 'Eenheid' : 'Unit'}
                </Text>
                <View style={selectionStyles.unitGrid}>
                  {availableUnits.map((unit) => (
                    <TouchableOpacity
                      key={unit.id}
                      style={[
                        selectionStyles.unitButton,
                        { 
                          backgroundColor: selectedUnit === unit.id ? colors.primary : colors.background,
                          borderColor: selectedUnit === unit.id ? colors.primary : colors.border,
                        }
                      ]}
                      onPress={() => setSelectedUnit(unit.id)}
                    >
                      <Text style={[
                        selectionStyles.unitButtonText,
                        selectedUnit === unit.id ? selectionStyles.selectedText : { color: colors.text }
                      ]}>
                        {unit.label[language]}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>
            {/* Cost Information */}
            <View style={modalStyles.section}>
              <Text style={[modalStyles.sectionTitle, { color: colors.text }]}>
                {language === 'nl' ? 'Kosten' : 'Cost'}
              </Text>
              <Text style={[modalStyles.sectionDescription, { color: colors.muted }]}>
                {language === 'nl' 
                  ? 'Hoeveel gaf je uit per dag?'
                  : 'How much did you spend per day?'}
              </Text>
              
              {/* Amount Input */}
              <View style={formStyles.fieldContainer}>
                <Text style={[formStyles.fieldLabel, { color: colors.text }]}>
                  {language === 'nl' ? 'Bedrag' : 'Amount'}
                </Text>
                <View style={formStyles.amountInputContainer}>
                  <View style={[formStyles.currencyIconContainer, { backgroundColor: colors.primary + '15' }]}>
                    {selectedCurrency === 'USD' ? (
                      <DollarSign size={20} color={colors.primary} />
                    ) : (
                      <Euro size={20} color={colors.primary} />
                    )}
                  </View>
                  <TextInput
                    style={[
                      formStyles.amountInput,
                      {
                        backgroundColor: colors.background,
                        borderColor: colors.border,
                        color: colors.text,
                      }
                    ]}
                    value={usageCost}
                    onChangeText={setUsageCost}
                    placeholder="0.00"
                    placeholderTextColor={colors.muted}
                    keyboardType="numeric"
                  />
                </View>
              </View>
              
              {/* Currency Selection */}
              <View style={formStyles.fieldContainer}>
                <Text style={[formStyles.fieldLabel, { color: colors.text }]}>
                  {language === 'nl' ? 'Valuta' : 'Currency'}
                </Text>
                <View style={selectionStyles.currencyGrid}>
                  <TouchableOpacity
                    style={[
                      selectionStyles.currencyButton,
                      { 
                        backgroundColor: selectedCurrency === 'EUR' ? colors.primary : colors.background,
                        borderColor: selectedCurrency === 'EUR' ? colors.primary : colors.border,
                      }
                    ]}
                    onPress={() => setSelectedCurrency('EUR')}
                  >
                    <Euro size={20} color={selectedCurrency === 'EUR' ? '#fff' : colors.text} />
                    <Text style={[
                      selectionStyles.currencyButtonText,
                      selectedCurrency === 'EUR' ? selectionStyles.selectedText : { color: colors.text }
                    ]}>
                      EUR
                    </Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[
                      selectionStyles.currencyButton,
                      { 
                        backgroundColor: selectedCurrency === 'USD' ? colors.primary : colors.background,
                        borderColor: selectedCurrency === 'USD' ? colors.primary : colors.border,
                      }
                    ]}
                    onPress={() => setSelectedCurrency('USD')}
                  >
                    <DollarSign size={20} color={selectedCurrency === 'USD' ? '#fff' : colors.text} />
                    <Text style={[
                      selectionStyles.currencyButtonText,
                      selectedCurrency === 'USD' ? selectionStyles.selectedText : { color: colors.text }
                    ]}>
                      USD
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            {/* Summary */}
            {(usageQuantity || usageCost) && (
              <View style={[modalStyles.summarySection, { backgroundColor: colors.primary + '08', borderColor: colors.primary + '25' }]}>
                <Text style={[modalStyles.summaryTitle, { color: colors.primary }]}>
                  {language === 'nl' ? 'Samenvatting' : 'Summary'}
                </Text>
                <Text style={[modalStyles.summaryText, { color: colors.text }]}>
                  {language === 'nl' 
                    ? `${usageQuantity ? `Je gebruikte ${usageQuantity} ${availableUnits.find(u => u.id === selectedUnit)?.label[language] || selectedUnit} per dag` : ''}${usageQuantity && usageCost ? ' en ' : ''}${usageCost ? `gaf ${selectedCurrency === 'USD' ? '$' : '€'}${usageCost} uit per dag` : ''}.`
                    : `${usageQuantity ? `You used ${usageQuantity} ${availableUnits.find(u => u.id === selectedUnit)?.label[language] || selectedUnit} per day` : ''}${usageQuantity && usageCost ? ' and ' : ''}${usageCost ? `spent ${selectedCurrency === 'USD' ? '$' : '€'}${usageCost} per day` : ''}.`}
                </Text>
              </View>
            )}
          </ScrollView>
          
          {/* Footer */}
          <View style={[modalStyles.modalFooter, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[modalButtonStyles.secondaryButton, { borderColor: colors.border }]}
              onPress={onClose}
            >
              <Text style={[modalButtonStyles.secondaryButtonText, { color: colors.text }]}>
                {language === 'nl' ? 'Annuleren' : 'Cancel'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[modalButtonStyles.primaryButton, { backgroundColor: colors.primary }]}
              onPress={handleSave}
            >
              <Save size={20} color="#fff" />
              <Text style={modalButtonStyles.primaryButtonText}>
                {language === 'nl' ? 'Opslaan' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};