import React from "react";
import { StyleSheet, Text, View, TouchableOpacity, Image } from "react-native";
import { Edit2, Mail, Phone, MapPin } from "lucide-react-native";
import { UserProfile } from "@/types/user";

interface ColorTheme {
  primary: string;
  text: string;
  muted: string;
  // Add other common theme colors if needed
}

interface ProfileSectionProps {
  profile: UserProfile;
  colors: ColorTheme;
  onEditProfile: () => void;
}

export const ProfileSection: React.FC<ProfileSectionProps> = ({
  profile,
  colors,
  onEditProfile,
}) => {
  return (
    <View style={styles.profileSection}>
      <View style={[styles.profileCircle, { backgroundColor: colors.primary }]}>
        {profile.profileImage ? (
          <Image
            source={{ uri: profile.profileImage }}
            style={styles.profileImage}
          />
        ) : (
          <Text style={styles.profileInitial}>
            {profile.name?.charAt(0).toUpperCase() || "U"}
          </Text>
        )}
      </View>
      <Text style={[styles.profileName, { color: colors.text }]}>
        {profile.name || "User"}
      </Text>
      <Text style={[styles.profileInfo, { color: colors.muted }]}>
        {profile.language === "nl" ? "Herstelt van" : "Recovering from"}{" "}
        {profile.addiction}
      </Text>

      {/* Show additional profile info if available */}
      {(profile.email || profile.phone || profile.country) && (
        <View style={styles.additionalInfoContainer}>
          {profile.email && (
            <View style={styles.infoRow}>
              <Mail size={14} color={colors.primary} style={styles.infoIcon} />
              <Text style={[styles.infoText, { color: colors.text }]}>
                {profile.email}
              </Text>
            </View>
          )}

          {profile.phone && (
            <View style={styles.infoRow}>
              <Phone size={14} color={colors.primary} style={styles.infoIcon} />
              <Text style={[styles.infoText, { color: colors.text }]}>
                {profile.phone}
              </Text>
            </View>
          )}

          {profile.country && (
            <View style={styles.infoRow}>
              <MapPin
                size={14}
                color={colors.primary}
                style={styles.infoIcon}
              />
              <Text style={[styles.infoText, { color: colors.text }]}>
                {profile.country}
              </Text>
            </View>
          )}
        </View>
      )}

      <TouchableOpacity
        style={[
          styles.editProfileButton,
          { backgroundColor: colors.primary + "20" },
        ]}
        onPress={() => {
          console.log('Edit profile button pressed');
          onEditProfile();
        }}
      >
        <Edit2 size={16} color={colors.primary} />
        <Text style={[styles.editProfileText, { color: colors.primary }]}>
          {profile.language === "nl" ? "Profiel bewerken" : "Edit Profile"}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({


  additionalInfoContainer: {
    alignItems: "center",
    marginBottom: 16,
  },
  editProfileButton: {
    alignItems: "center",
    borderRadius: 20,
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  editProfileText: {
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 6,
  },
  infoIcon: {
    marginRight: 6,
  },
  infoRow: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: 6,
  },
  infoText: {
    fontSize: 14,
  },
  profileCircle: {
    alignItems: "center",
    borderRadius: 40,
    height: 80,
    justifyContent: "center",
    marginBottom: 16,
    overflow: "hidden",
    width: 80,
  },
  profileImage: {
    height: 80,
    width: 80,
  },
  profileInfo: {
    fontSize: 16,
    marginBottom: 16,
  },
  profileInitial: {
    color: "#fff",
    fontSize: 32,
    fontWeight: "bold",
  },
  profileName: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 4,
  },
  profileSection: {
    alignItems: "center",
    marginBottom: 20,
    padding: 20,
  },


});
