import React, { ReactNode } from "react";
import { StyleSheet, Text, View, TouchableOpacity, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ChevronRight } from 'lucide-react-native';
import { ColorScheme } from '@/hooks/ui/useColorScheme';
interface SettingsSectionProps {
  title: string;
  children: ReactNode;
  colors: ColorScheme;
}
interface SettingsItemProps {
  icon: ReactNode;
  title: string;
  value?: string;
  onPress: () => void;
  textColor: string;
  borderColor: string;
  rightElement?: ReactNode;
  isLoading?: boolean;
  colors: ColorScheme;
}
export const SettingsSection: React.FC<SettingsSectionProps> & {
  Item: React.FC<SettingsItemProps>;
} = ({ title, children, colors }) => {
  return (
    <View style={styles.section}>
      {title ? (
        <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>
          {title.toUpperCase()}
        </Text>
      ) : null}
      <View style={[styles.sectionContent, { backgroundColor: colors.card || colors.background, borderColor: colors.border }]}>
        {children}
      </View>
    </View>
  );
};
const SettingsItem: React.FC<SettingsItemProps> = ({ 
  icon, 
  title, 
  value, 
  onPress, 
  textColor, 
  borderColor,
  rightElement,
  isLoading = false,
  colors
}) => {
  // Ensure colors is defined with fallback to prevent 'card' of undefined error
  if (!colors) {
    console.warn('Colors prop is undefined in SettingsItem');
    colors = {
      card: '#FFFFFF',
      background: '#FAFBFC',
      cardGradient: '#FEFEFE',
      primary: '#6366F1',
      textSecondary: '#475569',
      muted: '#94A3B8',
      border: '#E2E8F0'
    } as ColorScheme;
  }
  const cardColor = colors.card || colors.background;
  const cardGradientColor = colors.cardGradient || colors.card || colors.background;
  
  return (
    <TouchableOpacity 
      style={[styles.item, { borderBottomColor: borderColor }]} 
      onPress={onPress}
      disabled={isLoading}
      activeOpacity={0.7}
    >
      <LinearGradient
        colors={[cardColor, cardGradientColor] as const}
        style={styles.itemGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.itemLeft}>
          <View style={[styles.iconContainer, { backgroundColor: colors.primary + '15' }]}>
            {icon}
          </View>
          <Text style={[styles.itemTitle, { color: textColor }]}>
            {title}
          </Text>
        </View>
        <View style={styles.itemRight}>
          {isLoading ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <>
              {value && (
                <Text style={[styles.itemValue, { color: colors.textSecondary }]}>
                  {value}
                </Text>
              )}
              {rightElement || <ChevronRight size={20} color={colors.muted} strokeWidth={2.5} />}
            </>
          )}
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};
SettingsSection.Item = SettingsItem;
const styles = StyleSheet.create({


  iconContainer: {
    alignItems: 'center',
    borderRadius: 12,
    height: 40,
    justifyContent: 'center',
    marginRight: 16,
    width: 40,
  },
  item: {
    borderBottomWidth: 0.5,
    overflow: 'hidden',
  },
  itemGradient: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 18,
  },
  itemLeft: {
    alignItems: 'center',
    flexDirection: 'row',
    flex: 1,
  },
  itemRight: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  itemTitle: {
    flex: 1,
    fontSize: 17,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
  itemValue: {
    fontSize: 15,
    fontWeight: '500',
    letterSpacing: -0.2,
    marginRight: 12,
  },
  section: {
    marginBottom: 24,
    marginHorizontal: 16,
  },
  sectionContent: {
    borderRadius: 20,
    borderWidth: 1,
    elevation: 4,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: '700',
    letterSpacing: 1,
    marginBottom: 12,
    marginLeft: 4,
  },


});