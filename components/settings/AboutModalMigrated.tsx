import React from 'react';
import { Modal, View, Text, TouchableOpacity, ScrollView, Linking, StyleSheet } from 'react-native';
import { X, Heart, Globe, Shield, Mail } from 'lucide-react-native';
import { useTranslation } from '@/hooks/useTranslation';

interface SettingsColorScheme {
  background: string;
  card: string;
  border: string;
  text: string;
  primary: string;
  muted: string;
  success?: string;
  info?: string;
  warning?: string;
}

interface AboutModalProps {
  visible: boolean;
  onClose: () => void;
  colors: SettingsColorScheme;
}

export const AboutModalMigrated: React.FC<AboutModalProps> = ({
  visible,
  onClose,
  colors,
}) => {
  const { t } = useTranslation();
  const contactEmail = '<EMAIL>';

  const openLink = (url: string) => {
    Linking.openURL(url);
  };

  const openEmail = () => {
    Linking.openURL(`mailto:${contactEmail}`);
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
          <Text style={[styles.modalTitle, { color: colors.text }]}>
            {t('settings.about')}
          </Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={24} color={colors.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
          <View style={styles.appInfoSection}>
            <View style={[styles.appIcon, { backgroundColor: colors.primary + '20' }]}>
              <Heart size={32} color={colors.primary} />
            </View>
            <Text style={[styles.appName, { color: colors.text }]}>SobrixHealth</Text>
            <Text style={[styles.appVersion, { color: colors.muted }]}>Version 1.0.0</Text>
            <Text style={[styles.appDescription, { color: colors.text }]}>
              {t('settings.about') === 'About' 
                ? 'SobrixHealth is an app designed to help people in their recovery from addiction. It provides tools for tracking sobriety, mindfulness exercises, and access to resources.'
                : 'SobrixHealth is een app ontworpen om mensen te helpen bij hun herstel van verslaving. Het biedt tools voor het bijhouden van nuchterheid, mindfulness oefeningen, en toegang tot hulpbronnen.'}
            </Text>
          </View>
          
          <View style={styles.featuresSection}>
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>
              {t('settings.features')}
            </Text>
            
            <View style={styles.featuresList}>
              <View style={styles.featureItem}>
                <View style={[styles.featureIcon, { backgroundColor: (colors.success || colors.primary) + '20' }]}>
                  <Heart size={20} color={colors.success || colors.primary} />
                </View>
                <View style={styles.featureContent}>
                  <Text style={[styles.featureTitle, { color: colors.text }]}>
                    {t('settings.sobrietyTracker')}
                  </Text>
                  <Text style={[styles.featureDescription, { color: colors.muted }]}>
                    {t('settings.sobrietyTrackerDesc')}
                  </Text>
                </View>
              </View>
              
              <View style={styles.featureItem}>
                <View style={[styles.featureIcon, { backgroundColor: (colors.info || colors.primary) + '20' }]}>
                  <Globe size={20} color={colors.info || colors.primary} />
                </View>
                <View style={styles.featureContent}>
                  <Text style={[styles.featureTitle, { color: colors.text }]}>
                    {t('settings.mindfulness')}
                  </Text>
                  <Text style={[styles.featureDescription, { color: colors.muted }]}>
                    {t('settings.mindfulnessDesc')}
                  </Text>
                </View>
              </View>
              
              <View style={styles.featureItem}>
                <View style={[styles.featureIcon, { backgroundColor: (colors.warning || colors.primary) + '20' }]}>
                  <Shield size={20} color={colors.warning || colors.primary} />
                </View>
                <View style={styles.featureContent}>
                  <Text style={[styles.featureTitle, { color: colors.text }]}>
                    {t('settings.crisisSupport')}
                  </Text>
                  <Text style={[styles.featureDescription, { color: colors.muted }]}>
                    {t('settings.crisisSupportDesc')}
                  </Text>
                </View>
              </View>
            </View>
          </View>
          
          <View style={styles.supportSection}>
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>
              {t('settings.help')}
            </Text>
            
            <Text style={[styles.sectionDescription, { color: colors.text }]}>
              {t('settings.help') === 'Help & Support'
                ? 'Need help? Contact us through one of the options below.'
                : 'Heb je hulp nodig? Neem contact met ons op via een van de onderstaande opties.'}
            </Text>
            
            <TouchableOpacity 
              style={[styles.supportOption, { backgroundColor: colors.primary + '10', borderColor: colors.primary + '30' }]}
              onPress={openEmail}
            >
              <View style={[styles.iconContainer, { backgroundColor: colors.primary }]}>
                <Mail size={20} color="#fff" />
              </View>
              <View style={styles.optionContent}>
                <Text style={[styles.optionTitle, { color: colors.text }]}>
                  {t('settings.help') === 'Help & Support' ? 'Email' : 'E-mail'}
                </Text>
                <Text style={[styles.optionDescription, { color: colors.muted }]}>
                  {contactEmail}
                </Text>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.linkItem, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={() => openLink('https://sobrixhealth.com')}
            >
              <Globe size={20} color={colors.primary} />
              <Text style={[styles.linkText, { color: colors.text }]}>
                {t('settings.visitWebsite')}
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.faqSection}>
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>
              {t('settings.faq')}
            </Text>
            
            <View style={styles.faqItem}>
              <Text style={[styles.faqQuestion, { color: colors.text }]}>
                {t('settings.resetPassword')}
              </Text>
              <Text style={[styles.faqAnswer, { color: colors.muted }]}>
                {t('settings.resetPasswordAnswer')}
              </Text>
            </View>
            
            <View style={styles.faqItem}>
              <Text style={[styles.faqQuestion, { color: colors.text }]}>
                {t('settings.exportData')}
              </Text>
              <Text style={[styles.faqAnswer, { color: colors.muted }]}>
                {t('settings.exportDataAnswer')}
              </Text>
            </View>
            
            <View style={styles.faqItem}>
              <Text style={[styles.faqQuestion, { color: colors.text }]}>
                {t('settings.dataSafety')}
              </Text>
              <Text style={[styles.faqAnswer, { color: colors.muted }]}>
                {t('settings.dataSafetyAnswer')}
              </Text>
            </View>
          </View>
        </ScrollView>
        
        {/* Footer */}
        <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
          <TouchableOpacity
            style={[styles.closeFooterButton, { backgroundColor: colors.primary }]}
            onPress={onClose}
          >
            <Text style={styles.closeFooterButtonText}>
              {t('common.close')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  appDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginTop: 12,
    textAlign: 'center',
  },
  appIcon: {
    alignItems: 'center',
    borderRadius: 20,
    height: 80,
    justifyContent: 'center',
    marginBottom: 16,
    width: 80,
  },
  appInfoSection: {
    alignItems: 'center',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  appName: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  appVersion: {
    fontSize: 16,
    fontWeight: '500',
  },
  closeButton: {
    padding: 8,
  },
  closeFooterButton: {
    alignItems: 'center',
    borderRadius: 12,
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 16,
  },
  closeFooterButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  faqAnswer: {
    fontSize: 14,
    lineHeight: 20,
    marginTop: 4,
  },
  faqItem: {
    marginBottom: 20,
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '600',
  },
  faqSection: {
    marginBottom: 32,
  },
  featureContent: {
    flex: 1,
    marginLeft: 12,
  },
  featureDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginTop: 2,
  },
  featureIcon: {
    alignItems: 'center',
    borderRadius: 12,
    height: 40,
    justifyContent: 'center',
    width: 40,
  },
  featureItem: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    marginBottom: 16,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  featuresList: {
    marginTop: 16,
  },
  featuresSection: {
    marginBottom: 32,
  },
  iconContainer: {
    alignItems: 'center',
    borderRadius: 12,
    height: 40,
    justifyContent: 'center',
    width: 40,
  },
  linkItem: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginTop: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  linkText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  modalContainer: {
    flex: 1,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  modalFooter: {
    borderTopWidth: 1,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  modalHeader: {
    alignItems: 'center',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  optionContent: {
    flex: 1,
    marginLeft: 12,
  },
  optionDescription: {
    fontSize: 14,
    marginTop: 2,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  sectionDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
  },
  supportOption: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  supportSection: {
    marginBottom: 32,
  },
}); 