import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/context/theme-context';
import { useDatabaseMigration } from '@/hooks/database/use-database';
import { useDatabaseContext } from '@/context/database-context';
import Colors from '@/constants/colors';
import { Database, Download, X, CheckCircle, AlertCircle } from 'lucide-react-native';

interface DatabaseMigrationModalProps {
  visible: boolean;
  onClose: () => void;
}

export const DatabaseMigrationModal: React.FC<DatabaseMigrationModalProps> = ({
  visible,
  onClose,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { isInitialized, service } = useDatabaseContext();
  const { isMigrating, migrationError, migrationComplete, migrateData } = useDatabaseMigration();
  const [stats, setStats] = useState<{ tables: number; totalRecords: number } | null>(null);

  const handleMigration = async () => {
    try {
      await migrateData();
      // Get updated stats after migration
      const newStats = await service.getDatabaseStats();
      setStats(newStats);
    } catch (error) {
      console.error('Migration failed:', error);
      Alert.alert(
        'Migration Failed',
        'Failed to migrate data to database. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const loadStats = React.useCallback(async () => {
    if (isInitialized) {
      try {
        const dbStats = await service.getDatabaseStats();
        setStats(dbStats);
      } catch (error) {
        console.error('Failed to load database stats:', error);
      }
    }
  }, [isInitialized, service]);

  React.useEffect(() => {
    if (visible && isInitialized) {
      loadStats();
    }
  }, [visible, isInitialized, loadStats]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Database size={24} color={colors.primary} />
              <Text style={[styles.headerTitle, { color: colors.text }]}>
                Database Migration
              </Text>
            </View>
            <TouchableOpacity
              onPress={onClose}
              style={[styles.closeButton, { backgroundColor: colors.card }]}
            >
              <X size={20} color={colors.text} />
            </TouchableOpacity>
          </View>

          {/* Database Status */}
          <View style={[styles.section, { backgroundColor: colors.card }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Database Status
            </Text>
            
            <View style={styles.statusRow}>
              {isInitialized ? (
                <CheckCircle size={16} color={colors.success} />
              ) : (
                <AlertCircle size={16} color={colors.warning} />
              )}
              <Text style={[styles.statusText, { color: colors.text }]}>
                {isInitialized ? 'Database Initialized' : 'Database Not Ready'}
              </Text>
            </View>

            {stats && (
              <>
                <Text style={[styles.statsText, { color: colors.muted }]}>
                  Tables: {stats.tables}
                </Text>
                <Text style={[styles.statsText, { color: colors.muted }]}>
                  Total Records: {stats.totalRecords}
                </Text>
              </>
            )}
          </View>

          {/* Migration Section */}
          <View style={[styles.section, { backgroundColor: colors.card }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Data Migration
            </Text>
            
            <Text style={[styles.description, { color: colors.muted }]}>
              Migrate your existing data from AsyncStorage to the new SQLite database for better performance and reliability.
            </Text>

            {migrationComplete && (
              <View style={[styles.statusMessage, { backgroundColor: colors.success + '20' }]}>
                <CheckCircle size={16} color={colors.success} />
                <Text style={[styles.statusMessageText, { color: colors.success }]}>
                  Migration completed successfully!
                </Text>
              </View>
            )}

            {migrationError && (
              <View style={[styles.statusMessage, { backgroundColor: colors.danger + '20' }]}>
                <AlertCircle size={16} color={colors.danger} />
                <Text style={[styles.statusMessageText, { color: colors.danger }]}>
                  Migration failed: {migrationError}
                </Text>
              </View>
            )}

            <TouchableOpacity
              onPress={handleMigration}
              disabled={!isInitialized || isMigrating}
              style={[
                styles.migrationButton,
                { backgroundColor: isInitialized && !isMigrating ? colors.primary : colors.muted }
              ]}
            >
              {isMigrating ? (
                <ActivityIndicator size="small" color={colors.background} />
              ) : (
                <Download size={16} color={colors.background} />
              )}
              <Text style={[styles.migrationButtonText, { color: colors.background }]}>
                {isMigrating ? 'Migrating...' : 'Start Migration'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Information */}
          <View style={[styles.infoSection, { backgroundColor: colors.primary + '10' }]}>
            <Text style={[styles.infoTitle, { color: colors.primary }]}>
              About Database Migration
            </Text>
            <Text style={[styles.infoText, { color: colors.text }]}>
              • SQLite provides better performance for large datasets{'\n'}
              • Enables complex queries and data relationships{'\n'}
              • Reduces storage quota issues on web platforms{'\n'}
              • Your existing data will be preserved during migration
            </Text>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  closeButton: {
    borderRadius: 20,
    padding: 8,
  },
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 15,
  },
  header: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  headerLeft: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  infoSection: {
    borderRadius: 12,
    padding: 20,
  },
  infoText: {
    fontSize: 13,
    lineHeight: 18,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  migrationButton: {
    alignItems: 'center',
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 12,
  },
  migrationButtonText: {
    fontWeight: '600',
    marginLeft: 8,
  },
  section: {
    borderRadius: 12,
    marginBottom: 20,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  statsText: {
    fontSize: 14,
  },
  statusMessage: {
    alignItems: 'center',
    borderRadius: 8,
    flexDirection: 'row',
    marginBottom: 15,
    padding: 12,
  },
  statusMessageText: {
    fontWeight: '500',
    marginLeft: 8,
  },
  statusRow: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 8,
  },
  statusText: {
    marginLeft: 8,
  },
}); 