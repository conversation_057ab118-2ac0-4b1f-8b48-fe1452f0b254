import React from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Linking, Modal } from 'react-native';
import { Mail, MessageCircle, Phone, X } from 'lucide-react-native';
import { UserProfile } from "@/types/user";
import { useTranslation } from '@/hooks/useTranslation';

interface SettingsColorScheme {
  background: string;
  card: string;
  border: string;
  text: string;
  primary: string;
  muted: string;
}

interface HelpSupportModalProps {
  visible: boolean;
  onClose: () => void;
  profile: UserProfile;
  colors: SettingsColorScheme;
}

export const HelpSupportModal: React.FC<HelpSupportModalProps> = ({
  visible,
  onClose,
  profile: _profile, // Keep for backward compatibility but don't use
  colors,
}) => {
  const { t } = useTranslation();
  const contactEmail = '<EMAIL>';
  const contactPhone = '******-CRISIS';
  
  const openEmail = () => {
    Linking.openURL(`mailto:${contactEmail}`);
  };
  
  const openPhone = () => {
    Linking.openURL(`tel:${contactPhone}`);
  };
  
  const openChat = () => {
    // In a real app, this would open a chat interface
    Linking.openURL('https://crisisbox.com/chat');
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
          {/* Header */}
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {t('settings.helpModal.title')}
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={colors.text} strokeWidth={2.5} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
            <Text style={[styles.sectionDescription, { color: colors.text }]}>
              {t('settings.helpModal.description')}
            </Text>
            
            <View style={styles.supportOptionsSection}>
              <TouchableOpacity 
                style={[styles.supportOption, { backgroundColor: colors.primary + '10', borderColor: colors.primary + '30' }]}
                onPress={openEmail}
              >
                <View style={[styles.iconContainer, { backgroundColor: colors.primary }]}>
                  <Mail size={24} color="#fff" />
                </View>
                <View style={styles.optionContent}>
                  <Text style={[styles.optionTitle, { color: colors.text }]}>
                    {t('settings.helpModal.email')}
                  </Text>
                  <Text style={[styles.optionDescription, { color: colors.muted }]}>
                    {t('settings.helpModal.emailDescription')}
                  </Text>
                  <Text style={[styles.optionDetail, { color: colors.primary }]}>
                    {contactEmail}
                  </Text>
                </View>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.supportOption, { backgroundColor: colors.primary + '10', borderColor: colors.primary + '30' }]}
                onPress={openPhone}
              >
                <View style={[styles.iconContainer, { backgroundColor: colors.primary }]}>
                  <Phone size={24} color="#fff" />
                </View>
                <View style={styles.optionContent}>
                  <Text style={[styles.optionTitle, { color: colors.text }]}>
                    {t('settings.helpModal.phone')}
                  </Text>
                  <Text style={[styles.optionDescription, { color: colors.muted }]}>
                    {t('settings.helpModal.phoneDescription')}
                  </Text>
                  <Text style={[styles.optionDetail, { color: colors.primary }]}>
                    {contactPhone}
                  </Text>
                </View>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.supportOption, { backgroundColor: colors.primary + '10', borderColor: colors.primary + '30' }]}
                onPress={openChat}
              >
                <View style={[styles.iconContainer, { backgroundColor: colors.primary }]}>
                  <MessageCircle size={24} color="#fff" />
                </View>
                <View style={styles.optionContent}>
                  <Text style={[styles.optionTitle, { color: colors.text }]}>
                    {t('settings.helpModal.liveChat')}
                  </Text>
                  <Text style={[styles.optionDescription, { color: colors.muted }]}>
                    {t('settings.helpModal.liveChatDescription')}
                  </Text>
                  <Text style={[styles.optionDetail, { color: colors.primary }]}>
                    {t('settings.helpModal.available247')}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
            
            <View style={styles.faqSection}>
              <Text style={[styles.sectionTitle, { color: colors.primary }]}>
                {t('settings.faq')}
              </Text>
              
              <View style={styles.faqItem}>
                <Text style={[styles.faqQuestion, { color: colors.text }]}>
                  {t('settings.resetPassword')}
                </Text>
                <Text style={[styles.faqAnswer, { color: colors.muted }]}>
                  {t('settings.resetPasswordAnswer')}
                </Text>
              </View>
              
              <View style={styles.faqItem}>
                <Text style={[styles.faqQuestion, { color: colors.text }]}>
                  {t('settings.exportData')}
                </Text>
                <Text style={[styles.faqAnswer, { color: colors.muted }]}>
                  {t('settings.exportDataAnswer')}
                </Text>
              </View>
              
              <View style={styles.faqItem}>
                <Text style={[styles.faqQuestion, { color: colors.text }]}>
                  {t('settings.dataSafety')}
                </Text>
                <Text style={[styles.faqAnswer, { color: colors.muted }]}>
                  {t('settings.dataSafetyAnswer')}
                </Text>
              </View>
            </View>
          </ScrollView>
          
          {/* Footer */}
          <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.closeButtonFooter, { backgroundColor: colors.primary }]}
              onPress={onClose}
            >
              <Text style={styles.closeButtonText}>
                {t('common.close')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  closeButton: {
    padding: 4,
  },
  closeButtonFooter: {
    alignItems: 'center',
    borderRadius: 16,
    padding: 16,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: -0.2,
  },
  faqAnswer: {
    fontSize: 14,
    lineHeight: 20,
  },
  faqItem: {
    marginBottom: 20,
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  faqSection: {
    marginBottom: 16,
  },
  iconContainer: {
    alignItems: 'center',
    borderRadius: 24,
    height: 48,
    justifyContent: 'center',
    marginRight: 16,
    width: 48,
  },
  modalBody: {
    maxHeight: 400,
    padding: 20,
  },
  modalContent: {
    borderRadius: 24,
    elevation: 8,
    maxHeight: '80%',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    width: '100%',
  },
  modalFooter: {
    borderTopWidth: 1,
    padding: 20,
  },
  modalHeader: {
    alignItems: 'center',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
  },
  modalOverlay: {
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  optionContent: {
    flex: 1,
  },
  optionDescription: {
    fontSize: 14,
    marginBottom: 4,
  },
  optionDetail: {
    fontSize: 14,
    fontWeight: '500',
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 24,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
    marginBottom: 16,
  },
  supportOption: {
    alignItems: 'center',
    borderRadius: 16,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 16,
    padding: 20,
  },
  supportOptionsSection: {
    marginBottom: 32,
  },
});