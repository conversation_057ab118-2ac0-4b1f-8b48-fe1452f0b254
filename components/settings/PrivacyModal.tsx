import React from "react";
import { useState, useEffect, useMemo } from 'react';
import { StyleSheet, Text, View, ScrollView, Modal, TouchableOpacity, Switch } from 'react-native';
import { Shield, Eye, EyeOff, Database, Lock, X, Save } from 'lucide-react-native';
import { useUserStore } from '@/store/user/user-store';
import { UserProfile, PrivacySettings } from '@/types/user';
import { useTranslation } from '@/hooks/useTranslation';

interface SettingsColorScheme {
  background: string;
  card: string;
  border: string;
  text: string;
  primary: string;
  muted: string;
  success?: string;
  danger?: string;
  info?: string;
  secondary?: string;
  warning?: string;
}

interface PrivacyModalProps {
  visible: boolean;
  onClose: () => void;
  profile: UserProfile;
  colors: SettingsColorScheme;
}

export const PrivacyModal: React.FC<PrivacyModalProps> = ({
  visible,
  onClose,
  profile,
  colors,
}) => {
  const { updateProfile } = useUserStore();
  const { t } = useTranslation();
  
  // Initialize privacy settings
  const initialSettings: PrivacySettings = useMemo(() => ({
    dataCollection: true,
    anonymousAnalytics: true,
    crashReporting: true,
    locationTracking: false,
    biometricAuth: false,
    appLock: false,
  }), []);

  const [settings, setSettings] = useState<PrivacySettings>(
    { ...initialSettings, ...profile.privacySettings }
  );

  // Load privacy settings from profile when modal opens
  useEffect(() => {
    if (visible && profile.privacySettings) {
      setSettings({ ...initialSettings, ...profile.privacySettings });
    }
  }, [visible, profile, initialSettings]);

  const handleToggle = (id: string, value: boolean) => {
    setSettings(prev => ({ ...prev, [id]: value }));
  };

  const savePrivacySettings = () => {
    updateProfile({ privacySettings: settings });
    onClose();
  };

  const privacySections = [
    {
      title: t('settings.privacyModal.dataCollectionSection'),
      items: [
        {
          id: 'dataCollection' as keyof PrivacySettings,
          title: t('settings.privacyModal.dataCollection'),
          description: t('settings.privacyModal.dataCollectionDesc'),
          icon: Database,
          value: settings.dataCollection,
          color: colors.primary
        },
        {
          id: 'anonymousAnalytics' as keyof PrivacySettings,
          title: t('settings.privacyModal.anonymousAnalytics'),
          description: t('settings.privacyModal.anonymousAnalyticsDesc'),
          icon: Eye,
          value: settings.anonymousAnalytics,
          color: colors.info || colors.primary
        },
        {
          id: 'crashReporting' as keyof PrivacySettings,
          title: t('settings.privacyModal.crashReporting'),
          description: t('settings.privacyModal.crashReportingDesc'),
          icon: Shield,
          value: settings.crashReporting,
          color: colors.warning || colors.primary
        }
      ]
    },
    {
      title: t('settings.privacyModal.locationSecurity'),
      items: [
        {
          id: 'locationTracking' as keyof PrivacySettings,
          title: t('settings.privacyModal.locationTracking'),
          description: t('settings.privacyModal.locationTrackingDesc'),
          icon: EyeOff,
          value: settings.locationTracking,
          color: colors.secondary || colors.primary
        },
        {
          id: 'biometricAuth' as keyof PrivacySettings,
          title: t('settings.privacyModal.biometricAuth'),
          description: t('settings.privacyModal.biometricAuthDesc'),
          icon: Shield,
          value: settings.biometricAuth,
          color: colors.success || colors.primary
        },
        {
          id: 'appLock' as keyof PrivacySettings,
          title: t('settings.privacyModal.appLock'),
          description: t('settings.privacyModal.appLockDesc'),
          icon: Lock,
          value: settings.appLock,
          color: colors.danger || colors.primary
        }
      ]
    }
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
          {/* Header */}
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {t('settings.privacyModal.title')}
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={colors.text} strokeWidth={2.5} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
            {privacySections.map((section, sectionIndex) => (
              <View key={sectionIndex} style={styles.privacySection}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  {section.title}
                </Text>
                
                {section.items.map((item) => (
                  <View key={item.id} style={[styles.toggleItem, { backgroundColor: colors.background, borderColor: colors.border }]}>
                    <View style={[styles.iconContainer, { backgroundColor: item.color + '20' }]}>
                      <item.icon size={20} color={item.color} />
                    </View>
                    <View style={styles.itemContent}>
                      <Text style={[styles.itemTitle, { color: colors.text }]}>
                        {item.title}
                      </Text>
                      <Text style={[styles.itemDescription, { color: colors.muted }]}>
                        {item.description}
                      </Text>
                    </View>
                    <Switch
                      value={item.value}
                      onValueChange={(value) => handleToggle(item.id, value)}
                      trackColor={{ false: colors.border, true: colors.primary + '40' }}
                      thumbColor={item.value ? colors.primary : colors.muted}
                    />
                  </View>
                ))}
              </View>
            ))}
          </ScrollView>
          
          {/* Footer */}
          <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.cancelButton, { borderColor: colors.border }]}
              onPress={onClose}
            >
              <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                {t('common.cancel')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.saveButton, { backgroundColor: colors.primary }]}
              onPress={savePrivacySettings}
            >
              <Save size={20} color="#fff" />
              <Text style={styles.saveButtonText}>
                {t('common.save')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  cancelButton: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flex: 1,
    justifyContent: 'center',
    marginRight: 8,
    padding: 16,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  closeButton: {
    padding: 4,
  },
  iconContainer: {
    alignItems: 'center',
    borderRadius: 20,
    height: 40,
    justifyContent: 'center',
    marginRight: 16,
    width: 40,
  },
  itemContent: {
    flex: 1,
  },
  itemDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  modalBody: {
    maxHeight: 400,
    padding: 16,
  },
  modalContent: {
    borderRadius: 24,
    elevation: 8,
    maxHeight: '80%',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    width: '100%',
  },
  modalFooter: {
    borderTopWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  modalHeader: {
    alignItems: 'center',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
  },
  modalOverlay: {
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  privacySection: {
    marginBottom: 24,
  },
  saveButton: {
    alignItems: 'center',
    borderRadius: 12,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    marginLeft: 8,
    padding: 16,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  toggleItem: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 12,
    padding: 16,
  },
});