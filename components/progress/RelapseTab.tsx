import React from "react";
import { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Animated,
  Platform,
} from "react-native";
import {
  PlusCircle,
  TrendingUp,
  AlertCircle,
  ChevronUp,
  ChevronDown,
  Edit2,
  Trash2,
  Info,
} from "lucide-react-native";
import * as Haptics from "expo-haptics";
import { CalendarView } from "./shared/CalendarView";
import { RelapseForm } from "./RelapseForm";
import { RelapseEntry, Colors, ViewMode, UserProfile } from "./types";
import { EmptyState } from "./shared/EmptyState";
interface RelapseTabProps {
  profile: UserProfile | null;
  colors: Colors;
  fadeAnim: Animated.Value;
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode) => void;
  handleEditRelapse: (id: string) => void;
  handleDeleteRelapse: (id: string) => void;
}
export const RelapseTab: React.FC<RelapseTabProps> = ({
  profile,
  colors,
  fadeAnim,
  viewMode,
  setViewMode,
  handleEditRelapse,
  handleDeleteRelapse,
}) => {
  const [showRelapseForm, setShowRelapseForm] = useState(false);
  const [expandedRelapseId, setExpandedRelapseId] = useState<string | null>(
    null
  );
  const [, setRelapseDate] = useState(new Date());
  if (!profile) {
    return (
      <EmptyState
        icon={TrendingUp}
        message="No profile available"
        colors={colors}
      />
    );
  }
  const toggleRelapseExpand = (id: string) => {
    if (expandedRelapseId === id) {
      setExpandedRelapseId(null);
    } else {
      setExpandedRelapseId(id);
    }
  };
  const getViewModeTitle = (mode: "list" | "calendar" | "chart") => {
    switch (mode) {
      case "list":
        return profile.language === "nl" ? "Lijst" : "List";
      case "calendar":
        return profile.language === "nl" ? "Kalender" : "Calendar";
      case "chart":
        return profile.language === "nl" ? "Grafiek" : "Chart";
    }
  };
  return (
    <Animated.View style={[styles.tabContent, { opacity: fadeAnim }]}>
      <View style={styles.viewModeContainer}>
        <TouchableOpacity
          style={[
            styles.viewModeButton,
            viewMode === "list" && [
              styles.activeViewModeButton,
              { borderColor: colors.primary },
            ],
          ]}
          onPress={() => setViewMode("list")}
        >
          <Text
            style={[
              styles.viewModeText,
              { color: viewMode === "list" ? colors.primary : colors.muted },
            ]}
          >
            {getViewModeTitle("list")}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.viewModeButton,
            viewMode === "calendar" && [
              styles.activeViewModeButton,
              { borderColor: colors.primary },
            ],
          ]}
          onPress={() => setViewMode("calendar")}
        >
          <Text
            style={[
              styles.viewModeText,
              {
                color: viewMode === "calendar" ? colors.primary : colors.muted,
              },
            ]}
          >
            {getViewModeTitle("calendar")}
          </Text>
        </TouchableOpacity>
      </View>
      {!showRelapseForm ? (
        <>
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              setShowRelapseForm(true);
              if (Platform.OS !== "web") {
                Haptics.selectionAsync();
              }
            }}
          >
            <PlusCircle size={20} color="#fff" />
            <Text style={styles.addButtonText}>
              {profile.language === "nl"
                ? "Terugval registreren"
                : "Record Relapse"}
            </Text>
          </TouchableOpacity>
          {(profile.relapses?.length || 0) === 0 ? (
            <View style={styles.emptyState}>
              <TrendingUp size={48} color={colors.muted} />
              <Text style={[styles.emptyStateText, { color: colors.muted }]}>
                {profile.language === "nl"
                  ? "Geen terugvallen geregistreerd. Blijf sterk!"
                  : "No relapses recorded. Stay strong!"}
              </Text>
            </View>
          ) : viewMode === "list" ? (
            <View style={styles.entriesList}>
              {[...(profile.relapses || [])]
                .sort(
                  (a, b) =>
                    new Date(b.date).getTime() - new Date(a.date).getTime()
                )
                .map((relapse: RelapseEntry) => (
                  <View
                    key={relapse.id}
                    style={[
                      styles.entryCard,
                      {
                        backgroundColor: colors.card,
                        borderColor: colors.border,
                      },
                    ]}
                  >
                    <TouchableOpacity
                      style={styles.entryHeader}
                      onPress={() => toggleRelapseExpand(relapse.id)}
                    >
                      <Text style={[styles.entryDate, { color: colors.text }]}>
                        {new Date(relapse.date).toLocaleDateString(
                          profile.language === "nl" ? "nl-NL" : "en-US",
                          { year: "numeric", month: "short", day: "numeric" }
                        )}
                      </Text>
                      <View style={styles.entryHeaderRight}>
                        <AlertCircle size={20} color={colors.danger} />
                        {expandedRelapseId === relapse.id ? (
                          <ChevronUp
                            size={20}
                            color={colors.muted}
                            style={styles.chevronSpacing}
                          />
                        ) : (
                          <ChevronDown
                            size={20}
                            color={colors.muted}
                            style={styles.chevronSpacing}
                          />
                        )}
                      </View>
                    </TouchableOpacity>
                    {relapse.triggers.length > 0 && (
                      <View style={styles.triggerContainer}>
                        <Text
                          style={[styles.triggerLabel, { color: colors.muted }]}
                        >
                          {profile.language === "nl"
                            ? "Triggers:"
                            : "Triggers:"}
                        </Text>
                        <View style={styles.triggerTags}>
                          {relapse.triggers.map(
                            (trigger: string, index: number) => (
                              <View
                                key={index}
                                style={[
                                  styles.triggerTag,
                                  { backgroundColor: colors.primary + "20" },
                                ]}
                              >
                                <Text
                                  style={[
                                    styles.triggerTagText,
                                    { color: colors.primary },
                                  ]}
                                >
                                  {trigger}
                                </Text>
                              </View>
                            )
                          )}
                        </View>
                      </View>
                    )}
                    {expandedRelapseId === relapse.id && (
                      <>
                        {relapse.notes && (
                          <Text
                            style={[styles.entryNotes, { color: colors.text }]}
                          >
                            {relapse.notes}
                          </Text>
                        )}
                        <View style={styles.entryActions}>
                          <TouchableOpacity
                            style={[
                              styles.entryActionButton,
                              { backgroundColor: colors.primary + "20" },
                            ]}
                            onPress={() => handleEditRelapse(relapse.id)}
                          >
                            <Edit2 size={16} color={colors.primary} />
                            <Text
                              style={[
                                styles.entryActionText,
                                { color: colors.primary },
                              ]}
                            >
                              {profile.language === "nl" ? "Bewerken" : "Edit"}
                            </Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={[
                              styles.entryActionButton,
                              { backgroundColor: colors.danger + "20" },
                            ]}
                            onPress={() => handleDeleteRelapse(relapse.id)}
                          >
                            <Trash2 size={16} color={colors.danger} />
                            <Text
                              style={[
                                styles.entryActionText,
                                { color: colors.danger },
                              ]}
                            >
                              {profile.language === "nl"
                                ? "Verwijderen"
                                : "Delete"}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </>
                    )}
                  </View>
                ))}
            </View>
          ) : (
            <>
              <View
                style={[
                  styles.viewModeInfoCard,
                  { backgroundColor: colors.card, borderColor: colors.border },
                ]}
              >
                <Info size={20} color={colors.primary} />
                <Text style={[styles.viewModeInfoText, { color: colors.text }]}>
                  {profile.language === "nl"
                    ? "Tik op een datum om details te bekijken of een nieuwe terugval te registreren"
                    : "Tap on a date to view details or record a new relapse"}
                </Text>
              </View>
              <CalendarView
                entries={(profile.relapses || []).map(
                  (entry: RelapseEntry) => ({
                    id: entry.id,
                    date: new Date(entry.date),
                    value: 0, // Not used for relapses
                    type: "relapse",
                  })
                )}
                onDayPress={(date, entries) => {
                  if (entries.length > 0) {
                    const entryId = entries[0].id;
                    handleEditRelapse(entryId);
                  } else {
                    setRelapseDate(date);
                    setShowRelapseForm(true);
                  }
                }}
                colors={colors}
                language={profile.language || "en"}
                _type="relapse"
              />
            </>
          )}
        </>
      ) : (
        <RelapseForm
          visible={showRelapseForm}
          profile={profile}
          colors={colors}
          language={profile.language || "en"}
          onClose={() => setShowRelapseForm(false)}
          editingRelapseId={null}
        />
      )}
    </Animated.View>
  );
};
const styles = StyleSheet.create({
  activeViewModeButton: {
    borderRadius: 6,
    borderWidth: 1,
  },
  addButton: {
    alignItems: "center",
    borderRadius: 10,
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 16,
    padding: 12,
  },
  addButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 6,
  },
  chevronSpacing: {
    marginLeft: 6,
  },
  emptyState: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    paddingVertical: 48,
  },
  emptyStateText: {
    fontSize: 15,
    marginTop: 12,
    maxWidth: "80%",
    textAlign: "center",
  },
  entriesList: {
    flex: 1,
  },
  entryActionButton: {
    alignItems: "center",
    borderRadius: 12,
    flexDirection: "row",
    marginLeft: 6,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  entryActionText: {
    fontSize: 12,
    marginLeft: 3,
  },
  entryActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  entryCard: {
    borderRadius: 10,
    borderWidth: 1,
    marginBottom: 12,
    padding: 12,
  },
  entryDate: {
    fontSize: 15,
    fontWeight: "500",
  },
  entryHeader: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 10,
  },
  entryHeaderRight: {
    alignItems: "center",
    flexDirection: "row",
  },
  entryNotes: {
    fontSize: 13,
    lineHeight: 18,
    marginBottom: 12,
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  triggerContainer: {
    marginBottom: 10,
  },
  triggerLabel: {
    fontSize: 12,
    marginBottom: 6,
  },
  triggerTag: {
    borderRadius: 12,
    marginBottom: 6,
    marginRight: 6,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  triggerTagText: {
    fontSize: 12,
  },
  triggerTags: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  viewModeButton: {
    alignItems: "center",
    borderColor: "transparent",
    borderWidth: 1,
    flex: 1,
    justifyContent: "center",
    paddingVertical: 6,
  },
  viewModeContainer: {
    borderRadius: 6,
    flexDirection: "row",
    marginBottom: 16,
    overflow: "hidden",
  },
  viewModeInfoCard: {
    alignItems: "center",
    borderRadius: 6,
    borderWidth: 1,
    flexDirection: "row",
    marginBottom: 12,
    padding: 10,
  },
  viewModeInfoText: {
    flex: 1,
    fontSize: 12,
    marginLeft: 6,
  },
  viewModeText: {
    fontSize: 12,
    fontWeight: "500",
  },
});