import React from "react";
import { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
} from "react-native";
import { useUserStore } from "@/store/user/user-store";
import { X, Save, Plus, Minus } from "lucide-react-native";
import * as Haptics from "expo-haptics";
import { RelapseFormProps, RelapseEntry } from "./types";
export const RelapseForm: React.FC<RelapseFormProps> = ({
  visible,
  onClose,
  colors,
  language,
  editingRelapseId = null,
  profile,
}) => {
  const { addRelapse, editRelapse } = useUserStore();
  const [date, setDate] = useState(new Date().toISOString().split("T")[0]);
  const [selectedTriggers, setSelectedTriggers] = useState<string[]>([]);
  const [customTrigger, setCustomTrigger] = useState("");
  const [notes, setNotes] = useState("");
  const commonTriggers = {
    en: [
      "Stress",
      "Negative emotions",
      "Social pressure",
      "Celebration",
      "Boredom",
      "Availability",
      "Conflict",
      "Cravings",
    ],
    nl: [
      "Stress",
      "Negatieve emoties",
      "Sociale druk",
      "Viering",
      "Verveling",
      "Beschikbaarheid",
      "Conflict",
      "Verlangen",
    ],
  };
  useEffect(() => {
    if (editingRelapseId && profile) {
      const relapse = profile.relapses?.find(
        (r: RelapseEntry) => r.id === editingRelapseId
      );
      if (relapse) {
        setDate(relapse.date.split("T")[0]);
        setSelectedTriggers(relapse.triggers);
        setNotes(relapse.notes);
      }
    } else {
      // Reset form for new entry
      setDate(new Date().toISOString().split("T")[0]);
      setSelectedTriggers([]);
      setCustomTrigger("");
      setNotes("");
    }
  }, [editingRelapseId, profile, visible]);
  const handleSave = () => {
    if (Platform.OS !== "web") {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
    const relapseData = {
      date: new Date(date).toISOString(),
      triggers: selectedTriggers,
      notes,
    };
    if (editingRelapseId) {
      editRelapse(editingRelapseId, relapseData);
    } else {
      addRelapse(relapseData);
    }
    onClose();
  };
  const toggleTrigger = (trigger: string) => {
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
    if (selectedTriggers.includes(trigger)) {
      setSelectedTriggers(selectedTriggers.filter((t) => t !== trigger));
    } else {
      setSelectedTriggers([...selectedTriggers, trigger]);
    }
  };
  const addCustomTrigger = () => {
    if (!customTrigger.trim()) return;
    if (Platform.OS !== "web") {
      Haptics.selectionAsync();
    }
    if (!selectedTriggers.includes(customTrigger)) {
      setSelectedTriggers([...selectedTriggers, customTrigger]);
      setCustomTrigger("");
    }
  };
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.modalOverlay}
      >
        <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
          <View
            style={[styles.modalHeader, { borderBottomColor: colors.border }]}
          >
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {editingRelapseId
                ? language === "nl"
                  ? "Terugval bewerken"
                  : "Edit Relapse"
                : language === "nl"
                ? "Terugval registreren"
                : "Record Relapse"}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalBody}>
            <View style={styles.formSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                {language === "nl" ? "Datum" : "Date"}
              </Text>
              <TextInput
                style={[
                  styles.dateInput,
                  {
                    borderColor: colors.border,
                    backgroundColor: colors.background,
                    color: colors.text,
                  },
                ]}
                value={date}
                onChangeText={setDate}
                placeholder="YYYY-MM-DD"
                placeholderTextColor={colors.muted}
              />
            </View>
            <View style={styles.formSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                {language === "nl" ? "Triggers" : "Triggers"}
              </Text>
              <View style={styles.triggersContainer}>
                {commonTriggers[language === "nl" ? "nl" : "en"].map(
                  (trigger) => (
                    <TouchableOpacity
                      key={trigger}
                      style={[
                        styles.triggerButton,
                        {
                          backgroundColor: selectedTriggers.includes(trigger)
                            ? colors.primary
                            : colors.background,
                          borderColor: colors.border,
                        },
                      ]}
                      onPress={() => toggleTrigger(trigger)}
                    >
                      <Text
                        style={[
                          styles.triggerText,
                          selectedTriggers.includes(trigger)
                            ? styles.triggerTextSelected
                            : styles.triggerTextDefault,
                        ]}
                      >
                        {trigger}
                      </Text>
                      {selectedTriggers.includes(trigger) ? (
                        <Minus size={16} color="#fff" />
                      ) : (
                        <Plus size={16} color={colors.text} />
                      )}
                    </TouchableOpacity>
                  )
                )}
              </View>
              <View style={styles.customTriggerContainer}>
                <TextInput
                  style={[
                    styles.customTriggerInput,
                    {
                      borderColor: colors.border,
                      backgroundColor: colors.background,
                      color: colors.text,
                    },
                  ]}
                  value={customTrigger}
                  onChangeText={setCustomTrigger}
                  placeholder={
                    language === "nl"
                      ? "Aangepaste trigger..."
                      : "Custom trigger..."
                  }
                  placeholderTextColor={colors.muted}
                />
                <TouchableOpacity
                  style={[
                    styles.addTriggerButton,
                    { backgroundColor: colors.primary },
                  ]}
                  onPress={addCustomTrigger}
                  disabled={!customTrigger.trim()}
                >
                  <Plus size={20} color="#fff" />
                </TouchableOpacity>
              </View>
              {selectedTriggers.length > 0 && (
                <View style={styles.selectedTriggersContainer}>
                  <Text
                    style={[
                      styles.selectedTriggersTitle,
                      { color: colors.text },
                    ]}
                  >
                    {language === "nl"
                      ? "Geselecteerde triggers:"
                      : "Selected triggers:"}
                  </Text>
                  <View style={styles.selectedTriggersList}>
                    {selectedTriggers.map((trigger) => (
                      <View
                        key={trigger}
                        style={[
                          styles.selectedTriggerItem,
                          { backgroundColor: colors.primary + "20" },
                        ]}
                      >
                        <Text
                          style={[
                            styles.selectedTriggerText,
                            { color: colors.primary },
                          ]}
                        >
                          {trigger}
                        </Text>
                        <TouchableOpacity
                          style={styles.removeTriggerButton}
                          onPress={() => toggleTrigger(trigger)}
                        >
                          <X size={16} color={colors.primary} />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                </View>
              )}
            </View>
            <View style={styles.formSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                {language === "nl" ? "Notities" : "Notes"}
              </Text>
              <TextInput
                style={[
                  styles.notesInput,
                  {
                    borderColor: colors.border,
                    backgroundColor: colors.background,
                    color: colors.text,
                  },
                ]}
                placeholder={
                  language === "nl"
                    ? "Wat gebeurde er? Hoe voelde je je?"
                    : "What happened? How did you feel?"
                }
                placeholderTextColor={colors.muted}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
                value={notes}
                onChangeText={setNotes}
              />
            </View>
          </ScrollView>
          <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.cancelButton, { borderColor: colors.border }]}
              onPress={onClose}
            >
              <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                {language === "nl" ? "Annuleren" : "Cancel"}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.saveButton, { backgroundColor: colors.primary }]}
              onPress={handleSave}
            >
              <Save size={20} color="#fff" />
              <Text style={styles.saveButtonText}>
                {language === "nl" ? "Opslaan" : "Save"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};
const styles = StyleSheet.create({


  addTriggerButton: {
    alignItems: "center",
    borderRadius: 22,
    height: 44,
    justifyContent: "center",
    width: 44,
  },
  cancelButton: {
    alignItems: "center",
    borderRadius: 12,
    borderWidth: 1,
    flex: 1,
    justifyContent: "center",
    marginRight: 8,
    padding: 16,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: "500",
  },
  customTriggerContainer: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: 16,
  },
  customTriggerInput: {
    borderRadius: 12,
    borderWidth: 1,
    flex: 1,
    fontSize: 16,
    marginRight: 8,
    padding: 12,
  },
  dateInput: {
    borderRadius: 12,
    borderWidth: 1,
    fontSize: 16,
    padding: 12,
  },
  formSection: {
    marginBottom: 24,
  },
  modalBody: {
    maxHeight: 500,
    padding: 16,
  },
  modalContent: {
    borderRadius: 16,
    maxHeight: "90%",
    overflow: "hidden",
    width: "100%",
  },
  modalFooter: {
    borderTopWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
  },
  modalHeader: {
    alignItems: "center",
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
  },
  modalOverlay: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  notesInput: {
    borderRadius: 12,
    borderWidth: 1,
    fontSize: 16,
    minHeight: 100,
    padding: 12,
  },
  removeTriggerButton: {
    padding: 2,
  },
  saveButton: {
    alignItems: "center",
    borderRadius: 12,
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    marginLeft: 8,
    padding: 16,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 12,
  },
  selectedTriggerItem: {
    alignItems: "center",
    borderRadius: 16,
    flexDirection: "row",
    margin: 4,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  selectedTriggerText: {
    fontSize: 14,
    marginRight: 8,
  },
  selectedTriggersContainer: {
    marginTop: 8,
  },
  selectedTriggersList: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  selectedTriggersTitle: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 8,
  },
  triggerButton: {
    alignItems: "center",
    borderRadius: 20,
    borderWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    margin: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  triggerText: {
    fontSize: 14,
    fontWeight: "500",
    textAlign: "center",
  },
  triggerTextDefault: {
    color: undefined, // Will be overridden by dynamic color
  },
  triggerTextSelected: {
    color: "#fff",
  },
  triggersContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 12,
  },


});