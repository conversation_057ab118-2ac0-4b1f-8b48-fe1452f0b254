import React from "react";
import { StyleSheet, Text, TouchableOpacity, Platform } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { LucideIcon } from "lucide-react-native";
import * as Haptics from "expo-haptics";
import { Colors } from "../types";

interface ActionButtonProps {
  onPress: () => void;
  title: string;
  icon?: LucideIcon;
  colors: Colors;
  variant?: "primary" | "secondary" | "danger";
  size?: "small" | "medium" | "large";
  disabled?: boolean;
}

export const ActionButton: React.FC<ActionButtonProps> = ({
  onPress,
  title,
  icon: Icon,
  colors,
  variant = "primary",
  size = "medium",
  disabled = false,
}) => {
  const handlePress = () => {
    if (disabled) return;

    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    onPress();
  };

  const getGradientColors = (): [string, string] => {
    switch (variant) {
      case "primary":
        return [colors.primary, colors.primaryDark || colors.primary];
      case "secondary":
        return [colors.secondary, colors.secondaryDark || colors.secondary];
      case "danger":
        return [colors.danger, colors.dangerDark || colors.danger];
      default:
        return [colors.primary, colors.primaryDark || colors.primary];
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case "small":
        return {
          padding: 12,
          fontSize: 14,
          iconSize: 16,
        };
      case "medium":
        return {
          padding: 16,
          fontSize: 16,
          iconSize: 20,
        };
      case "large":
        return {
          padding: 20,
          fontSize: 18,
          iconSize: 24,
        };
      default:
        return {
          padding: 16,
          fontSize: 16,
          iconSize: 20,
        };
    }
  };

  const sizeStyles = getSizeStyles();

  return (
    <TouchableOpacity
      style={[
        styles.button,
        disabled ? styles.buttonDisabled : styles.buttonEnabled,
      ]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={getGradientColors()}
        style={[styles.gradient, { padding: sizeStyles.padding }]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        {Icon && (
          <Icon size={sizeStyles.iconSize} color="#fff" strokeWidth={2.5} />
        )}
        <Text
          style={[
            styles.text,
            {
              fontSize: sizeStyles.fontSize,
            },
            Icon ? styles.textWithIcon : styles.textWithoutIcon,
          ]}
        >
          {title}
        </Text>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({


  button: {
    borderRadius: 16,
    elevation: 4,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonEnabled: {
    opacity: 1,
  },
  gradient: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
  },
  text: {
    color: "#fff",
    fontWeight: "700",
    letterSpacing: -0.3,
  },
  textWithIcon: {
    marginLeft: 8,
  },
  textWithoutIcon: {
    marginLeft: 0,
  },


});
