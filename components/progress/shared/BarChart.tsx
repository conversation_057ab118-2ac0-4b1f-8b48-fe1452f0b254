import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ColorScheme } from '@/hooks/ui/useColorScheme';
interface BarChartProps {
  data: number[];
  width: number;
  height: number;
  colors?: ColorScheme;
  language?: 'en' | 'nl';
  labels?: string[];
  maxValue?: number;
  barColor?: string;
}
export const BarChart: React.FC<BarChartProps> = ({ 
  data, 
  labels = [], 
  width, 
  height,
  colors,
  language = 'en',
  maxValue,
  barColor
}) => {
  // Default color if colors prop is not provided
  const primaryColor = barColor || (colors?.primary || '#3498db');
  
  if (!data || data.length === 0) {
    return (
      <View style={[styles.container, { width, height }]}>
        <Text style={styles.noDataText}>
          {language === 'nl' ? 'Geen gegevens beschikbaar' : 'No data available'}
        </Text>
      </View>
    );
  }
  
  const actualMaxValue = maxValue || Math.max(...data, 1);
  const barWidth = width / data.length - 10;
  
  return (
    <View style={[styles.container, { height, width }]}>
      <View style={styles.barsContainer}>
        {data.map((value, index) => {
          const barHeight = value === 0 ? 0 : (value / actualMaxValue) * (height - 30);
          
          return (
            <View key={index} style={styles.barWrapper}>
              <View 
                style={[
                  styles.bar, 
                  { 
                    height: barHeight, 
                    backgroundColor: primaryColor,
                    width: barWidth
                  }
                ]} 
              />
              {labels[index] && (
                <Text style={styles.label}>{labels[index]}</Text>
              )}
              <Text style={styles.value}>{value}</Text>
            </View>
          );
        })}
      </View>
    </View>
  );
};
const styles = StyleSheet.create({


  bar: {
    borderRadius: 5,
    width: 20,
  },
  barWrapper: {
    alignItems: 'center',
  },
  barsContainer: {
    alignItems: 'flex-end',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  container: {
    paddingBottom: 10,
  },
  label: {
    color: '#666',
    fontSize: 12,
    marginTop: 5,
  },
  noDataText: {
    color: '#666',
    marginTop: 50,
    textAlign: 'center',
  },
  value: {
    color: '#666',
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 5,
  },


});