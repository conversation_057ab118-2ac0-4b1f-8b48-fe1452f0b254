import React from "react";
import { StyleSheet, Text, View, ScrollView } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Calendar,
  BarChart3,
  Award,
  Activity
} from "lucide-react-native";
import { UserProfile } from "@/types/user";
import { HealthGoal } from "@/types/health";
import { Colors } from "../types";

interface ProgressInsightsProps {
  profile: UserProfile;
  colors: Colors;
  language: "en" | "nl";
}

interface InsightCard {
  id: string;
  title: string;
  value: string;
  trend: "up" | "down" | "stable";
  percentage: number;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
}

export const ProgressInsights: React.FC<ProgressInsightsProps> = ({
  profile,
  colors,
  language,
}) => {
  // Calculate insights from profile data
  const calculateInsights = (): InsightCard[] => {
    const insights: InsightCard[] = [];
    
    // Sobriety streak
    if (profile.sobrietyDate) {
      const daysSober = Math.floor(
        (Date.now() - new Date(profile.sobrietyDate).getTime()) / (1000 * 60 * 60 * 24)
      );
      insights.push({
        id: "sobriety",
        title: language === "nl" ? "Dagen Nuchter" : "Days Sober",
        value: daysSober.toString(),
        trend: "up",
        percentage: 100,
        icon: Target,
        color: colors.success,
      });
    }

    // Mood trend (last 7 days vs previous 7 days)
    if (profile.moodEntries && profile.moodEntries.length > 0) {
      const now = new Date();
      const last7Days = profile.moodEntries.filter(entry => 
        (now.getTime() - new Date(entry.date).getTime()) / (1000 * 60 * 60 * 24) <= 7
      );
      const previous7Days = profile.moodEntries.filter(entry => {
        const daysDiff = (now.getTime() - new Date(entry.date).getTime()) / (1000 * 60 * 60 * 24);
        return daysDiff > 7 && daysDiff <= 14;
      });

      if (last7Days.length > 0) {
        const avgMoodLast = last7Days.reduce((sum, entry) => sum + entry.mood, 0) / last7Days.length;
        const avgMoodPrevious = previous7Days.length > 0 
          ? previous7Days.reduce((sum, entry) => sum + entry.mood, 0) / previous7Days.length
          : avgMoodLast;
        
        const trendPercentage = previous7Days.length > 0 
          ? ((avgMoodLast - avgMoodPrevious) / avgMoodPrevious) * 100
          : 0;

        insights.push({
          id: "mood",
          title: language === "nl" ? "Gemiddelde Stemming" : "Average Mood",
          value: avgMoodLast.toFixed(1),
          trend: trendPercentage > 0 ? "up" : trendPercentage < 0 ? "down" : "stable",
          percentage: Math.abs(trendPercentage),
          icon: Activity,
          color: colors.primary,
        });
      }
    }

    // Health goals completion rate
    if (profile.healthGoals && profile.healthGoals.length > 0) {
      const enabledGoals = profile.healthGoals.filter((goal: HealthGoal) => goal.enabled);
      const completedToday = enabledGoals.filter((goal: HealthGoal) => {
        const today = new Date().toDateString();
        return goal.history?.some((entry: { date: string; value: number }) => 
          new Date(entry.date).toDateString() === today && 
          entry.value >= (goal.target || 0)
        );
      });

      const completionRate = enabledGoals.length > 0 
        ? (completedToday.length / enabledGoals.length) * 100 
        : 0;

      insights.push({
        id: "health",
        title: language === "nl" ? "Doelen Behaald" : "Goals Achieved",
        value: `${completedToday.length}/${enabledGoals.length}`,
        trend: completionRate >= 75 ? "up" : completionRate >= 50 ? "stable" : "down",
        percentage: completionRate,
        icon: Award,
        color: colors.info,
      });
    }

    // Weekly consistency
    const weeklyEntries = profile.moodEntries?.filter(entry => {
      const daysDiff = (Date.now() - new Date(entry.date).getTime()) / (1000 * 60 * 60 * 24);
      return daysDiff <= 7;
    }) || [];

    insights.push({
      id: "consistency",
      title: language === "nl" ? "Wekelijkse Consistentie" : "Weekly Consistency",
      value: `${weeklyEntries.length}/7`,
      trend: weeklyEntries.length >= 5 ? "up" : weeklyEntries.length >= 3 ? "stable" : "down",
      percentage: (weeklyEntries.length / 7) * 100,
      icon: Calendar,
      color: colors.warning,
    });

    return insights;
  };

  const insights = calculateInsights();

  const renderTrendIcon = (trend: "up" | "down" | "stable") => {
    switch (trend) {
      case "up":
        return <TrendingUp size={12} color={colors.success} />;
      case "down":
        return <TrendingDown size={12} color={colors.danger} />;
      default:
        return <BarChart3 size={12} color={colors.info} />;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          {language === "nl" ? "Voortgang Inzichten" : "Progress Insights"}
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          {language === "nl" ? "Je prestaties in een oogopslag" : "Your performance at a glance"}
        </Text>
      </View>

      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.cardsContainer}
        decelerationRate="fast"
        snapToInterval={162}
        snapToAlignment="start"
      >
        {insights.map((insight) => {
          const IconComponent = insight.icon;
          return (
            <View
              key={insight.id}
              style={[
                styles.insightCard,
                { backgroundColor: colors.card, borderColor: colors.border }
              ]}
            >
              <LinearGradient
                colors={[colors.card, colors.cardGradient || colors.card]}
                style={styles.cardGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <View style={styles.cardHeader}>
                  <View
                    style={[
                      styles.iconContainer,
                      { backgroundColor: insight.color + "20" }
                    ]}
                  >
                    <IconComponent size={16} color={insight.color} />
                  </View>
                  {renderTrendIcon(insight.trend)}
                </View>

                <Text style={[styles.cardTitle, { color: colors.text }]}>
                  {insight.title}
                </Text>

                <Text style={[styles.cardValue, { color: insight.color }]}>
                  {insight.value}
                </Text>

                {insight.percentage > 0 && (
                  <View style={styles.progressContainer}>
                    <View
                      style={[
                        styles.progressBar,
                        { backgroundColor: colors.border }
                      ]}
                    >
                      <View
                        style={[
                          styles.progressFill,
                          {
                            width: `${Math.min(insight.percentage, 100)}%`,
                            backgroundColor: insight.color,
                          }
                        ]}
                      />
                    </View>
                    <Text style={[styles.progressText, { color: colors.textSecondary }]}>
                      {insight.percentage.toFixed(0)}%
                    </Text>
                  </View>
                )}
              </LinearGradient>
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  cardGradient: {
    borderRadius: 12,
    padding: 12,
  },
  cardHeader: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  cardTitle: {
    fontSize: 12,
    fontWeight: "600",
    marginBottom: 6,
  },
  cardValue: {
    fontSize: 20,
    fontWeight: "700",
    marginBottom: 8,
  },
  cardsContainer: {
    paddingHorizontal: 16,
    paddingRight: 32,
  },
  container: {
    marginBottom: 16,
  },
  header: {
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  iconContainer: {
    alignItems: "center",
    borderRadius: 10,
    height: 28,
    justifyContent: "center",
    width: 28,
  },
  insightCard: {
    borderRadius: 12,
    borderWidth: 1,
    elevation: 3,
    marginRight: 12,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    width: 150,
  },
  progressBar: {
    borderRadius: 3,
    flex: 1,
    height: 6,
    overflow: "hidden",
  },
  progressContainer: {
    alignItems: "center",
    flexDirection: "row",
    gap: 6,
  },
  progressFill: {
    borderRadius: 3,
    height: "100%",
  },
  progressText: {
    fontSize: 11,
    fontWeight: "600",
    minWidth: 30,
  },
  subtitle: {
    fontSize: 13,
    lineHeight: 18,
  },
  title: {
    fontSize: 16,
    fontWeight: "700",
    marginBottom: 2,
  },
}); 