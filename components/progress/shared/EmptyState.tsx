import React from "react";
import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { LucideIcon, Plus } from "lucide-react-native";
import { Colors } from "../types";

interface EmptyStateProps {
  icon: LucideIcon;
  message: string;
  colors: Colors;
  isDashed?: boolean;
  actionText?: string;
  onAction?: () => void;
  motivationalText?: string;
  language?: "en" | "nl";
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon: Icon,
  message,
  colors,
  isDashed = true,
  actionText,
  onAction,
  motivationalText,
  language = "en",
}) => {
  const getMotivationalMessage = () => {
    if (motivationalText) return motivationalText;
    
    const messages = {
      en: [
        "Every journey begins with a single step",
        "Your progress matters, no matter how small",
        "Building healthy habits takes time",
        "You're stronger than you think",
      ],
      nl: [
        "Elke reis begint met een enkele stap",
        "<PERSON> vooruitgang telt, hoe klein ook",
        "Gezonde gewoonten opbouwen kost tijd",
        "Je bent sterker dan je denkt",
      ],
    };
    
    const randomIndex = Math.floor(Math.random() * messages[language].length);
    return messages[language][randomIndex];
  };

  return (
    <View
      style={[
        styles.emptyState,
        {
          borderColor: colors.border,
          backgroundColor: colors.card,
        },
        isDashed ? styles.emptyStateDashed : styles.emptyStateSolid,
      ]}
    >
      <LinearGradient
        colors={[
          colors.card || colors.background,
          colors.cardGradient || colors.card || colors.background,
        ]}
        style={styles.emptyStateGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View
          style={[
            styles.iconContainer,
            { backgroundColor: colors.muted + "20" },
          ]}
        >
          <Icon size={56} color={colors.muted} strokeWidth={1.5} />
        </View>
        
        <Text style={[styles.message, { color: colors.text }]}>{message}</Text>
        
        <Text style={[styles.motivationalText, { color: colors.textSecondary }]}>
          {getMotivationalMessage()}
        </Text>

        {actionText && onAction && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary }]}
            onPress={onAction}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={[colors.primary, colors.primaryDark || colors.primary]}
              style={styles.actionButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Plus size={20} color="#fff" strokeWidth={2.5} />
              <Text style={styles.actionButtonText}>{actionText}</Text>
            </LinearGradient>
          </TouchableOpacity>
        )}
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  actionButton: {
    borderRadius: 16,
    elevation: 4,
    marginTop: 20,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  actionButtonGradient: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 14,
  },
  actionButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  emptyState: {
    borderRadius: 24,
    borderWidth: 2,
    elevation: 4,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  emptyStateDashed: {
    borderStyle: "dashed",
  },
  emptyStateGradient: {
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 48,
  },
  emptyStateSolid: {
    borderStyle: "solid",
  },
  iconContainer: {
    alignItems: "center",
    borderRadius: 32,
    height: 96,
    justifyContent: "center",
    marginBottom: 20,
    width: 96,
  },
  message: {
    fontSize: 18,
    fontWeight: "600",
    letterSpacing: -0.2,
    lineHeight: 24,
    marginBottom: 12,
    textAlign: "center",
  },
  motivationalText: {
    fontSize: 14,
    fontStyle: "italic",
    lineHeight: 20,
    textAlign: "center",
  },
});
