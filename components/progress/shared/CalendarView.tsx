import React from "react";
import { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import {
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
} from "lucide-react-native";
interface Entry {
  id: string;
  date: Date;
  value: number;
  type: "mood" | "relapse" | "health";
  metric?: string;
}
interface CalendarViewProps {
  entries: Entry[];
  onDayPress: (date: Date, entries: Entry[]) => void;
  colors: {
    primary: string;
    text: string;
    muted: string;
    success: string;
    warning: string;
    danger: string;
  };
  language: "en" | "nl";
  _type: string;
}
export const CalendarView: React.FC<CalendarViewProps> = ({
  entries,
  onDayPress,
  colors,
  language,
  _type,
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };
  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };
  const getMonthName = (month: number) => {
    const monthNames =
      language === "nl"
        ? [
            "Januari",
            "Februari",
            "Maart",
            "April",
            "Mei",
            "Juni",
            "Juli",
            "Augustus",
            "September",
            "Oktober",
            "November",
            "December",
          ]
        : [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
          ];
    return monthNames[month];
  };
  const getDayName = (day: number) => {
    const dayNames =
      language === "nl"
        ? ["Zo", "Ma", "Di", "Wo", "Do", "Vr", "Za"]
        : ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    return dayNames[day];
  };
  const previousMonth = () => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() - 1);
    setCurrentMonth(newMonth);
  };
  const nextMonth = () => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + 1);
    setCurrentMonth(newMonth);
  };
  const renderCalendar = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDay = getFirstDayOfMonth(year, month);
    const days = [];
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<View key={`empty-${i}`} style={styles.dayCell} />);
    }
    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      // Find entries for this day
      const dayEntries = entries.filter((entry) => {
        const entryDate = new Date(entry.date);
        return (
          entryDate.getDate() === day &&
          entryDate.getMonth() === month &&
          entryDate.getFullYear() === year
        );
      });
      const hasMood = dayEntries.some((entry) => entry.type === "mood");
      const hasRelapse = dayEntries.some((entry) => entry.type === "relapse");
      const hasHealth = dayEntries.some((entry) => entry.type === "health");
      
      // Determine the mood value for coloring (if any)
      let moodValue = 0;
      if (hasMood) {
        const moodEntry = dayEntries.find((entry) => entry.type === "mood");
        if (moodEntry) {
          moodValue = moodEntry.value;
        }
      }
      // Determine if this is today
      const today = new Date();
      const isToday =
        today.getDate() === day &&
        today.getMonth() === month &&
        today.getFullYear() === year;
      days.push(
        <TouchableOpacity
          key={day}
          style={[
            styles.dayCell,
            isToday && [styles.todayCell, { borderColor: colors.primary }],
          ]}
          onPress={() => onDayPress(date, dayEntries)}
        >
          <Text style={[styles.dayText, { color: colors.text }]}>{day}</Text>
          <View style={styles.entryIndicators}>
            {hasMood && (
              <View
                style={[
                  styles.moodIndicator,
                  {
                    backgroundColor:
                      moodValue <= 2
                        ? colors.danger
                        : moodValue === 3
                        ? colors.warning
                        : colors.success,
                  },
                ]}
              />
            )}
            {hasRelapse && (
              <View
                style={[
                  styles.relapseIndicator,
                  { backgroundColor: colors.danger },
                ]}
              />
            )}
            {hasHealth && (
              <View
                style={[
                  styles.healthIndicator,
                  { backgroundColor: colors.primary },
                ]}
              />
            )}
          </View>
        </TouchableOpacity>
      );
    }
    return days;
  };
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={previousMonth} style={styles.navButton}>
          <ChevronLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <View style={styles.monthYearContainer}>
          <CalendarIcon
            size={20}
            color={colors.primary}
            style={styles.calendarIcon}
          />
          <Text style={[styles.monthYear, { color: colors.text }]}>
            {getMonthName(currentMonth.getMonth())} {currentMonth.getFullYear()}
          </Text>
        </View>
        <TouchableOpacity onPress={nextMonth} style={styles.navButton}>
          <ChevronRight size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>
      <View style={styles.daysOfWeek}>
        {[0, 1, 2, 3, 4, 5, 6].map((day) => (
          <Text key={day} style={[styles.dayOfWeek, { color: colors.muted }]}>
            {getDayName(day)}
          </Text>
        ))}
      </View>
      <View style={styles.calendarGrid}>{renderCalendar()}</View>
      <View style={styles.legend}>
        <Text style={[styles.legendTitle, { color: colors.text }]}>
          {language === "nl" ? "Legenda" : "Legend"}
        </Text>
        <View style={styles.legendItem}>
          <View
            style={[
              styles.legendIndicator,
              { backgroundColor: colors.success },
            ]}
          />
          <Text style={[styles.legendText, { color: colors.text }]}>
            {language === "nl" ? "Goede stemming" : "Good mood"}
          </Text>
        </View>
        <View style={styles.legendItem}>
          <View
            style={[
              styles.legendIndicator,
              { backgroundColor: colors.warning },
            ]}
          />
          <Text style={[styles.legendText, { color: colors.text }]}>
            {language === "nl" ? "Neutrale stemming" : "Neutral mood"}
          </Text>
        </View>
        <View style={styles.legendItem}>
          <View
            style={[styles.legendIndicator, { backgroundColor: colors.danger }]}
          />
          <Text style={[styles.legendText, { color: colors.text }]}>
            {language === "nl"
              ? "Slechte stemming / Terugval"
              : "Bad mood / Relapse"}
          </Text>
        </View>
        <View style={styles.legendItem}>
          <View
            style={[styles.legendIndicator, { backgroundColor: colors.primary }]}
          />
          <Text style={[styles.legendText, { color: colors.text }]}>
            {language === "nl"
              ? "Gezondheidsgegevens"
              : "Health data"}
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};
const styles = StyleSheet.create({


  calendarGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  calendarIcon: {
    marginRight: 8,
  },
  container: {
    flex: 1,
  },
  dayCell: {
    alignItems: "center",
    aspectRatio: 1,
    justifyContent: "center",
    padding: 4,
    width: "14.28%",
  },
  dayOfWeek: {
    flex: 1,
    fontSize: 12,
    fontWeight: "500",
    textAlign: "center",
  },
  dayText: {
    fontSize: 14,
    fontWeight: "500",
  },
  daysOfWeek: {
    flexDirection: "row",
    marginBottom: 8,
  },
  entryIndicators: {
    flexDirection: "row",
    marginTop: 4,
  },
  header: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  healthIndicator: {
    borderRadius: 4,
    height: 8,
    marginHorizontal: 1,
    width: 8,
  },
  legend: {
    borderTopColor: "#e5e5e5",
    borderTopWidth: 1,
    marginTop: 24,
    padding: 16,
  },
  legendIndicator: {
    borderRadius: 6,
    height: 12,
    marginRight: 8,
    width: 12,
  },
  legendItem: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: 8,
  },
  legendText: {
    fontSize: 14,
  },
  legendTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
  },
  monthYear: {
    fontSize: 18,
    fontWeight: "600",
  },
  monthYearContainer: {
    alignItems: "center",
    flexDirection: "row",
  },
  moodIndicator: {
    borderRadius: 4,
    height: 8,
    marginHorizontal: 1,
    width: 8,
  },
  navButton: {
    padding: 8,
  },
  relapseIndicator: {
    borderRadius: 4,
    height: 8,
    marginHorizontal: 1,
    width: 8,
  },
  todayCell: {
    borderRadius: 4,
    borderWidth: 1,
  },


});