import React from "react";
import { StyleSheet, Text, View, TouchableOpacity, Platform, ScrollView } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { 
  Plus,
  Smile,
  Activity,
  Target,
  TrendingUp
} from "lucide-react-native";
import * as Haptics from "expo-haptics";
import { Colors } from "../types";

interface QuickActionsProps {
  colors: Colors;
  language: "en" | "nl";
  onAddMood: () => void;
  onAddHealth: () => void;
  onAddRelapse: () => void;
  onViewMilestones: () => void;
}

interface QuickAction {
  id: string;
  title: string;
  subtitle: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
  onPress: () => void;
}

export const QuickActions: React.FC<QuickActionsProps> = ({
  colors,
  language,
  onAddMood,
  onAddHealth,
  onAddRelapse,
  onViewMilestones,
}) => {
  const actions: QuickAction[] = [
    {
      id: "mood",
      title: language === "nl" ? "Log Stemming" : "Log Mood",
      subtitle: language === "nl" ? "Hoe voel je je?" : "How are you feeling?",
      icon: Smile,
      color: colors.primary,
      onPress: onAddMood,
    },
    {
      id: "health",
      title: language === "nl" ? "Gezondheid" : "Health",
      subtitle: language === "nl" ? "Track doelen" : "Track goals",
      icon: Activity,
      color: colors.info,
      onPress: onAddHealth,
    },
    {
      id: "relapse",
      title: language === "nl" ? "Terugval" : "Relapse",
      subtitle: language === "nl" ? "Rapporteer incident" : "Report incident",
      icon: TrendingUp,
      color: colors.warning,
      onPress: onAddRelapse,
    },
    {
      id: "milestones",
      title: language === "nl" ? "Mijlpalen" : "Milestones",
      subtitle: language === "nl" ? "Bekijk prestaties" : "View achievements",
      icon: Target,
      color: colors.success,
      onPress: onViewMilestones,
    },
  ];

  const handleActionPress = (action: QuickAction) => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    action.onPress();
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          {language === "nl" ? "Snelle Acties" : "Quick Actions"}
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          {language === "nl" ? "Voeg snel nieuwe gegevens toe" : "Quickly add new data"}
        </Text>
      </View>

      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.actionsContainer}
        style={styles.actionsScrollView}
      >
        {actions.map((action) => {
          const IconComponent = action.icon;
          return (
            <TouchableOpacity
              key={action.id}
              style={[
                styles.actionCard,
                { backgroundColor: colors.card, borderColor: colors.border }
              ]}
              onPress={() => handleActionPress(action)}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={[colors.card, colors.cardGradient || colors.card]}
                style={styles.cardGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: action.color + "20" }
                  ]}
                >
                  <IconComponent size={20} color={action.color} />
                </View>

                <Text style={[styles.actionTitle, { color: colors.text }]}>
                  {action.title}
                </Text>

                <Text style={[styles.actionSubtitle, { color: colors.textSecondary }]}>
                  {action.subtitle}
                </Text>

                <View style={styles.addIconContainer}>
                  <Plus size={12} color={action.color} />
                </View>
              </LinearGradient>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  actionCard: {
    borderRadius: 12,
    borderWidth: 1,
    elevation: 2,
    marginRight: 12,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    width: 120,
  },
  actionSubtitle: {
    fontSize: 11,
    lineHeight: 14,
    textAlign: "center",
  },
  actionTitle: {
    fontSize: 13,
    fontWeight: "600",
    marginBottom: 2,
    textAlign: "center",
  },
  actionsContainer: {
    paddingHorizontal: 16,
    paddingRight: 32,
  },
  actionsScrollView: {
    marginBottom: 8,
  },
  addIconContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: 6,
  },
  cardGradient: {
    alignItems: "center",
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 12,
  },
  container: {
    marginBottom: 16,
  },
  header: {
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  iconContainer: {
    alignItems: "center",
    borderRadius: 12,
    height: 36,
    justifyContent: "center",
    marginBottom: 8,
    width: 36,
  },
  subtitle: {
    fontSize: 13,
    lineHeight: 18,
  },
  title: {
    fontSize: 16,
    fontWeight: "700",
    marginBottom: 2,
  },
}); 