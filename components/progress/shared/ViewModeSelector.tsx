import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { Colors, ViewMode, Language } from "../types";
import { getTranslation } from "../utils";
interface ViewModeSelectorProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  colors: Colors;
  language: Language;
  availableModes?: ViewMode[];
}
export const ViewModeSelector: React.FC<ViewModeSelectorProps> = ({
  viewMode,
  onViewModeChange,
  colors,
  language,
  availableModes = ["list", "calendar", "chart"],
}) => {
  return (
    <View
      style={[
        styles.container,
        { backgroundColor: colors.card, borderColor: colors.border },
      ]}
    >
      {availableModes.map((mode) => (
        <TouchableOpacity
          key={mode}
          style={[
            styles.button,
            viewMode === mode && [
              styles.activeButton,
              { backgroundColor: colors.primary + "20" },
            ],
          ]}
          onPress={() => onViewModeChange(mode)}
        >
          <Text
            style={[
              styles.buttonText,
              {
                color: viewMode === mode ? colors.primary : colors.muted,
              },
            ]}
          >
            {getTranslation(mode, language)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};
const styles = StyleSheet.create({


  activeButton: {
    borderRadius: 12,
    margin: 4,
  },
  button: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    paddingVertical: 12,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: "600",
    letterSpacing: -0.1,
  },
  container: {
    borderRadius: 16,
    borderWidth: 1,
    elevation: 2,
    flexDirection: "row",
    marginBottom: 20,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },


});