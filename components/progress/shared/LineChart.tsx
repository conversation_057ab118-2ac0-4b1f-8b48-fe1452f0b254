import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Svg, { Path, Line, Circle } from 'react-native-svg';
import { ColorScheme } from '@/hooks/ui/useColorScheme';
interface DataPoint {
  date: string;
  value: number;
  cravingValue?: number;
  id?: string;
  notes?: string;
}
interface LineChartProps {
  data: DataPoint[];
  width: number;
  height: number;
  colors?: ColorScheme;
  language?: 'en' | 'nl';
  maxValue?: number;
  lineColor?: string;
}
export const LineChart: React.FC<LineChartProps> = ({ 
  data, 
  width, 
  height,
  colors,
  language = 'en',
  maxValue,
  lineColor
}) => {
  // Default color if colors prop is not provided
  const primaryColor = lineColor || (colors?.primary || '#3498db');
  
  if (!data || data.length === 0) {
    return (
      <View style={[styles.container, { width, height }]}>
        <Text style={styles.noDataText}>
          {language === 'nl' ? '<PERSON><PERSON> g<PERSON><PERSON> be<PERSON>' : 'No data available'}
        </Text>
      </View>
    );
  }
  
  const actualMaxValue = maxValue || Math.max(...data.map(d => d.value), 1);
  const padding = { top: 20, right: 20, bottom: 30, left: 40 };
  
  const chartWidth = width - padding.left - padding.right;
  const chartHeight = height - padding.top - padding.bottom;
  
  // Generate path for the line
  const generatePath = () => {
    if (data.length === 0) return '';
    
    return data.map((point, index) => {
      const x = (index / (data.length - 1 || 1)) * chartWidth + padding.left;
      const y = chartHeight - (point.value / actualMaxValue) * chartHeight + padding.top;
      
      return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
    }).join(' ');
  };
  
  // Generate grid lines
  const generateGridLines = () => {
    const lines = [];
    const numLines = 5;
    
    for (let i = 0; i <= numLines; i++) {
      const y = chartHeight * (i / numLines) + padding.top;
      lines.push(
        <Line
          key={`grid-${i}`}
          x1={padding.left}
          y1={y}
          x2={width - padding.right}
          y2={y}
          stroke="#e5e5e5"
          strokeWidth={1}
          strokeDasharray="5,5"
        />
      );
      
      // Add y-axis labels
      const value = actualMaxValue - (i / numLines) * actualMaxValue;
      lines.push(
        <View
          key={`label-${i}`}
          style={[styles.yAxisLabelContainer, { top: y - 10 }]}
        >
          <Text style={[styles.axisLabel, styles.yAxisLabelText]}>
            {Math.round(value * 10) / 10}
          </Text>
        </View>
      );
    }
    
    return lines;
  };
  
  // Generate data points
  const generateDataPoints = () => {
    return data.map((point, index) => {
      const x = (index / (data.length - 1 || 1)) * chartWidth + padding.left;
      const y = chartHeight - (point.value / actualMaxValue) * chartHeight + padding.top;
      
      return (
        <Circle
          key={`point-${index}`}
          cx={x}
          cy={y}
          r={4}
          fill="#fff"
          stroke={primaryColor}
          strokeWidth={2}
        />
      );
    });
  };
  
  // Generate x-axis labels
  const generateXAxisLabels = () => {
    // Show only a subset of labels if there are too many
    const step = Math.max(1, Math.floor(data.length / 5));
    
    return data.map((point, index) => {
      if (index % step !== 0 && index !== data.length - 1) return null;
      
      const x = (index / (data.length - 1 || 1)) * chartWidth + padding.left;
      
      const date = new Date(point.date);
      const formattedDate = date.toLocaleDateString(
        language === 'nl' ? 'nl-NL' : 'en-US',
        { month: 'short', day: 'numeric' }
      );
      
      return (
        <View
          key={`x-label-${index}`}
          style={[styles.xAxisLabelContainer, { left: x - 20, top: height - padding.bottom + 5 }]}
        >
          <Text style={[styles.axisLabel, styles.xAxisLabelText]}>
            {formattedDate}
          </Text>
        </View>
      );
    });
  };
  
  return (
    <View style={[styles.container, { width, height }]}>
      {generateGridLines()}
      
      <Svg width={width} height={height}>
        <Path
          d={generatePath()}
          stroke={primaryColor}
          strokeWidth={2}
          fill="none"
        />
        {generateDataPoints()}
      </Svg>
      
      {generateXAxisLabels()}
    </View>
  );
};
const styles = StyleSheet.create({


  axisLabel: {
    color: '#666',
    fontSize: 10,
  },
  container: {
    position: 'relative',
  },
  noDataText: {
    color: '#666',
    marginTop: 50,
    textAlign: 'center',
  },
  xAxisLabelContainer: {
    position: 'absolute',
    width: 40,
  },
  xAxisLabelText: {
    textAlign: 'center',
  },
  yAxisLabelContainer: {
    left: 10,
    position: 'absolute',
    width: 30,
  },
  yAxisLabelText: {
    textAlign: 'right',
  },


});