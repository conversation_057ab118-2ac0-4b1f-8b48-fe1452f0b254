import { Colors, Language } from "./types";
// Date utilities
export const formatDate = (date: Date | string, language: Language): string => {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return dateObj.toLocaleDateString(language === "nl" ? "nl-NL" : "en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};
export const calculateDaysSober = (sobrietyDate: string): number => {
  const sobriety = new Date(sobrietyDate);
  const today = new Date();
  const diffTime = Math.abs(today.getTime() - sobriety.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};
// Mood utilities
export const getMoodEmoji = (value: number): string => {
  switch (value) {
    case 1:
      return "😞";
    case 2:
      return "😕";
    case 3:
      return "😐";
    case 4:
      return "🙂";
    case 5:
      return "😄";
    default:
      return "😐";
  }
};
// ✅ MIGRATED: Use translationService.getMoodLabel() instead
export const getMoodLabel = (value: number, language: Language): string => {
  // This function is deprecated - use translationService.getMoodLabel() or useTranslation().getMoodLabel()
  console.warn('getMoodLabel with language parameter is deprecated. Use translationService.getMoodLabel() instead.');
  
  if (language === "nl") {
    switch (value) {
      case 1: return "Zeer slecht";
      case 2: return "Slecht";
      case 3: return "Neutraal";
      case 4: return "Goed";
      case 5: return "Zeer goed";
      default: return "Neutraal";
    }
  } else {
    switch (value) {
      case 1: return "Very Bad";
      case 2: return "Bad";
      case 3: return "Neutral";
      case 4: return "Good";
      case 5: return "Very Good";
      default: return "Neutral";
    }
  }
};
export const getMoodColor = (value: number, colors: Colors): string => {
  switch (value) {
    case 1:
      return colors.danger;
    case 2:
      return colors.warning;
    case 3:
      return colors.info;
    case 4:
      return colors.success;
    case 5:
      return colors.primary;
    default:
      return colors.info;
  }
};
// ✅ MIGRATED: Use translationService.getCravingLabel() instead
export const getCravingLabel = (value: number, language: Language): string => {
  // This function is deprecated - use translationService.getCravingLabel() or useTranslation().getCravingLabel()
  console.warn('getCravingLabel with language parameter is deprecated. Use translationService.getCravingLabel() instead.');
  
  if (language === "nl") {
    switch (value) {
      case 1: return "Geen";
      case 2: return "Mild";
      case 3: return "Matig";
      case 4: return "Sterk";
      case 5: return "Zeer sterk";
      default: return "Geen";
    }
  } else {
    switch (value) {
      case 1: return "None";
      case 2: return "Mild";
      case 3: return "Moderate";
      case 4: return "Strong";
      case 5: return "Very Strong";
      default: return "None";
    }
  }
};
export const getCravingColor = (value: number, colors: Colors): string => {
  switch (value) {
    case 1:
      return colors.success;
    case 2:
      return colors.info;
    case 3:
      return colors.warning;
    case 4:
      return colors.danger;
    case 5:
      return "#8B0000"; // Dark red
    default:
      return colors.success;
  }
};
// Health utilities
export const getHealthMetricEmoji = (metric: string, value: number): string => {
  switch (metric) {
    case "sleep":
      return value >= 7 ? "😴" : "😩";
    case "water":
    case "hydration":
      return value >= 6 ? "💧" : "🥤";
    case "exercise":
      return value >= 30 ? "💪" : "🛋️";
    case "pills":
      return value <= 2 ? "💊" : "⚠️";
    case "mindfulness":
      return value >= 10 ? "🧘" : "🤯";
    default:
      return "✨";
  }
};
// ✅ MIGRATED: Use translationService.getHealthMetricLabel() instead
export const getHealthMetricLabel = (
  metric: string,
  value: number,
  language: Language
): string => {
  // This function is deprecated - use translationService.getHealthMetricLabel() or useTranslation().getHealthMetricLabel()
  console.warn('getHealthMetricLabel with language parameter is deprecated. Use translationService.getHealthMetricLabel() instead.');
  
  if (language === "nl") {
    switch (metric) {
      case "sleep": return `${value} uur slaap`;
      case "water": return `${value} ml water`;
      case "hydration": return `${value} glazen water`;
      case "exercise": return `${value} minuten beweging`;
      case "pills": return `${value} pillen`;
      case "mindfulness": return `${value} minuten mindfulness`;
      default: return `${value}`;
    }
  } else {
    switch (metric) {
      case "sleep": return `${value} hours of sleep`;
      case "water": return `${value} ml water`;
      case "hydration": return `${value} glasses of water`;
      case "exercise": return `${value} minutes of exercise`;
      case "pills": return `${value} pills`;
      case "mindfulness": return `${value} minutes of mindfulness`;
      default: return `${value}`;
    }
  }
};
export const getHealthMetricColor = (
  metric: string,
  value: number,
  colors: Colors
): string => {
  switch (metric) {
    case "sleep":
      return value >= 7 ? colors.success : colors.warning;
    case "water":
    case "hydration":
      return value >= 6 ? colors.success : colors.warning;
    case "exercise":
      return value >= 30 ? colors.success : colors.warning;
    case "pills":
      return value <= 2 ? colors.success : colors.danger;
    case "mindfulness":
      return value >= 10 ? colors.success : colors.warning;
    default:
      return colors.info;
  }
};
// Usage/Savings utilities
export const calculateSavings = (
  profile: { usageCost?: number; costFrequency?: string },
  days: number
): number => {
  if (!profile?.usageCost || !profile?.costFrequency) return 0;
  let dailyCost = 0;
  switch (profile.costFrequency) {
    case "daily":
      dailyCost = profile.usageCost;
      break;
    case "weekly":
      dailyCost = profile.usageCost / 7;
      break;
    case "monthly":
      dailyCost = profile.usageCost / 30;
      break;
    case "yearly":
      dailyCost = profile.usageCost / 365;
      break;
    default:
      dailyCost = 0;
  }
  return dailyCost * days;
};
export const calculateMonthlySavings = (profile: {
  usageCost?: number;
  costFrequency?: string;
}): number => {
  if (!profile?.usageCost || !profile?.costFrequency) return 0;
  switch (profile.costFrequency) {
    case "daily":
      return profile.usageCost * 30;
    case "weekly":
      return profile.usageCost * 4.3;
    case "monthly":
      return profile.usageCost;
    case "yearly":
      return profile.usageCost / 12;
    default:
      return 0;
  }
};
// Data sorting utilities
export const sortEntriesByDate = <T extends { date: string }>(
  entries: T[],
  ascending = false
): T[] => {
  return [...entries].sort((a, b) => {
    const dateA = new Date(a.date).getTime();
    const dateB = new Date(b.date).getTime();
    return ascending ? dateA - dateB : dateB - dateA;
  });
};
// ✅ MIGRATED: Use translationService.getCommonTriggers() instead
export const getCommonTriggers = (language: Language): string[] => {
  // This function is deprecated - use translationService.getCommonTriggers() or useTranslation().getCommonTriggers()
  console.warn('getCommonTriggers with language parameter is deprecated. Use translationService.getCommonTriggers() instead.');
  
  return language === "nl"
    ? [
        "Stress",
        "Negatieve emoties",
        "Sociale druk",
        "Viering",
        "Verveling",
        "Beschikbaarheid",
        "Conflict",
        "Verlangen",
      ]
    : [
        "Stress",
        "Negative emotions",
        "Social pressure",
        "Celebration",
        "Boredom",
        "Availability",
        "Conflict",
        "Cravings",
      ];
};
// ✅ MIGRATED: Use translationService.translate() instead
export const getTranslation = (key: string, language: Language): string => {
  // This function is deprecated - use translationService.translate() or useTranslation().t()
  console.warn('getTranslation with language parameter is deprecated. Use translationService.translate() instead.');
  
  const translations: Record<string, Record<Language, string>> = {
    // View modes
    list: { en: "List", nl: "Lijst" },
    calendar: { en: "Calendar", nl: "Kalender" },
    chart: { en: "Chart", nl: "Grafiek" },
    // Common actions
    save: { en: "Save", nl: "Opslaan" },
    cancel: { en: "Cancel", nl: "Annuleren" },
    edit: { en: "Edit", nl: "Bewerken" },
    delete: { en: "Delete", nl: "Verwijderen" },
    add: { en: "Add", nl: "Toevoegen" },
    // Common labels
    date: { en: "Date", nl: "Datum" },
    notes: { en: "Notes", nl: "Notities" },
    mood: { en: "Mood", nl: "Stemming" },
    craving: { en: "Craving", nl: "Verlangen" },
    triggers: { en: "Triggers", nl: "Triggers" },
    // Empty states
    noMoodEntries: {
      en: "No mood entries yet. Add your first one!",
      nl: "Nog geen stemmingen gelogd. Voeg je eerste toe!",
    },
    noRelapses: {
      en: "No relapses recorded. Stay strong!",
      nl: "Geen terugvallen geregistreerd. Blijf sterk!",
    },
    noHealthData: {
      en: "No health data. Add your first health metric entry.",
      nl: "Geen gezondheidsgegevens. Voeg je eerste gezondheidsmeting toe.",
    },
  };
  return translations[key]?.[language] || key;
};