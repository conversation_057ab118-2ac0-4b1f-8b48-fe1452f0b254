import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BaseTabProps } from "./types";
import { calculateDaysSober, calculateSavings, calculateMonthlySavings } from "./utils";
import { getSubstanceIcon } from "@/utils";

type UsageTabProps = BaseTabProps;

export const UsageTab: React.FC<UsageTabProps> = ({
  profile,
  colors,
  language,
}) => {
  // Calculate days sober
  const diffDays = profile?.sobrietyDate
    ? calculateDaysSober(profile.sobrietyDate)
    : 0;

  return (
    <View style={styles.usageContainer}>
      <View
        style={[
          styles.usageCard,
          { backgroundColor: colors.card, borderColor: colors.border },
        ]}
      >
        <LinearGradient
          colors={
            [
              colors.card || colors.background,
              colors.cardGradient || colors.card || colors.background,
            ] as const
          }
          style={styles.usageGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.usageHeader}>
            <View
              style={[
                styles.usageIconContainer,
                { backgroundColor: colors.primary + "20" },
              ]}
            >
              {getSubstanceIcon(profile?.substanceType, {
                size: 32,
                color: colors.primary,
                strokeWidth: 2,
              })}
            </View>
            <Text style={[styles.usageTitle, { color: colors.text }]}>
              {language === "nl" ? "Gebruiksgegevens" : "Usage Information"}
            </Text>
          </View>

          <View style={styles.usageDetailsContainer}>
            {profile?.substanceType && (
              <View style={styles.usageDetailItem}>
                <Text
                  style={[styles.usageDetailLabel, { color: colors.muted }]}
                >
                  {language === "nl" ? "Type:" : "Type:"}
                </Text>
                <Text style={[styles.usageDetailValue, { color: colors.text }]}>
                  {profile.substanceType}
                </Text>
              </View>
            )}

            {profile?.usageAmount &&
              profile.usageAmount !== "Not specified" && (
                <View style={styles.usageDetailItem}>
                  <Text
                    style={[styles.usageDetailLabel, { color: colors.muted }]}
                  >
                    {language === "nl" ? "Hoeveelheid:" : "Amount:"}
                  </Text>
                  <Text
                    style={[styles.usageDetailValue, { color: colors.text }]}
                  >
                    {profile.usageAmount}
                  </Text>
                </View>
              )}

            {profile?.costFrequency && (
              <View style={styles.usageDetailItem}>
                <Text
                  style={[styles.usageDetailLabel, { color: colors.muted }]}
                >
                  {language === "nl" ? "Frequentie:" : "Frequency:"}
                </Text>
                <Text style={[styles.usageDetailValue, { color: colors.text }]}>
                  {profile.costFrequency === "daily"
                    ? language === "nl"
                      ? "Dagelijks"
                      : "Daily"
                    : profile.costFrequency === "weekly"
                    ? language === "nl"
                      ? "Wekelijks"
                      : "Weekly"
                    : profile.costFrequency === "monthly"
                    ? language === "nl"
                      ? "Maandelijks"
                      : "Monthly"
                    : profile.costFrequency}
                </Text>
              </View>
            )}

            {profile?.usageCost && (
              <View style={styles.usageDetailItem}>
                <Text
                  style={[styles.usageDetailLabel, { color: colors.muted }]}
                >
                  {language === "nl" ? "Kosten:" : "Cost:"}
                </Text>
                <Text style={[styles.usageDetailValue, { color: colors.text }]}>
                  {profile.currency === "EUR" ? "€" : "$"}
                  {profile.usageCost}
                  {profile.costFrequency === "daily"
                    ? language === "nl"
                      ? " per dag"
                      : " per day"
                    : profile.costFrequency === "weekly"
                    ? language === "nl"
                      ? " per week"
                      : " per week"
                    : profile.costFrequency === "monthly"
                    ? language === "nl"
                      ? " per maand"
                      : " per month"
                    : ""}
                </Text>
              </View>
            )}
          </View>

          <View
            style={[
              styles.savingsSummary,
              { backgroundColor: colors.success + "15" },
            ]}
          >
            <Text
              style={[styles.savingsSummaryTitle, { color: colors.success }]}
            >
              {language === "nl"
                ? "Besparingen Samenvatting"
                : "Savings Summary"}
            </Text>

            <View style={styles.savingsRow}>
              <Text style={[styles.savingsLabel, { color: colors.text }]}>
                {language === "nl" ? "Dagen nuchter:" : "Days sober:"}
              </Text>
              <Text style={[styles.savingsValue, { color: colors.text }]}>
                {diffDays}
              </Text>
            </View>

            <View style={styles.savingsRow}>
              <Text style={[styles.savingsLabel, { color: colors.text }]}>
                {language === "nl" ? "Totaal bespaard:" : "Total saved:"}
              </Text>
              <Text style={[styles.savingsValue, { color: colors.text }]}>
                {profile?.currency === "EUR" ? "€" : "$"}
                {profile
                  ? calculateSavings(profile, diffDays).toFixed(2)
                  : "0.00"}
              </Text>
            </View>

            <View style={styles.savingsRow}>
              <Text style={[styles.savingsLabel, { color: colors.text }]}>
                {language === "nl"
                  ? "Maandelijkse besparing:"
                  : "Monthly saving:"}
              </Text>
              <Text style={[styles.savingsValue, { color: colors.text }]}>
                {profile?.currency === "EUR" ? "€" : "$"}
                {profile ? calculateMonthlySavings(profile).toFixed(2) : "0.00"}
              </Text>
            </View>

            <View style={styles.savingsRow}>
              <Text style={[styles.savingsLabel, { color: colors.text }]}>
                {language === "nl" ? "Jaarlijkse besparing:" : "Yearly saving:"}
              </Text>
              <Text style={[styles.savingsValue, { color: colors.text }]}>
                {profile?.currency === "EUR" ? "€" : "$"}
                {profile
                  ? (calculateMonthlySavings(profile) * 12).toFixed(2)
                  : "0.00"}
              </Text>
            </View>
          </View>
        </LinearGradient>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({


  savingsLabel: {
    fontSize: 15,
    fontWeight: "500",
  },
  savingsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 10,
  },
  savingsSummary: {
    borderRadius: 16,
    padding: 20,
  },
  savingsSummaryTitle: {
    fontSize: 18,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginBottom: 16,
    textAlign: "center",
  },
  savingsValue: {
    fontSize: 15,
    fontWeight: "700",
    letterSpacing: -0.2,
  },
  usageCard: {
    borderRadius: 20,
    borderWidth: 1,
    elevation: 4,
    marginBottom: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  usageContainer: {
    marginBottom: 16,
  },
  usageDetailItem: {
    borderBottomColor: "rgba(0,0,0,0.05)",
    borderBottomWidth: 1,
    flexDirection: "row",
    marginBottom: 12,
    paddingBottom: 12,
  },
  usageDetailLabel: {
    fontSize: 16,
    fontWeight: "500",
    width: 100,
  },
  usageDetailValue: {
    flex: 1,
    fontSize: 16,
    fontWeight: "600",
    letterSpacing: -0.2,
  },
  usageDetailsContainer: {
    marginBottom: 20,
  },
  usageGradient: {
    padding: 20,
  },
  usageHeader: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: 20,
  },
  usageIconContainer: {
    alignItems: "center",
    borderRadius: 32,
    height: 64,
    justifyContent: "center",
    marginRight: 16,
    width: 64,
  },
  usageTitle: {
    fontSize: 24,
    fontWeight: "800",
    letterSpacing: -0.5,
  },


});
