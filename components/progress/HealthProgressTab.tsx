import React from "react";
import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { LineChart } from "./shared/LineChart";
import { CalendarView } from "./shared/CalendarView";
import { Activity } from "lucide-react-native";
import { DataPoint, CalendarEntry, Colors, Language, ViewMode } from "./types";
import { EmptyState } from "./shared/EmptyState";

interface HealthProgressTabProps {
  healthData: DataPoint[];
  healthDataByMetric?: Record<string, DataPoint[]>;
  healthCalendarEntries: CalendarEntry[];
  handleEditHealth: (id: string) => void;
  handleCalendarDayPress: (date: Date, entries: CalendarEntry[]) => void;
  viewMode: ViewMode;
  screenWidth: number;
  colors: Colors;
  language: Language;
  getHealthMetricEmoji: (metric: string, value: number) => string;
  getHealthMetricLabel: (
    metric: string,
    value: number,
    language: Language
  ) => string;
  getHealthMetricColor: (
    metric: string,
    value: number,
    colors: Colors
  ) => string;
}

export const HealthProgressTab: React.FC<HealthProgressTabProps> = ({
  healthData,
  healthDataByMetric,
  healthCalendarEntries,
  handleEditHealth,
  handleCalendarDayPress,
  viewMode,
  screenWidth,
  colors,
  language,
  getHealthMetricEmoji,
  getHealthMetricLabel,
  getHealthMetricColor,
}) => {
  return (
    <View>
      {viewMode === "chart" && healthData.length > 0 && (
        <View>
          {healthDataByMetric && Object.keys(healthDataByMetric).length > 0 ? (
            // Render separate charts for each health metric
            Object.entries(healthDataByMetric).map(([metricType, metricData]) => {
              if (metricData.length === 0) return null;
              
              const getMetricTitle = (metric: string) => {
                if (language === "nl") {
                  switch (metric) {
                    case "sleep": return "Slaap";
                    case "exercise": return "Beweging";
                    case "hydration": return "Hydratatie";
                    case "pills": return "Pillen";
                    default: return metric;
                  }
                } else {
                  switch (metric) {
                    case "sleep": return "Sleep";
                    case "exercise": return "Exercise";
                    case "hydration": return "Hydration";
                    case "pills": return "Pills";
                    default: return metric;
                  }
                }
              };

              const getMetricColor = (metric: string) => {
                switch (metric) {
                  case "sleep": return colors.primary;
                  case "exercise": return colors.success;
                  case "hydration": return colors.info;
                  case "pills": return colors.warning;
                  default: return colors.primary;
                }
              };

              const getMaxValue = (metric: string) => {
                switch (metric) {
                  case "sleep": return 12;
                  case "exercise": return 120;
                  case "hydration": return 12;
                  case "pills": return 10;
                  default: return Math.max(...metricData.map(d => d.value), 1);
                }
              };

              return (
                <View
                  key={metricType}
                  style={[
                    styles.chartContainer,
                    { backgroundColor: colors.card, borderColor: colors.border },
                  ]}
                >
                  <LinearGradient
                    colors={
                      [
                        colors.card || colors.background,
                        colors.cardGradient || colors.card || colors.background,
                      ] as const
                    }
                    style={styles.chartGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    <Text style={[styles.chartTitle, { color: colors.text }]}>
                      {getMetricTitle(metricType)} {language === "nl" ? "Trend" : "Trend"}
                    </Text>
                    <LineChart
                      data={metricData}
                      width={screenWidth - 40}
                      height={200}
                      colors={colors}
                      language={language}
                      lineColor={getMetricColor(metricType)}
                      maxValue={getMaxValue(metricType)}
                    />
                  </LinearGradient>
                </View>
              );
            })
          ) : (
            // Fallback to single chart if grouped data is not available
            <View
              style={[
                styles.chartContainer,
                { backgroundColor: colors.card, borderColor: colors.border },
              ]}
            >
              <LinearGradient
                colors={
                  [
                    colors.card || colors.background,
                    colors.cardGradient || colors.card || colors.background,
                  ] as const
                }
                style={styles.chartGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Text style={[styles.chartTitle, { color: colors.text }]}>
                  {language === "nl" ? "Gezondheidsvoortgang" : "Health Progress"}
                </Text>
                <LineChart
                  data={healthData}
                  width={screenWidth - 40}
                  height={220}
                  colors={colors}
                  language={language}
                />
              </LinearGradient>
            </View>
          )}
        </View>
      )}

      {viewMode === "calendar" && (
        <View
          style={[
            styles.calendarContainer,
            { backgroundColor: colors.card, borderColor: colors.border },
          ]}
        >
          <LinearGradient
            colors={
              [
                colors.card || colors.background,
                colors.cardGradient || colors.card || colors.background,
              ] as const
            }
            style={styles.chartGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Text style={[styles.chartTitle, { color: colors.text }]}>
              {language === "nl" ? "Gezondheidskalender" : "Health Calendar"}
            </Text>
            <CalendarView
              entries={healthCalendarEntries}
              colors={colors}
              language={language}
              _type="health"
              onDayPress={handleCalendarDayPress}
            />
          </LinearGradient>
        </View>
      )}

      {viewMode === "list" && (
        <View style={styles.listContainer}>
          <Text style={[styles.listTitle, { color: colors.text }]}>
            {language === "nl" ? "Gezondheidslog" : "Health Log"}
          </Text>

          {healthData.length === 0 ? (
            <EmptyState
              icon={Activity}
              message={
                language === "nl"
                  ? "Geen gezondheidsgegevens. Voeg je eerste gezondheidsmeting toe."
                  : "No health data. Add your first health metric entry."
              }
              colors={colors}
            />
          ) : (
            healthData
              .sort(
                (a: DataPoint, b: DataPoint) =>
                  new Date(b.date).getTime() - new Date(a.date).getTime()
              )
              .map((entry: DataPoint) => (
                <TouchableOpacity
                  key={entry.id}
                  style={[
                    styles.entryItem,
                    {
                      borderBottomColor: colors.border,
                      backgroundColor: colors.card,
                    },
                  ]}
                  onPress={() => handleEditHealth(entry.id)}
                >
                  <LinearGradient
                    colors={
                      [
                        colors.card || colors.background,
                        colors.cardGradient || colors.card || colors.background,
                      ] as const
                    }
                    style={styles.entryGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    <View style={styles.entryHeader}>
                      <Text style={[styles.entryDate, { color: colors.text }]}>
                        {new Date(entry.date).toLocaleDateString(
                          language === "nl" ? "nl-NL" : "en-US",
                          {
                            year: "numeric",
                            month: "short",
                            day: "numeric",
                          }
                        )}
                      </Text>
                      <View
                        style={[
                          styles.metricIndicator,
                          {
                            backgroundColor: getHealthMetricColor(
                              entry.metric || "",
                              entry.value,
                              colors
                            ),
                          },
                        ]}
                      >
                        <Text style={styles.metricValue}>
                          {getHealthMetricEmoji(
                            entry.metric || "",
                            entry.value
                          )}
                        </Text>
                      </View>
                    </View>

                    {entry.notes && (
                      <Text
                        style={[styles.entryNotes, { color: colors.muted }]}
                      >
                        {entry.notes}
                      </Text>
                    )}

                    <View style={styles.entryFooter}>
                      <Text
                        style={[styles.entryDetail, { color: colors.muted }]}
                      >
                        {language === "nl" ? "Metriek: " : "Metric: "}
                        <Text style={{ color: colors.text }}>
                          {entry.metric}
                        </Text>
                      </Text>
                      <Text
                        style={[styles.entryDetail, { color: colors.muted }]}
                      >
                        {language === "nl" ? "Waarde: " : "Value: "}
                        <Text style={{ color: colors.text }}>
                          {getHealthMetricLabel(
                            entry.metric || "",
                            entry.value,
                            language
                          )}
                        </Text>
                      </Text>
                    </View>
                  </LinearGradient>
                </TouchableOpacity>
              ))
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({


  calendarContainer: {
    borderRadius: 20,
    borderWidth: 1,
    elevation: 4,
    marginBottom: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  chartContainer: {
    borderRadius: 20,
    borderWidth: 1,
    elevation: 4,
    marginBottom: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  chartGradient: {
    padding: 20,
  },
  chartTitle: {
    fontSize: 20,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginBottom: 16,
  },
  entryDate: {
    fontSize: 16,
    fontWeight: "600",
    letterSpacing: -0.2,
  },
  entryDetail: {
    fontSize: 14,
    fontWeight: "500",
  },
  entryFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  entryGradient: {
    padding: 16,
  },
  entryHeader: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  entryItem: {
    borderBottomWidth: 1,
    borderRadius: 16,
    elevation: 2,
    marginBottom: 12,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  entryNotes: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  listContainer: {
    marginBottom: 16,
  },
  listTitle: {
    fontSize: 20,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginBottom: 16,
  },
  metricIndicator: {
    alignItems: "center",
    borderRadius: 18,
    height: 36,
    justifyContent: "center",
    width: 36,
  },
  metricValue: {
    fontSize: 18,
  },


});
