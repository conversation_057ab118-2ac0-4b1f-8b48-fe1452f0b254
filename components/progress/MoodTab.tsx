import React from "react";
import { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Animated,
  Dimensions,
  Platform,
} from "react-native";
import {
  Plus<PERSON>ircle,
  Meh,
  Frown,
  Smile,
  ChevronUp,
  ChevronDown,
  Edit2,
  Trash2,
  Info,
  Activity,
  TrendingUp,
} from "lucide-react-native";
import * as Haptics from "expo-haptics";
import { LineChart } from "./shared/LineChart";
import { CalendarView } from "./shared/CalendarView";
import { MoodForm } from "./MoodForm";
import { LinearGradient } from "expo-linear-gradient";
import { MoodEntry, Colors, ViewMode, DataPoint, UserProfile } from "./types";
import { EmptyState } from "./shared/EmptyState";
const { width } = Dimensions.get("window");
interface MoodTabProps {
  profile: UserProfile | null;
  colors: Colors;
  fadeAnim: Animated.Value;
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode) => void;
  moodChartData: DataPoint[];
  cravingChartData: DataPoint[];
  handleEditMood: (id: string) => void;
  handleDeleteMood: (id: string) => void;
}
export const MoodTab: React.FC<MoodTabProps> = ({
  profile,
  colors,
  fadeAnim,
  viewMode,
  setViewMode,
  moodChartData,
  cravingChartData,
  handleEditMood,
  handleDeleteMood,
}) => {
  const [showMoodForm, setShowMoodForm] = useState(false);
  const [expandedMoodId, setExpandedMoodId] = useState<string | null>(null);
  if (!profile) {
    return (
      <EmptyState
        icon={Activity}
        message="No profile available"
        colors={colors}
      />
    );
  }
  const toggleMoodExpand = (id: string) => {
    if (expandedMoodId === id) {
      setExpandedMoodId(null);
    } else {
      setExpandedMoodId(id);
    }
  };
  const renderMoodIcon = (value: number, selected: number) => {
    const size = 24;
    const color = value === selected ? colors.primary : colors.muted;
    if (value <= 2) {
      return <Frown size={size} color={color} strokeWidth={2.5} />;
    } else if (value === 3) {
      return <Meh size={size} color={color} strokeWidth={2.5} />;
    } else {
      return <Smile size={size} color={color} strokeWidth={2.5} />;
    }
  };
  const getViewModeTitle = (mode: "list" | "calendar" | "chart") => {
    switch (mode) {
      case "list":
        return profile.language === "nl" ? "Lijst" : "List";
      case "calendar":
        return profile.language === "nl" ? "Kalender" : "Calendar";
      case "chart":
        return profile.language === "nl" ? "Grafiek" : "Chart";
    }
  };
  return (
    <Animated.View style={[styles.tabContent, { opacity: fadeAnim }]}>
      <View
        style={[
          styles.viewModeContainer,
          { backgroundColor: colors.card, borderColor: colors.border },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.viewModeButton,
            viewMode === "list" && [
              styles.activeViewModeButton,
              { backgroundColor: colors.primary + "20" },
            ],
          ]}
          onPress={() => setViewMode("list")}
        >
          <Text
            style={[
              styles.viewModeText,
              { color: viewMode === "list" ? colors.primary : colors.muted },
            ]}
          >
            {getViewModeTitle("list")}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.viewModeButton,
            viewMode === "calendar" && [
              styles.activeViewModeButton,
              { backgroundColor: colors.primary + "20" },
            ],
          ]}
          onPress={() => setViewMode("calendar")}
        >
          <Text
            style={[
              styles.viewModeText,
              {
                color: viewMode === "calendar" ? colors.primary : colors.muted,
              },
            ]}
          >
            {getViewModeTitle("calendar")}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.viewModeButton,
            viewMode === "chart" && [
              styles.activeViewModeButton,
              { backgroundColor: colors.primary + "20" },
            ],
          ]}
          onPress={() => setViewMode("chart")}
        >
          <Text
            style={[
              styles.viewModeText,
              { color: viewMode === "chart" ? colors.primary : colors.muted },
            ]}
          >
            {getViewModeTitle("chart")}
          </Text>
        </TouchableOpacity>
      </View>
      {!showMoodForm ? (
        <>
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              setShowMoodForm(true);
              if (Platform.OS !== "web") {
                Haptics.selectionAsync();
              }
            }}
          >
            <LinearGradient
              colors={
                [colors.primary, colors.primaryDark || colors.primary] as const
              }
              style={styles.addButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <PlusCircle size={20} color="#fff" strokeWidth={2.5} />
              <Text style={[styles.addButtonText, styles.addButtonTextSpacing]}>
                {profile.language === "nl"
                  ? "Nieuwe stemming toevoegen"
                  : "Add New Mood Entry"}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
          {(profile.moodEntries?.length || 0) === 0 ? (
            <View
              style={[
                styles.emptyState,
                { borderColor: colors.border, backgroundColor: colors.card },
              ]}
            >
              <LinearGradient
                colors={
                  [
                    colors.card || colors.background,
                    colors.cardGradient || colors.card || colors.background,
                  ] as const
                }
                style={styles.emptyStateGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <View
                  style={[
                    styles.emptyIconContainer,
                    { backgroundColor: colors.muted + "20" },
                  ]}
                >
                  <Activity size={56} color={colors.muted} strokeWidth={1.5} />
                </View>
                <Text style={[styles.emptyStateText, { color: colors.text }]}>
                  {profile.language === "nl"
                    ? "Nog geen stemmingen gelogd. Voeg je eerste toe!"
                    : "No mood entries yet. Add your first one!"}
                </Text>
              </LinearGradient>
            </View>
          ) : viewMode === "list" ? (
            <View style={styles.entriesList}>
              {[...(profile.moodEntries || [])]
                .sort(
                  (a, b) =>
                    new Date(b.date).getTime() - new Date(a.date).getTime()
                )
                .map((entry: MoodEntry) => (
                  <View
                    key={entry.id}
                    style={[
                      styles.entryCard,
                      {
                        backgroundColor: colors.card,
                        borderColor: colors.border,
                      },
                    ]}
                  >
                    <LinearGradient
                      colors={
                        [
                          colors.card || colors.background,
                          colors.cardGradient ||
                            colors.card ||
                            colors.background,
                        ] as const
                      }
                      style={styles.entryGradient}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 1 }}
                    >
                      <TouchableOpacity
                        style={styles.entryHeader}
                        onPress={() => toggleMoodExpand(entry.id)}
                      >
                        <Text
                          style={[styles.entryDate, { color: colors.text }]}
                        >
                          {new Date(entry.date).toLocaleDateString(
                            profile.language === "nl" ? "nl-NL" : "en-US",
                            { year: "numeric", month: "short", day: "numeric" }
                          )}
                        </Text>
                        <View style={styles.entryHeaderRight}>
                          {renderMoodIcon(entry.mood, entry.mood)}
                          {expandedMoodId === entry.id ? (
                            <ChevronUp
                              size={20}
                              color={colors.muted}
                              style={styles.chevronSpacing}
                              strokeWidth={2}
                            />
                          ) : (
                            <ChevronDown
                              size={20}
                              color={colors.muted}
                              style={styles.chevronSpacing}
                              strokeWidth={2}
                            />
                          )}
                        </View>
                      </TouchableOpacity>
                      <View style={styles.entryDetails}>
                        <View style={styles.entryMetric}>
                          <Text
                            style={[
                              styles.entryMetricLabel,
                              { color: colors.muted },
                            ]}
                          >
                            {profile.language === "nl" ? "Stemming" : "Mood"}
                          </Text>
                          <Text
                            style={[
                              styles.entryMetricValue,
                              { color: colors.text },
                            ]}
                          >
                            {entry.mood}/5
                          </Text>
                        </View>
                        <View style={styles.entryMetric}>
                          <Text
                            style={[
                              styles.entryMetricLabel,
                              { color: colors.muted },
                            ]}
                          >
                            {profile.language === "nl"
                              ? "Verlangen"
                              : "Craving"}
                          </Text>
                          <Text
                            style={[
                              styles.entryMetricValue,
                              { color: colors.text },
                            ]}
                          >
                            {entry.cravingIntensity}/5
                          </Text>
                        </View>
                      </View>
                      {expandedMoodId === entry.id && (
                        <>
                          {entry.notes && (
                            <Text
                              style={[
                                styles.entryNotes,
                                { color: colors.text },
                              ]}
                            >
                              {entry.notes}
                            </Text>
                          )}
                          <View style={styles.entryActions}>
                            <TouchableOpacity
                              style={[
                                styles.entryActionButton,
                                { backgroundColor: colors.primary + "20" },
                              ]}
                              onPress={() => handleEditMood(entry.id)}
                            >
                              <Edit2
                                size={16}
                                color={colors.primary}
                                strokeWidth={2.5}
                              />
                              <Text
                                style={[
                                  styles.entryActionText,
                                  { color: colors.primary },
                                ]}
                              >
                                {profile.language === "nl"
                                  ? "Bewerken"
                                  : "Edit"}
                              </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                              style={[
                                styles.entryActionButton,
                                { backgroundColor: colors.danger + "20" },
                              ]}
                              onPress={() => handleDeleteMood(entry.id)}
                            >
                              <Trash2
                                size={16}
                                color={colors.danger}
                                strokeWidth={2.5}
                              />
                              <Text
                                style={[
                                  styles.entryActionText,
                                  { color: colors.danger },
                                ]}
                              >
                                {profile.language === "nl"
                                  ? "Verwijderen"
                                  : "Delete"}
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </>
                      )}
                    </LinearGradient>
                  </View>
                ))}
            </View>
          ) : viewMode === "calendar" ? (
            <>
              <View
                style={[
                  styles.viewModeInfoCard,
                  { backgroundColor: colors.card, borderColor: colors.border },
                ]}
              >
                <LinearGradient
                  colors={
                    [
                      colors.card || colors.background,
                      colors.cardGradient || colors.card || colors.background,
                    ] as const
                  }
                  style={styles.infoCardGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Info size={20} color={colors.primary} strokeWidth={2} />
                  <Text
                    style={[styles.viewModeInfoText, { color: colors.text }]}
                  >
                    {profile.language === "nl"
                      ? "Tik op een datum om details te bekijken of een nieuwe stemming toe te voegen"
                      : "Tap on a date to view details or add a new mood entry"}
                  </Text>
                </LinearGradient>
              </View>
              <CalendarView
                entries={(profile.moodEntries || []).map(
                  (entry: MoodEntry) => ({
                    id: entry.id,
                    date: new Date(entry.date),
                    value: entry.mood,
                    type: "mood" as const,
                  })
                )}
                onDayPress={(date, entries) => {
                  if (entries.length > 0) {
                    const entryId = entries[0].id;
                    handleEditMood(entryId);
                  } else {
                    setShowMoodForm(true);
                  }
                }}
                colors={colors}
                language={profile.language || "en"}
                _type="mood"
              />
            </>
          ) : (
            <View style={styles.chartView}>
              <View
                style={[
                  styles.viewModeInfoCard,
                  { backgroundColor: colors.card, borderColor: colors.border },
                ]}
              >
                <LinearGradient
                  colors={
                    [
                      colors.card || colors.background,
                      colors.cardGradient || colors.card || colors.background,
                    ] as const
                  }
                  style={styles.infoCardGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <TrendingUp
                    size={20}
                    color={colors.primary}
                    strokeWidth={2}
                  />
                  <Text
                    style={[styles.viewModeInfoText, { color: colors.text }]}
                  >
                    {profile.language === "nl"
                      ? "Bekijk trends in je stemming en verlangen over tijd"
                      : "View trends in your mood and cravings over time"}
                  </Text>
                </LinearGradient>
              </View>
              <View
                style={[
                  styles.chartContainer,
                  { backgroundColor: colors.card, borderColor: colors.border },
                ]}
              >
                <LinearGradient
                  colors={
                    [
                      colors.card || colors.background,
                      colors.cardGradient || colors.card || colors.background,
                    ] as const
                  }
                  style={styles.chartGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text style={[styles.chartTitle, { color: colors.text }]}>
                    {profile.language === "nl"
                      ? "Stemming Trend"
                      : "Mood Trend"}
                  </Text>
                  <View style={styles.chartLegend}>
                    <View style={styles.chartLegendItem}>
                      <View
                        style={[
                          styles.chartLegendColor,
                          { backgroundColor: colors.primary },
                        ]}
                      />
                      <Text
                        style={[styles.chartLegendText, { color: colors.text }]}
                      >
                        {profile.language === "nl" ? "Stemming" : "Mood"}
                      </Text>
                    </View>
                  </View>
                  <LineChart
                    data={moodChartData}
                    lineColor={colors.primary}
                    width={width - 40}
                    height={200}
                    maxValue={5}
                  />
                </LinearGradient>
              </View>
              <View
                style={[
                  styles.chartContainer,
                  { backgroundColor: colors.card, borderColor: colors.border },
                ]}
              >
                <LinearGradient
                  colors={
                    [
                      colors.card || colors.background,
                      colors.cardGradient || colors.card || colors.background,
                    ] as const
                  }
                  style={styles.chartGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text style={[styles.chartTitle, { color: colors.text }]}>
                    {profile.language === "nl"
                      ? "Verlangen Trend"
                      : "Craving Trend"}
                  </Text>
                  <View style={styles.chartLegend}>
                    <View style={styles.chartLegendItem}>
                      <View
                        style={[
                          styles.chartLegendColor,
                          { backgroundColor: colors.warning },
                        ]}
                      />
                      <Text
                        style={[styles.chartLegendText, { color: colors.text }]}
                      >
                        {profile.language === "nl" ? "Verlangen" : "Craving"}
                      </Text>
                    </View>
                  </View>
                  <LineChart
                    data={cravingChartData}
                    lineColor={colors.warning}
                    width={width - 40}
                    height={200}
                    maxValue={5}
                  />
                </LinearGradient>
              </View>
              <View
                style={[
                  styles.chartExplanation,
                  { backgroundColor: colors.primary + "10" },
                ]}
              >
                <Text
                  style={[styles.chartExplanationTitle, { color: colors.text }]}
                >
                  {profile.language === "nl"
                    ? "Wat betekent dit?"
                    : "What does this mean?"}
                </Text>
                <Text
                  style={[
                    styles.chartExplanationText,
                    { color: colors.textSecondary },
                  ]}
                >
                  {profile.language === "nl"
                    ? "Deze grafieken tonen hoe je stemming en verlangen veranderen over tijd. Hogere waarden voor stemming zijn beter, terwijl lagere waarden voor verlangen beter zijn."
                    : "These charts show how your mood and cravings change over time. Higher values for mood are better, while lower values for cravings are better."}
                </Text>
              </View>
            </View>
          )}
        </>
      ) : (
        <MoodForm
          visible={showMoodForm}
          onClose={() => setShowMoodForm(false)}
          colors={colors}
          language={profile.language || "en"}
          editingMoodId={null}
          profile={profile}
        />
      )}
    </Animated.View>
  );
};
const styles = StyleSheet.create({
  activeViewModeButton: {
    borderRadius: 12,
    margin: 4,
  },
  addButton: {
    borderRadius: 12,
    elevation: 3,
    marginBottom: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
  },
  addButtonGradient: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    padding: 14,
  },
  addButtonText: {
    color: "#fff",
    fontSize: 15,
    fontWeight: "700",
    letterSpacing: -0.2,
    marginLeft: 6,
  },
  addButtonTextSpacing: {
    marginLeft: 6,
  },
  chartContainer: {
    borderRadius: 16,
    borderWidth: 1,
    elevation: 3,
    marginBottom: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
  },
  chartExplanation: {
    borderRadius: 12,
    marginBottom: 32,
    marginTop: 16,
    padding: 16,
  },
  chartExplanationText: {
    fontSize: 14,
    fontWeight: "500",
    lineHeight: 20,
  },
  chartExplanationTitle: {
    fontSize: 16,
    fontWeight: "700",
    letterSpacing: -0.2,
    marginBottom: 6,
  },
  chartGradient: {
    padding: 16,
  },
  chartLegend: {
    flexDirection: "row",
    marginBottom: 12,
  },
  chartLegendColor: {
    borderRadius: 4,
    height: 10,
    marginRight: 6,
    width: 10,
  },
  chartLegendItem: {
    alignItems: "center",
    flexDirection: "row",
    marginRight: 12,
  },
  chartLegendText: {
    fontSize: 13,
    fontWeight: "500",
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: "700",
    letterSpacing: -0.2,
    marginBottom: 12,
  },
  chartView: {
    flex: 1,
    paddingVertical: 16,
  },
  chevronSpacing: {
    marginLeft: 6,
  },
  emptyIconContainer: {
    alignItems: "center",
    borderRadius: 20,
    height: 80,
    justifyContent: "center",
    marginBottom: 16,
    width: 80,
  },
  emptyState: {
    borderRadius: 20,
    borderStyle: "dashed",
    borderWidth: 2,
    elevation: 3,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
  },
  emptyStateGradient: {
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: "600",
    letterSpacing: -0.1,
    lineHeight: 22,
    textAlign: "center",
  },
  entriesList: {
    flex: 1,
  },
  entryActionButton: {
    alignItems: "center",
    borderRadius: 16,
    flexDirection: "row",
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  entryActionText: {
    fontSize: 13,
    fontWeight: "600",
    letterSpacing: -0.1,
    marginLeft: 4,
  },
  entryActions: {
    flexDirection: "row",
    gap: 8,
    justifyContent: "flex-end",
  },
  entryCard: {
    borderRadius: 16,
    borderWidth: 1,
    elevation: 3,
    marginBottom: 12,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
  },
  entryDate: {
    fontSize: 16,
    fontWeight: "700",
    letterSpacing: -0.2,
  },
  entryDetails: {
    flexDirection: "row",
    gap: 20,
    marginBottom: 12,
  },
  entryGradient: {
    padding: 16,
  },
  entryHeader: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  entryHeaderRight: {
    alignItems: "center",
    flexDirection: "row",
  },
  entryMetric: {
    flex: 1,
  },
  entryMetricLabel: {
    fontSize: 12,
    fontWeight: "500",
    marginBottom: 2,
  },
  entryMetricValue: {
    fontSize: 16,
    fontWeight: "700",
    letterSpacing: -0.2,
  },
  entryNotes: {
    fontSize: 14,
    fontWeight: "500",
    lineHeight: 20,
    marginBottom: 12,
  },
  infoCardGradient: {
    alignItems: "center",
    flexDirection: "row",
    padding: 12,
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  viewModeButton: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    paddingVertical: 10,
  },
  viewModeContainer: {
    borderRadius: 12,
    borderWidth: 1,
    elevation: 2,
    flexDirection: "row",
    marginBottom: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 4,
  },
  viewModeInfoCard: {
    borderRadius: 12,
    borderWidth: 1,
    elevation: 2,
    marginBottom: 12,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 4,
  },
  viewModeInfoText: {
    flex: 1,
    fontSize: 13,
    fontWeight: "500",
    lineHeight: 18,
    marginLeft: 8,
  },
  viewModeText: {
    fontSize: 13,
    fontWeight: "600",
    letterSpacing: -0.1,
  },
});