// Shared types for progress components
export interface Colors {
  primary: string;
  primaryLight: string;
  primaryDark: string;
  secondary: string;
  secondaryLight: string;
  secondaryDark: string;
  accent: string;
  accentLight: string;
  accentDark: string;
  background: string;
  backgroundSecondary: string;
  card: string;
  cardSecondary: string;
  cardGradient: string;
  text: string;
  textSecondary: string;
  textTertiary: string;
  border: string;
  borderLight: string;
  borderDark: string;
  success: string;
  successLight: string;
  successDark: string;
  warning: string;
  warningLight: string;
  warningDark: string;
  danger: string;
  dangerLight: string;
  dangerDark: string;
  info: string;
  infoLight: string;
  infoDark: string;
  muted: string;
  mutedLight: string;
  mutedDark: string;
  notification: string;
  glass: string;
  glassSecondary: string;
  shadow: string;
  shadowLight: string;
  shadowDark: string;
  gray: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  };
}

export type Language = "en" | "nl";
export type ViewMode = "chart" | "calendar" | "list";

// Data interfaces
export interface DataPoint {
  date: string;
  value: number;
  id: string;
  notes?: string;
  metric?: string;
}

export interface MoodEntry {
  id: string;
  date: string;
  mood: 1 | 2 | 3 | 4 | 5;
  cravingIntensity: 1 | 2 | 3 | 4 | 5;
  notes?: string;
}

export interface RelapseEntry {
  id: string;
  date: string;
  triggers: string[];
  notes: string;
}

export interface HealthEntry {
  id: string;
  date: string;
  value: number | string;
  metric: string;
  notes?: string;
}

export interface CalendarEntry {
  id: string;
  date: Date;
  value: number;
  type: "mood" | "relapse" | "health";
  metric?: string;
}

// Profile interface - matches the actual user store structure
export interface UserProfile {
  id?: string;
  language?: Language;
  sobrietyDate?: string | null;
  substanceType?: string;
  usageAmount?: string;
  usageCost?: number;
  costFrequency?: string;
  currency?: "EUR" | "USD";
  moodEntries?: MoodEntry[];
  relapses?: RelapseEntry[];
  healthMetrics?: HealthEntry[];
}

// Component props interfaces
export interface BaseTabProps {
  profile: UserProfile | null;
  colors: Colors;
  language: Language;
}

export interface TabWithViewModeProps extends BaseTabProps {
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode) => void;
}

export interface FormProps {
  visible: boolean;
  onClose: () => void;
  colors: Colors;
  language: Language;
  profile: UserProfile | null;
}

export interface MoodFormProps extends FormProps {
  editingMoodId?: string | null;
}

export interface RelapseFormProps extends FormProps {
  editingRelapseId?: string | null;
}
