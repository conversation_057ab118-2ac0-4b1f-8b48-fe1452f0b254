import React from "react";
import { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
} from "react-native";
import { useUserStore } from "@/store/user/user-store";
import { X, Save } from "lucide-react-native";
import * as Haptics from "expo-haptics";
import { MoodFormProps, MoodEntry } from "./types";
import {
  getMoodLabel,
  getCravingLabel,
  getMoodEmoji,
  getMoodColor,
  getCravingColor,
} from "./utils";
export const MoodForm: React.FC<MoodFormProps> = ({
  visible,
  onClose,
  colors,
  language,
  editingMoodId = null,
  profile,
}) => {
  const { addMoodEntry, editMoodEntry } = useUserStore();
  const [mood, setMood] = useState<1 | 2 | 3 | 4 | 5>(3);
  const [cravingIntensity, setCravingIntensity] = useState<1 | 2 | 3 | 4 | 5>(
    1
  );
  const [notes, setNotes] = useState("");
  React.useEffect(() => {
    if (editingMoodId && profile) {
      const moodEntry = profile.moodEntries?.find(
        (entry: MoodEntry) => entry.id === editingMoodId
      );
      if (moodEntry) {
        setMood(moodEntry.mood);
        setCravingIntensity(moodEntry.cravingIntensity);
        setNotes(moodEntry.notes || "");
      }
    } else {
      // Reset form for new entry
      setMood(3);
      setCravingIntensity(1);
      setNotes("");
    }
  }, [editingMoodId, profile, visible]);
  const handleSave = () => {
    if (Platform.OS !== "web") {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
    const moodData = {
      date: new Date().toISOString(),
      mood,
      cravingIntensity,
      notes,
    };
    if (editingMoodId) {
      editMoodEntry(editingMoodId, moodData);
    } else {
      addMoodEntry(moodData);
    }
    onClose();
  };
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.modalOverlay}
      >
        <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
          <View
            style={[styles.modalHeader, { borderBottomColor: colors.border }]}
          >
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {editingMoodId
                ? language === "nl"
                  ? "Stemming bewerken"
                  : "Edit Mood Entry"
                : language === "nl"
                ? "Nieuwe stemming"
                : "New Mood Entry"}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalBody}>
            <View style={styles.formSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                {language === "nl"
                  ? "Hoe voel je je vandaag?"
                  : "How are you feeling today?"}
              </Text>
              <View style={styles.moodSelector}>
                {[1, 2, 3, 4, 5].map((value) => (
                  <TouchableOpacity
                    key={`mood-${value}`}
                    style={[
                      styles.moodOption,
                      {
                        backgroundColor:
                          mood === value
                            ? getMoodColor(value, colors)
                            : colors.background,
                        borderColor: colors.border,
                      },
                    ]}
                    onPress={() => {
                      setMood(value as 1 | 2 | 3 | 4 | 5);
                      if (Platform.OS !== "web") {
                        Haptics.selectionAsync();
                      }
                    }}
                  >
                    <Text
                      style={[
                        styles.moodValue,
                        mood === value
                          ? styles.moodValueSelected
                          : styles.moodValueDefault,
                      ]}
                    >
                      {getMoodEmoji(value)}
                    </Text>
                    <Text
                      style={[
                        styles.moodLabel,
                        mood === value
                          ? styles.moodLabelSelected
                          : styles.moodLabelDefault,
                      ]}
                    >
                      {getMoodLabel(value, language)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={styles.formSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                {language === "nl"
                  ? "Hoe sterk is je verlangen?"
                  : "How strong is your craving?"}
              </Text>
              <View style={styles.cravingSelector}>
                {[1, 2, 3, 4, 5].map((value) => (
                  <TouchableOpacity
                    key={`craving-${value}`}
                    style={[
                      styles.cravingOption,
                      {
                        backgroundColor:
                          cravingIntensity === value
                            ? getCravingColor(value, colors)
                            : colors.background,
                        borderColor: colors.border,
                      },
                    ]}
                    onPress={() => {
                      setCravingIntensity(value as 1 | 2 | 3 | 4 | 5);
                      if (Platform.OS !== "web") {
                        Haptics.selectionAsync();
                      }
                    }}
                  >
                    <Text
                      style={[
                        styles.cravingValue,
                        cravingIntensity === value
                          ? styles.cravingValueSelected
                          : styles.cravingValueDefault,
                      ]}
                    >
                      {value}
                    </Text>
                    <Text
                      style={[
                        styles.cravingLabel,
                        cravingIntensity === value
                          ? styles.cravingLabelSelected
                          : styles.cravingLabelDefault,
                      ]}
                    >
                      {getCravingLabel(value, language)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={styles.formSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                {language === "nl" ? "Notities" : "Notes"}
              </Text>
              <TextInput
                style={[
                  styles.notesInput,
                  {
                    borderColor: colors.border,
                    backgroundColor: colors.background,
                    color: colors.text,
                  },
                ]}
                placeholder={
                  language === "nl"
                    ? "Voeg eventuele gedachten of triggers toe..."
                    : "Add any thoughts or triggers..."
                }
                placeholderTextColor={colors.muted}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
                value={notes}
                onChangeText={setNotes}
              />
            </View>
          </ScrollView>
          <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.cancelButton, { borderColor: colors.border }]}
              onPress={onClose}
            >
              <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                {language === "nl" ? "Annuleren" : "Cancel"}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.saveButton, { backgroundColor: colors.primary }]}
              onPress={handleSave}
            >
              <Save size={20} color="#fff" />
              <Text style={styles.saveButtonText}>
                {language === "nl" ? "Opslaan" : "Save"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};
const styles = StyleSheet.create({


  cancelButton: {
    alignItems: "center",
    borderRadius: 12,
    borderWidth: 1,
    flex: 1,
    justifyContent: "center",
    marginRight: 8,
    padding: 16,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: "500",
  },
  cravingLabel: {
    fontSize: 10,
    textAlign: "center",
  },
  cravingLabelDefault: {
    color: undefined, // Will be overridden by dynamic color
  },
  cravingLabelSelected: {
    color: "#fff",
  },
  cravingOption: {
    alignItems: "center",
    borderRadius: 12,
    borderWidth: 1,
    height: 80,
    justifyContent: "center",
    padding: 8,
    width: 60,
  },
  cravingSelector: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  cravingValue: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 4,
  },
  cravingValueDefault: {
    fontSize: 18,
  },
  cravingValueSelected: {
    fontSize: 20,
  },
  formSection: {
    marginBottom: 24,
  },
  modalBody: {
    maxHeight: 500,
    padding: 16,
  },
  modalContent: {
    borderRadius: 16,
    maxHeight: "90%",
    overflow: "hidden",
    width: "100%",
  },
  modalFooter: {
    borderTopWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
  },
  modalHeader: {
    alignItems: "center",
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
  },
  modalOverlay: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  moodLabel: {
    fontSize: 10,
    textAlign: "center",
  },
  moodLabelDefault: {
    color: undefined, // Will be overridden by dynamic color
  },
  moodLabelSelected: {
    color: "#fff",
  },
  moodOption: {
    alignItems: "center",
    borderRadius: 12,
    borderWidth: 1,
    height: 80,
    justifyContent: "center",
    padding: 8,
    width: 60,
  },
  moodSelector: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  moodValue: {
    fontSize: 20,
    marginBottom: 4,
  },
  moodValueDefault: {
    color: undefined, // Will be overridden by dynamic color
    fontSize: 18,
  },
  moodValueSelected: {
    color: "#fff",
    fontSize: 20,
  },
  notesInput: {
    borderRadius: 12,
    borderWidth: 1,
    fontSize: 16,
    minHeight: 100,
    padding: 12,
  },
  saveButton: {
    alignItems: "center",
    borderRadius: 12,
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    marginLeft: 8,
    padding: 16,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 12,
  },


});