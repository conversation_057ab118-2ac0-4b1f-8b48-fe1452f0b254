import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Calendar } from 'lucide-react-native';
import Colors from '../../constants/colors';
import { useTheme } from '../../context/theme-context';

interface StepHeaderProps {
  title: string;
  subtitle: string;
  personalizedMessage?: string;
}

const StepHeader: React.FC<StepHeaderProps> = ({ title, subtitle, personalizedMessage }) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];

  return (
    <View style={styles.stepHeader}>
      <View style={[styles.stepIcon, { backgroundColor: colors.primary + '20' }]}>
        <Calendar size={36} color={colors.primary} strokeWidth={2} />
      </View>
      <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
      <Text style={[styles.subtitle, { color: colors.textSecondary }]}>{subtitle}</Text>
      {personalizedMessage && (
        <Text style={[styles.personalMessage, { color: colors.accent }]}>
          {personalizedMessage}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  stepHeader: {
    alignItems: 'center',
    marginBottom: 32,
  },
  stepIcon: {
    alignItems: 'center',
    borderRadius: 44,
    elevation: 4,
    height: 88,
    justifyContent: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    width: 88,
  },
  title: {
    fontSize: 34,
    fontWeight: '800',
    letterSpacing: -0.8,
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 19,
    fontWeight: '500',
    lineHeight: 26,
    marginBottom: 16,
    textAlign: 'center',
  },
  personalMessage: {
    fontSize: 16,
    fontStyle: 'italic',
    fontWeight: '500',
    lineHeight: 22,
    marginTop: 16,
    textAlign: 'center',
  },
});

export default StepHeader;