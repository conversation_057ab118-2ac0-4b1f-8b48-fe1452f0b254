import React, { useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Platform,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import Colors from '../../constants/colors';
import { useTheme } from '../../context/theme-context';
import { useTranslation } from '../../hooks/useTranslation';
import { getUnitsForAddiction } from '../../utils';

interface UsageInputProps {
  usageAmount: string;
  usageUnit: string;
  language: 'en' | 'nl';
  addiction: string;
  onUsageAmountChange: (amount: string) => void;
  onUsageUnitChange: (unit: string) => void;
}

const UsageInput: React.FC<UsageInputProps> = ({
  usageAmount,
  usageUnit,
  language,
  addiction,
  onUsageAmountChange,
  onUsageUnitChange,
}) => {
  const { currentTheme } = useTheme();
  const { t } = useTranslation();
  const colors = Colors[currentTheme];
  const isWeb = Platform.OS === 'web';

  const units = useMemo(() => getUnitsForAddiction(addiction), [addiction]);

  const handleUnitSelect = (unit: string) => {
    onUsageUnitChange(unit);
    if (!isWeb) {
      Haptics.selectionAsync();
    }
  };

  return (
    <View style={styles.usageSection}>
      <Text style={[styles.sectionLabel, { color: colors.text }]}>
        {t('onboarding.sobriety.usageLabel')}
      </Text>
      <View style={styles.usageInputRow}>
        <View style={styles.usageAmountContainer}>
          <TextInput
            style={[
              styles.usageInput,
              {
                borderColor: colors.border,
                backgroundColor: colors.card,
                color: colors.text,
              },
            ]}
            placeholder={t('onboarding.sobriety.usagePlaceholder')}
            placeholderTextColor={colors.textTertiary}
            value={usageAmount}
            onChangeText={onUsageAmountChange}
            keyboardType="numeric"
          />
        </View>
        <View style={styles.unitContainer}>
          <Text style={[styles.unitLabel, { color: colors.textSecondary }]}>
            {t('onboarding.sobriety.usageUnitLabel')}
          </Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.unitScrollView}
          >
            {units.map((unit) => (
              <TouchableOpacity
                key={unit.value}
                style={[
                  styles.unitChip,
                  {
                    backgroundColor:
                      usageUnit === unit.value
                        ? colors.primary + '20'
                        : colors.card,
                    borderColor:
                      usageUnit === unit.value
                        ? colors.primary
                        : colors.border,
                  },
                ]}
                onPress={() => handleUnitSelect(unit.value)}
              >
                <Text
                  style={[
                    styles.unitText,
                    {
                      color:
                        usageUnit === unit.value
                          ? colors.primary
                          : colors.text,
                    },
                  ]}
                >
                  {language === 'nl' ? unit.labelNL : unit.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  usageSection: {
    marginBottom: 32,
  },
  sectionLabel: {
    fontSize: 19,
    fontWeight: '700',
    letterSpacing: -0.3,
    marginBottom: 16,
  },
  usageInputRow: {
    gap: 16,
  },
  usageAmountContainer: {
    flex: 1,
  },
  usageInput: {
    borderRadius: 20,
    borderWidth: 2,
    elevation: 2,
    fontSize: 17,
    fontWeight: '500',
    paddingHorizontal: 24,
    paddingVertical: 18,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  unitContainer: {
    marginTop: 16,
  },
  unitLabel: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 8,
  },
  unitScrollView: {
    flexDirection: 'row',
  },
  unitChip: {
    borderRadius: 24,
    borderWidth: 2,
    marginRight: 8,
    paddingHorizontal: 18,
    paddingVertical: 10,
  },
  unitText: {
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: -0.1,
  },
});

export default UsageInput;