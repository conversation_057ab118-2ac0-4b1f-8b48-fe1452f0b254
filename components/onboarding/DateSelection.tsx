import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { Calendar } from 'lucide-react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import Colors from '../../constants/colors';
import { useTheme } from '../../context/theme-context';

interface DateSelectionProps {
  sobrietyDate: Date;
  showDatePicker: boolean;
  language: 'en' | 'nl';
  onDateChange: (event: DateTimePickerEvent, selectedDate?: Date) => void;
  onShowDatePickerChange: (show: boolean) => void;
}

const DateSelection: React.FC<DateSelectionProps> = ({
  sobrietyDate,
  showDatePicker,
  language,
  onDateChange,
  onShowDatePickerChange,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];

  return (
    <View style={styles.dateSection}>
      {Platform.OS === 'android' && (
        <TouchableOpacity
          style={[styles.dateButton, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => onShowDatePickerChange(true)}
        >
          <Calendar size={20} color={colors.primary} strokeWidth={2} />
          <Text style={[styles.dateButtonText, { color: colors.text }]}>
            {sobrietyDate.toLocaleDateString(language === 'nl' ? 'nl-NL' : 'en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </Text>
        </TouchableOpacity>
      )}

      {showDatePicker && (
        <View style={styles.datePickerContainer}>
          <DateTimePicker
            value={sobrietyDate}
            mode="date"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={onDateChange}
            maximumDate={new Date()}
            style={styles.datePicker}
            locale={language === 'nl' ? 'nl' : 'en'}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  dateSection: {
    marginBottom: 32,
  },
  dateButton: {
    alignItems: 'center',
    borderRadius: 20,
    borderWidth: 2,
    elevation: 2,
    flexDirection: 'row',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  dateButtonText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  datePickerContainer: {
    marginTop: 16,
  },
  datePicker: {
    width: '100%',
  },
});

export default DateSelection;