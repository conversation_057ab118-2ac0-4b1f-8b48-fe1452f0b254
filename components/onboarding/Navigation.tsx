import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ChevronRight, ChevronLeft } from 'lucide-react-native';
import Colors from '../../constants/colors';
import { useTheme } from '../../context/theme-context';
import { useTranslation } from '../../hooks/useTranslation';

interface NavigationProps {
  onBack: () => void;
  onNext: () => void;
  onSkip: () => void;
}

const Navigation: React.FC<NavigationProps> = ({ onBack, onNext, onSkip }) => {
  const { currentTheme } = useTheme();
  const { t } = useTranslation();
  const colors = Colors[currentTheme];

  return (
    <View style={styles.navigationButtons}>
      <TouchableOpacity style={styles.backButton} onPress={onBack}>
        <ChevronLeft size={20} color={colors.textSecondary} strokeWidth={2} />
        <Text style={[styles.backButtonText, { color: colors.textSecondary }]}>
          {t('common.back')}
        </Text>
      </TouchableOpacity>
      <View style={styles.rightButtons}>
        <TouchableOpacity style={styles.skipButton} onPress={onSkip}>
          <Text style={[styles.skipButtonText, { color: colors.textTertiary }]}>
            {t('common.skip')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.primaryButton, { backgroundColor: colors.primary }]}
          onPress={onNext}
        >
          <LinearGradient
            colors={[colors.primary, colors.primaryDark || colors.primary]}
            style={styles.buttonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={styles.primaryButtonText}>{t('common.continue')}</Text>
            <ChevronRight size={20} color="#fff" strokeWidth={2.5} />
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  navigationButtons: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  backButton: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 4,
  },
  rightButtons: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 16,
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  skipButtonText: {
    fontSize: 15,
    fontWeight: '600',
  },
  primaryButton: {
    borderRadius: 20,
    elevation: 4,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '700',
    letterSpacing: -0.3,
    marginRight: 8,
  },
  buttonGradient: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 28,
    paddingVertical: 18,
  },
});

export default Navigation;