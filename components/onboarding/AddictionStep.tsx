import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Platform,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import {
  ChevronRight,
  ChevronLeft,
  Check,
  Shield,
  Wine,
  Cigarette,
  Pill,
  Zap,
  Gamepad2,
  Smartphone,
  AlertCircle,
} from "lucide-react-native";
import Colors from "@/constants/colors";
import { useTheme } from "@/context/theme-context";
import { useTranslation } from "@/hooks/useTranslation";
import * as Haptics from "expo-haptics";

interface AddictionStepProps {
  addiction: string;
  customAddiction: string;
  onAddictionChange: (addiction: string) => void;
  onCustomAddictionChange: (customAddiction: string) => void;
  onNext: () => void;
  onBack: () => void;
  errors: { [key: string]: string };
  personalizedMessage?: string;
}

const isWeb = Platform.select({ web: true, default: false });

// Modern addiction types with icons and enhanced styling
const addictions = [
  {
    id: "Alcohol",
    label: "Alcohol",
    icon: Wine,
    color: "#8B5CF6",
    gradient: ["#8B5CF6", "#7C3AED"] as const,
  },
  {
    id: "Tobacco",
    label: "Tobacco",
    icon: Cigarette,
    color: "#EF4444",
    gradient: ["#EF4444", "#DC2626"] as const,
  },
  {
    id: "Cannabis",
    label: "Cannabis",
    icon: Pill,
    color: "#10B981",
    gradient: ["#10B981", "#059669"] as const,
  },
  {
    id: "Opioids",
    label: "Opioids",
    icon: Pill,
    color: "#F59E0B",
    gradient: ["#F59E0B", "#D97706"] as const,
  },
  {
    id: "Stimulants",
    label: "Stimulants",
    icon: Zap,
    color: "#06B6D4",
    gradient: ["#06B6D4", "#0891B2"] as const,
  },
  {
    id: "Gambling",
    label: "Gambling",
    icon: Gamepad2,
    color: "#EC4899",
    gradient: ["#EC4899", "#DB2777"] as const,
  },
  {
    id: "Social Media",
    label: "Social Media",
    icon: Smartphone,
    color: "#3B82F6",
    gradient: ["#3B82F6", "#2563EB"] as const,
  },
  {
    id: "Other",
    label: "Other",
    icon: AlertCircle,
    color: "#6B7280",
    gradient: ["#6B7280", "#4B5563"] as const,
  },
];

export default function AddictionStep({
  addiction,
  customAddiction,
  onAddictionChange,
  onCustomAddictionChange,
  onNext,
  onBack,
  errors,
  personalizedMessage,
}: AddictionStepProps) {
  const { currentTheme } = useTheme();
  const { t } = useTranslation();
  const colors = Colors[currentTheme];

  const handleAddictionSelect = (selectedAddiction: string) => {
    onAddictionChange(selectedAddiction);
    if (!isWeb) {
      Haptics.selectionAsync();
    }
  };

  // Helper function to determine if the form is valid
  const isFormValid = () => {
    return addiction && (addiction !== "Other" || customAddiction.trim());
  };

  // Helper function to get button style
  const getButtonStyle = () => {
    const isValid = isFormValid();
    return {
      backgroundColor: isValid ? colors.primary : colors.border,
      opacity: isValid ? 1 : 0.5,
    };
  };

  return (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <View
          style={[styles.stepIcon, { backgroundColor: colors.primary + "20" }]}
        >
          <Shield size={36} color={colors.primary} strokeWidth={2} />
        </View>
        <Text style={[styles.title, { color: colors.text }]}>
          {t('onboarding.addiction.title')}
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          {t('onboarding.addiction.subtitle')}
        </Text>
        {personalizedMessage && (
          <Text style={[styles.personalMessage, { color: colors.accent }]}>
            {personalizedMessage}
          </Text>
        )}
      </View>

      {errors.addiction && (
        <Text style={[styles.errorText, { color: colors.danger }]}>
          {errors.addiction}
        </Text>
      )}

      <View style={styles.addictionGrid}>
        {addictions.map((item) => {
          const IconComponent = item.icon;
          const isSelected = addiction === item.id;

          return (
            <TouchableOpacity
              key={item.id}
              style={[
                styles.addictionCard,
                {
                  backgroundColor: colors.card,
                  borderColor: isSelected ? colors.primary : colors.border,
                },
              ]}
              onPress={() => handleAddictionSelect(item.id)}
            >
              <LinearGradient
                colors={[colors.card, colors.card]}
                style={styles.addictionCardGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <View
                  style={[
                    styles.addictionIcon,
                    { backgroundColor: colors.border },
                  ]}
                >
                  <IconComponent
                    size={28}
                    color={colors.text}
                    strokeWidth={2}
                  />
                </View>
                <Text style={[styles.addictionLabel, { color: colors.text }]}>
                  {item.label}
                </Text>
                {isSelected && (
                  <View
                    style={[
                      styles.selectedBadge,
                      { backgroundColor: colors.primary },
                    ]}
                  >
                    <Check size={14} color="#fff" strokeWidth={2.5} />
                  </View>
                )}
              </LinearGradient>
            </TouchableOpacity>
          );
        })}
      </View>

      {addiction === "Other" && (
        <View style={styles.inputContainer}>
          <TextInput
            style={[
              styles.modernInput,
              {
                borderColor: errors.customAddiction
                  ? colors.danger
                  : customAddiction
                  ? colors.primary
                  : colors.border,
                backgroundColor: colors.card,
                color: colors.text,
              },
            ]}
            placeholder={t('onboarding.addiction.specifyPlaceholder')}
            placeholderTextColor={colors.textTertiary}
            value={customAddiction}
            onChangeText={onCustomAddictionChange}
          />
          {errors.customAddiction && (
            <Text style={[styles.errorText, { color: colors.danger }]}>
              {errors.customAddiction}
            </Text>
          )}
        </View>
      )}

      <View style={styles.navigationButtons}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <ChevronLeft size={20} color={colors.textSecondary} strokeWidth={2} />
          <Text
            style={[styles.backButtonText, { color: colors.textSecondary }]}
          >
            {t('common.back')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.primaryButton, getButtonStyle()]}
          onPress={onNext}
          disabled={
            !addiction || (addiction === "Other" && !customAddiction.trim())
          }
        >
          <LinearGradient
            colors={
              addiction && (addiction !== "Other" || customAddiction.trim())
                ? [colors.primary, colors.primaryDark || colors.primary]
                : [colors.border, colors.border]
            }
            style={styles.buttonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={styles.primaryButtonText}>{t('common.continue')}</Text>
            <ChevronRight size={20} color="#fff" strokeWidth={2.5} />
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  addictionCard: {
    borderRadius: 20,
    borderWidth: 2,
    elevation: 4,
    marginBottom: 16,
    overflow: "hidden",
    position: "relative",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    width: "48%",
  },
  addictionCardGradient: {
    alignItems: "center",
    justifyContent: "center",
    minHeight: 120,
    padding: 24,
  },
  addictionGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: 32,
  },
  addictionIcon: {
    alignItems: "center",
    borderRadius: 28,
    height: 56,
    justifyContent: "center",
    marginBottom: 12,
    width: 56,
  },
  addictionLabel: {
    fontSize: 15,
    fontWeight: "700",
    letterSpacing: -0.2,
    textAlign: "center",
  },
  backButton: {
    alignItems: "center",
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 4,
  },
  buttonGradient: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    paddingHorizontal: 28,
    paddingVertical: 18,
  },
  errorText: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 4,
    marginTop: 8,
  },
  inputContainer: {
    marginBottom: 32,
  },
  modernInput: {
    borderRadius: 20,
    borderWidth: 2,
    elevation: 2,
    fontSize: 17,
    fontWeight: "500",
    paddingHorizontal: 24,
    paddingVertical: 18,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  navigationButtons: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  personalMessage: {
    fontSize: 16,
    fontStyle: "italic",
    fontWeight: "500",
    lineHeight: 22,
    marginTop: 16,
    textAlign: "center",
  },
  primaryButton: {
    borderRadius: 20,
    elevation: 4,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  primaryButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginRight: 8,
  },
  selectedBadge: {
    alignItems: "center",
    borderRadius: 12,
    height: 24,
    justifyContent: "center",
    position: "absolute",
    right: 12,
    top: 12,
    width: 24,
  },
  stepContainer: {
    flex: 1,
    justifyContent: "center",
  },
  stepHeader: {
    alignItems: "center",
    marginBottom: 32,
  },
  stepIcon: {
    alignItems: "center",
    borderRadius: 44,
    elevation: 4,
    height: 88,
    justifyContent: "center",
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    width: 88,
  },
  subtitle: {
    fontSize: 19,
    fontWeight: "500",
    lineHeight: 26,
    marginBottom: 16,
    textAlign: "center",
  },
  title: {
    fontSize: 34,
    fontWeight: "800",
    letterSpacing: -0.8,
    marginBottom: 12,
    textAlign: "center",
  },
});
