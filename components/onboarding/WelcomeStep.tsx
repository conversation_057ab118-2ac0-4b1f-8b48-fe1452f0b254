import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import {
  ChevronRight,
  Download,
  Sparkles,
  Target,
  Heart,
  Users,
} from "lucide-react-native";
import Colors from "@/constants/colors";
import { useTheme } from "@/context/theme-context";
import { useTranslation } from "@/hooks/useTranslation";

interface WelcomeStepProps {
  isImporting: boolean;
  onNext: () => void;
  onImportData: () => void;
}

export default function WelcomeStep({
  isImporting,
  onNext,
  onImportData,
}: WelcomeStepProps) {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { t } = useTranslation();

  return (
    <View style={styles.stepContainer}>
      <LinearGradient
        colors={[colors.primary + "15", colors.secondary + "15"]}
        style={styles.heroGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.heroIconContainer}>
          <Sparkles size={72} color={colors.primary} strokeWidth={1.5} />
        </View>
      </LinearGradient>

      <Text style={[styles.title, { color: colors.text }]}>
        {t('onboarding.welcome.title')}
      </Text>
      <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
        {t('onboarding.welcome.subtitle')}
      </Text>
      <Text style={[styles.description, { color: colors.textTertiary }]}>
        {t('onboarding.welcome.description')}
      </Text>

      <View style={styles.featuresContainer}>
        <View style={styles.featureItem}>
          <View
            style={[
              styles.featureIcon,
              { backgroundColor: colors.primary + "20" },
            ]}
          >
            <Target size={24} color={colors.primary} strokeWidth={2} />
          </View>
          <Text style={[styles.featureText, { color: colors.text }]}>
            {t('onboarding.welcome.trackProgress')}
          </Text>
        </View>
        <View style={styles.featureItem}>
          <View
            style={[
              styles.featureIcon,
              { backgroundColor: colors.secondary + "20" },
            ]}
          >
            <Heart size={24} color={colors.secondary} strokeWidth={2} />
          </View>
          <Text style={[styles.featureText, { color: colors.text }]}>
            {t('onboarding.welcome.mindfulness')}
          </Text>
        </View>
        <View style={styles.featureItem}>
          <View
            style={[
              styles.featureIcon,
              { backgroundColor: colors.accent + "20" },
            ]}
          >
            <Users size={24} color={colors.accent} strokeWidth={2} />
          </View>
          <Text style={[styles.featureText, { color: colors.text }]}>
            {t('onboarding.welcome.support')}
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.primaryButton, { backgroundColor: colors.primary }]}
        onPress={onNext}
      >
        <LinearGradient
          colors={[colors.primary, colors.primaryDark || colors.primary]}
          style={styles.buttonGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          <Text style={styles.primaryButtonText}>{t('onboarding.welcome.getStarted')}</Text>
          <ChevronRight size={20} color="#fff" strokeWidth={2.5} />
        </LinearGradient>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.secondaryButton, { borderColor: colors.border }]}
        onPress={onImportData}
        disabled={isImporting}
      >
        {isImporting ? (
          <ActivityIndicator size="small" color={colors.primary} />
        ) : (
          <>
            <Download size={18} color={colors.primary} strokeWidth={2} />
            <Text
              style={[styles.secondaryButtonText, { color: colors.primary }]}
            >
              {t('onboarding.welcome.returningUser')}
            </Text>
          </>
        )}
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({


  buttonGradient: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    paddingHorizontal: 28,
    paddingVertical: 18,
  },
  description: {
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    marginBottom: 32,
    textAlign: "center",
  },
  featureIcon: {
    alignItems: "center",
    borderRadius: 28,
    elevation: 2,
    height: 56,
    justifyContent: "center",
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    width: 56,
  },
  featureItem: {
    alignItems: "center",
    flex: 1,
  },
  featureText: {
    fontSize: 14,
    fontWeight: "600",
    letterSpacing: -0.2,
    textAlign: "center",
  },
  featuresContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 40,
    paddingHorizontal: 8,
  },
  heroGradient: {
    alignItems: "center",
    alignSelf: "center",
    borderRadius: 70,
    elevation: 8,
    height: 140,
    justifyContent: "center",
    marginBottom: 32,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    width: 140,
  },
  heroIconContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  primaryButton: {
    borderRadius: 20,
    elevation: 4,
    marginBottom: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  primaryButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginRight: 8,
  },
  secondaryButton: {
    alignItems: "center",
    borderRadius: 20,
    borderWidth: 2,
    flexDirection: "row",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: "600",
    letterSpacing: -0.2,
    marginLeft: 8,
  },
  stepContainer: {
    flex: 1,
    justifyContent: "center",
  },
  subtitle: {
    fontSize: 19,
    fontWeight: "500",
    lineHeight: 26,
    marginBottom: 16,
    textAlign: "center",
  },
  title: {
    fontSize: 34,
    fontWeight: "800",
    letterSpacing: -0.8,
    marginBottom: 12,
    textAlign: "center",
  },


});
