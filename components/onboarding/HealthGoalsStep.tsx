import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Platform,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { ChevronRight, ChevronLeft, Check, Heart } from "lucide-react-native";
import Colors from "@/constants/colors";
import { useTheme } from "@/context/theme-context";
import { useTranslation } from "@/hooks/useTranslation";
import * as Haptics from "expo-haptics";
import { healthGoalOptions } from "@/constants/health-goals";

interface HealthGoal {
  enabled: boolean;
  target: string;
}

interface HealthGoalsStepProps {
  healthGoals: { [key: string]: HealthGoal };
  language: "en" | "nl";
  onHealthGoalChange: (goalId: string, goal: HealthGoal) => void;
  onNext: () => void;
  onBack: () => void;
  onSkip: () => void;
  personalizedMessage?: string;
}

const isWeb = Platform.select({ web: true, default: false });

export default function HealthGoalsStep({
  healthGoals,
  language,
  onHealthGoalChange,
  onNext,
  onBack,
  onSkip,
  personalizedMessage,
}: HealthGoalsStepProps) {
  const { currentTheme } = useTheme();
  const { t } = useTranslation();
  const colors = Colors[currentTheme];

  const handleGoalToggle = (goalId: string) => {
    const currentGoal = healthGoals[goalId];
    onHealthGoalChange(goalId, {
      ...currentGoal,
      enabled: !currentGoal.enabled,
      target: !currentGoal.enabled ? "" : currentGoal.target, // Initialize target to empty string if enabling
    });

    if (!isWeb) {
      Haptics.selectionAsync();
    }
  };

  const handleTargetChange = (goalId: string, target: string) => {
    const currentGoal = healthGoals[goalId];
    onHealthGoalChange(goalId, {
      ...currentGoal,
      target,
    });
  };

  return (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <View
          style={[styles.stepIcon, { backgroundColor: colors.primary + "20" }]}
        >
          <Heart size={36} color={colors.primary} strokeWidth={2} />
        </View>
        <Text style={[styles.title, { color: colors.text }]}>
          {t('onboarding.healthGoals.title')}
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          {t('onboarding.healthGoals.subtitle')}
        </Text>
        {personalizedMessage && (
          <Text style={[styles.personalMessage, { color: colors.accent }]}>
            {personalizedMessage}
          </Text>
        )}
      </View>

      <View style={styles.healthGoalsContainer}>
        {healthGoalOptions.map((goal) => {
          const IconComponent = goal.icon;
          const currentGoal = healthGoals[goal.id];
          return (
            <View
              key={goal.id}
              style={[
                styles.healthGoalItem,
                { backgroundColor: colors.card, borderColor: colors.border },
              ]}
            >
              <View style={styles.healthGoalHeader}>
                <View
                  style={[
                    styles.healthGoalIconContainer,
                    { backgroundColor: goal.color + "20" },
                  ]}
                >
                  <IconComponent size={24} color={goal.color} strokeWidth={2} />
                </View>
                <Text style={[styles.healthGoalLabel, { color: colors.text }]}>
                  {language === "nl" ? goal.labelNL : goal.label}
                </Text>
                <TouchableOpacity
                  style={[
                    styles.goalEnableButton,
                    {
                      backgroundColor: currentGoal.enabled
                        ? colors.primary
                        : colors.border,
                    },
                  ]}
                  onPress={() => handleGoalToggle(goal.id)}
                >
                  <Check
                    size={16}
                    color={currentGoal.enabled ? "#fff" : colors.textSecondary}
                    strokeWidth={2.5}
                  />
                </TouchableOpacity>
              </View>
              {currentGoal.enabled && (
                <View style={styles.healthGoalInputContainer}>
                  <TextInput
                    style={[
                      styles.modernInput,
                      styles.healthGoalTargetInput,
                      {
                        backgroundColor: colors.background,
                        color: colors.text,
                        borderColor: colors.border,
                      },
                    ]}
                    placeholder={goal.placeholder}
                    placeholderTextColor={colors.textTertiary}
                    value={currentGoal.target || ""}
                    onChangeText={(text) => handleTargetChange(goal.id, text)}
                    keyboardType={
                      goal.inputType === "number" ? "numeric" : "default"
                    }
                  />
                  <Text
                    style={[
                      styles.healthGoalUnit,
                      { color: colors.textSecondary },
                    ]}
                  >
                    {language === "nl" ? goal.unitNL : goal.unit}
                  </Text>
                </View>
              )}
            </View>
          );
        })}
      </View>

      <View style={styles.navigationButtons}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <ChevronLeft size={20} color={colors.textSecondary} strokeWidth={2} />
          <Text
            style={[styles.backButtonText, { color: colors.textSecondary }]}
          >
            {t('common.back')}
          </Text>
        </TouchableOpacity>
        <View style={styles.rightButtons}>
          <TouchableOpacity style={styles.skipButton} onPress={onSkip}>
            <Text
              style={[styles.skipButtonText, { color: colors.textTertiary }]}
            >
              {t('common.skip')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: colors.primary }]}
            onPress={onNext}
          >
            <LinearGradient
              colors={[colors.primary, colors.primaryDark || colors.primary]}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.primaryButtonText}>
                {t('common.continue')}
              </Text>
              <ChevronRight size={20} color="#fff" strokeWidth={2.5} />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  backButton: {
    alignItems: "center",
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 4,
  },
  buttonGradient: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    paddingHorizontal: 28,
    paddingVertical: 18,
  },
  goalEnableButton: {
    alignItems: "center",
    borderRadius: 15,
    height: 30,
    justifyContent: "center",
    width: 30,
  },
  healthGoalHeader: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: 12,
  },
  healthGoalIconContainer: {
    alignItems: "center",
    borderRadius: 20,
    height: 40,
    justifyContent: "center",
    marginRight: 12,
    width: 40,
  },
  healthGoalInputContainer: {
    alignItems: "center",
    flexDirection: "row",
    gap: 8,
    marginTop: 8,
  },
  healthGoalItem: {
    borderRadius: 20,
    borderWidth: 2,
    marginBottom: 16,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  healthGoalLabel: {
    flex: 1,
    fontSize: 17,
    fontWeight: "700",
  },
  healthGoalTargetInput: {
    flex: 1,
    fontSize: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  healthGoalUnit: {
    fontSize: 15,
    fontWeight: "500",
  },
  healthGoalsContainer: {
    marginBottom: 24,
  },
  modernInput: {
    borderRadius: 20,
    borderWidth: 2,
    elevation: 2,
    fontSize: 17,
    fontWeight: "500",
    paddingHorizontal: 24,
    paddingVertical: 18,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  navigationButtons: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  personalMessage: {
    fontSize: 16,
    fontStyle: "italic",
    fontWeight: "500",
    lineHeight: 22,
    marginTop: 16,
    textAlign: "center",
  },
  primaryButton: {
    borderRadius: 20,
    elevation: 4,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  primaryButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginRight: 8,
  },
  rightButtons: {
    alignItems: "center",
    flexDirection: "row",
    gap: 16,
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  skipButtonText: {
    fontSize: 15,
    fontWeight: "600",
  },
  stepContainer: {
    flex: 1,
    justifyContent: "center",
  },
  stepHeader: {
    alignItems: "center",
    marginBottom: 32,
  },
  stepIcon: {
    alignItems: "center",
    borderRadius: 44,
    elevation: 4,
    height: 88,
    justifyContent: "center",
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    width: 88,
  },
  subtitle: {
    fontSize: 19,
    fontWeight: "500",
    lineHeight: 26,
    marginBottom: 16,
    textAlign: "center",
  },
  title: {
    fontSize: 34,
    fontWeight: "800",
    letterSpacing: -0.8,
    marginBottom: 12,
    textAlign: "center",
  },
});
