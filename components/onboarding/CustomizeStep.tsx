import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Platform,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import {
  ChevronLeft,
  Check,
  LayoutGrid,
  Circle,
  DollarSign,
  Euro,
  Settings,
} from "lucide-react-native";
import Colors from "@/constants/colors";
import { useTheme } from "@/context/theme-context";
import { useTranslation } from "@/hooks/useTranslation";
import * as Haptics from "expo-haptics";

interface CustomizeStepProps {
  dashboardView: "cards" | "circular";
  usageCost: string;
  costFrequency: string;
  onDashboardViewChange: (view: "cards" | "circular") => void;
  onUsageCostChange: (cost: string) => void;
  onCostFrequencyChange: (frequency: string) => void;
  onNext: () => void;
  onBack: () => void;
  onSkip: () => void;
  personalizedMessage?: string;
}

const isWeb = Platform.select({ web: true, default: false });

export default function CustomizeStep({
  dashboardView,
  usageCost,
  costFrequency,
  onDashboardViewChange,
  onUsageCostChange,
  onCostFrequencyChange,
  onNext,
  onBack,
  onSkip,
  personalizedMessage,
}: CustomizeStepProps) {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { t, getCurrentLanguage } = useTranslation();
  const language = getCurrentLanguage();

  const frequencyOptions = [
    { label: t('onboarding.customize.frequency.daily'), value: "daily" },
    { label: t('onboarding.customize.frequency.weekly'), value: "weekly" },
    { label: t('onboarding.customize.frequency.monthly'), value: "monthly" },
  ];

  const handleDashboardViewSelect = (view: "cards" | "circular") => {
    onDashboardViewChange(view);
    if (!isWeb) {
      Haptics.selectionAsync();
    }
  };

  const handleFrequencySelect = (frequency: string) => {
    onCostFrequencyChange(frequency);
    if (!isWeb) {
      Haptics.selectionAsync();
    }
  };

  return (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <View
          style={[styles.stepIcon, { backgroundColor: colors.primary + "20" }]}
        >
          <Settings size={36} color={colors.primary} strokeWidth={2} />
        </View>
        <Text style={[styles.title, { color: colors.text }]}>
          {t('onboarding.customize.title')}
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          {t('onboarding.customize.subtitle')}
        </Text>
        {personalizedMessage && (
          <Text style={[styles.personalMessage, { color: colors.accent }]}>
            {personalizedMessage}
          </Text>
        )}
      </View>

      <View style={styles.displaySection}>
        <Text style={[styles.sectionLabel, { color: colors.text }]}>
          {t('onboarding.customize.dashboardLabel')}
        </Text>

        <View style={styles.displayOptions}>
          <TouchableOpacity
            style={[
              styles.displayCard,
              {
                backgroundColor:
                  dashboardView === "cards"
                    ? colors.primary + "15"
                    : colors.card,
                borderColor:
                  dashboardView === "cards" ? colors.primary : colors.border,
              },
            ]}
            onPress={() => handleDashboardViewSelect("cards")}
          >
            <View
              style={[
                styles.displayIcon,
                { backgroundColor: colors.primary + "20" },
              ]}
            >
              <LayoutGrid size={36} color={colors.primary} strokeWidth={2} />
            </View>
            <Text style={[styles.displayTitle, { color: colors.text }]}>
              {t('onboarding.customize.cardsLabel')}
            </Text>
            <Text
              style={[
                styles.displayDescription,
                { color: colors.textSecondary },
              ]}
            >
              {t('onboarding.customize.cardsDescription')}
            </Text>
            {dashboardView === "cards" && (
              <View
                style={[
                  styles.selectedBadge,
                  { backgroundColor: colors.primary },
                ]}
              >
                <Check size={14} color="#fff" strokeWidth={2.5} />
              </View>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.displayCard,
              {
                backgroundColor:
                  dashboardView === "circular"
                    ? colors.primary + "15"
                    : colors.card,
                borderColor:
                  dashboardView === "circular" ? colors.primary : colors.border,
              },
            ]}
            onPress={() => handleDashboardViewSelect("circular")}
          >
            <View
              style={[
                styles.displayIcon,
                { backgroundColor: colors.secondary + "20" },
              ]}
            >
              <Circle size={36} color={colors.secondary} strokeWidth={2} />
            </View>
            <Text style={[styles.displayTitle, { color: colors.text }]}>
              {t('onboarding.customize.circularLabel')}
            </Text>
            <Text
              style={[
                styles.displayDescription,
                { color: colors.textSecondary },
              ]}
            >
              {t('onboarding.customize.circularDescription')}
            </Text>
            {dashboardView === "circular" && (
              <View
                style={[
                  styles.selectedBadge,
                  { backgroundColor: colors.primary },
                ]}
              >
                <Check size={14} color="#fff" strokeWidth={2.5} />
              </View>
            )}
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.costSection}>
        <Text style={[styles.sectionLabel, { color: colors.text }]}>
          {t('onboarding.customize.costLabel')}
        </Text>

        <View style={styles.costInputRow}>
          <View
            style={[
              styles.modernInput,
              styles.costInputContainer,
              {
                borderColor: colors.border,
                backgroundColor: colors.card,
              },
            ]}
          >
            {language === "nl" ? (
              <Euro size={20} color={colors.primary} strokeWidth={2} />
            ) : (
              <DollarSign size={20} color={colors.primary} strokeWidth={2} />
            )}
            <TextInput
              style={[
                styles.costInput,
                {
                  color: colors.text,
                },
              ]}
              placeholder="0.00"
              placeholderTextColor={colors.textTertiary}
              value={usageCost}
              onChangeText={onUsageCostChange}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.frequencyContainer}>
            <Text style={[styles.unitLabel, { color: colors.textSecondary }]}>
              {t('onboarding.customize.frequencyLabel')}
            </Text>
            <View style={styles.frequencyOptions}>
              {frequencyOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.frequencyChip,
                    {
                      backgroundColor:
                        costFrequency === option.value
                          ? colors.primary + "20"
                          : colors.card,
                      borderColor:
                        costFrequency === option.value
                          ? colors.primary
                          : colors.border,
                    },
                  ]}
                  onPress={() => handleFrequencySelect(option.value)}
                >
                  <Text
                    style={[
                      styles.frequencyText,
                      {
                        color:
                          costFrequency === option.value
                            ? colors.primary
                            : colors.text,
                      },
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </View>

      <Text style={[styles.settingsNote, { color: colors.textTertiary }]}>
        {t('onboarding.customize.settingsNote')}
      </Text>

      <View style={styles.navigationButtons}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <ChevronLeft size={20} color={colors.textSecondary} strokeWidth={2} />
          <Text
            style={[styles.backButtonText, { color: colors.textSecondary }]}
          >
            {t('common.back')}
          </Text>
        </TouchableOpacity>

        <View style={styles.rightButtons}>
          <TouchableOpacity style={styles.skipButton} onPress={onSkip}>
            <Text
              style={[styles.skipButtonText, { color: colors.textTertiary }]}
            >
              {t('common.skip')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: colors.primary }]}
            onPress={onNext}
          >
            <LinearGradient
              colors={[colors.primary, colors.primaryDark || colors.primary]}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.primaryButtonText}>
                {t('onboarding.customize.completeSetup')}
              </Text>
              <Check size={20} color="#fff" strokeWidth={2.5} />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  backButton: {
    alignItems: "center",
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 4,
  },
  buttonGradient: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    paddingHorizontal: 28,
    paddingVertical: 18,
  },
  costInput: {
    flex: 1,
    fontSize: 17,
    fontWeight: "500",
  },
  costInputContainer: {
    flex: 1,
  },
  costInputRow: {
    gap: 16,
  },
  costSection: {
    marginBottom: 24,
  },
  displayCard: {
    alignItems: "center",
    borderRadius: 20,
    borderWidth: 2,
    elevation: 4,
    flex: 1,
    padding: 24,
    position: "relative",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  displayDescription: {
    fontSize: 13,
    fontWeight: "500",
    lineHeight: 18,
    textAlign: "center",
  },
  displayIcon: {
    alignItems: "center",
    borderRadius: 32,
    height: 64,
    justifyContent: "center",
    marginBottom: 16,
    width: 64,
  },
  displayOptions: {
    flexDirection: "row",
    gap: 16,
    justifyContent: "space-between",
  },
  displaySection: {
    marginBottom: 32,
  },
  displayTitle: {
    fontSize: 17,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginBottom: 8,
    textAlign: "center",
  },
  frequencyChip: {
    borderRadius: 24,
    borderWidth: 2,
    paddingHorizontal: 18,
    paddingVertical: 10,
  },
  frequencyContainer: {
    marginTop: 16,
  },
  frequencyOptions: {
    flexDirection: "row",
    gap: 8,
  },
  frequencyText: {
    fontSize: 14,
    fontWeight: "600",
    letterSpacing: -0.1,
  },
  modernInput: {
    alignItems: "center",
    borderRadius: 20,
    borderWidth: 2,
    elevation: 2,
    flexDirection: "row",
    gap: 12,
    paddingHorizontal: 24,
    paddingVertical: 18,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  navigationButtons: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  personalMessage: {
    fontSize: 16,
    fontStyle: "italic",
    fontWeight: "500",
    lineHeight: 22,
    marginTop: 16,
    textAlign: "center",
  },
  primaryButton: {
    borderRadius: 20,
    elevation: 4,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  primaryButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginRight: 8,
  },
  rightButtons: {
    alignItems: "center",
    flexDirection: "row",
    gap: 16,
  },
  sectionLabel: {
    fontSize: 19,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginBottom: 16,
  },
  selectedBadge: {
    alignItems: "center",
    borderRadius: 12,
    height: 24,
    justifyContent: "center",
    position: "absolute",
    right: 12,
    top: 12,
    width: 24,
  },
  settingsNote: {
    fontSize: 13,
    fontStyle: "italic",
    fontWeight: "500",
    marginBottom: 32,
    textAlign: "center",
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  skipButtonText: {
    fontSize: 15,
    fontWeight: "600",
  },
  stepContainer: {
    flex: 1,
    justifyContent: "center",
  },
  stepHeader: {
    alignItems: "center",
    marginBottom: 32,
  },
  stepIcon: {
    alignItems: "center",
    borderRadius: 44,
    elevation: 4,
    height: 88,
    justifyContent: "center",
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    width: 88,
  },
  subtitle: {
    fontSize: 19,
    fontWeight: "500",
    lineHeight: 26,
    marginBottom: 16,
    textAlign: "center",
  },
  title: {
    fontSize: 34,
    fontWeight: "800",
    letterSpacing: -0.8,
    marginBottom: 12,
    textAlign: "center",
  },
  unitLabel: {
    fontSize: 15,
    fontWeight: "600",
    marginBottom: 8,
  },
});
