import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { ChevronRight, ChevronLeft, User } from "lucide-react-native";
import Colors from "@/constants/colors";
import { useTheme } from "@/context/theme-context";
import { useTranslation } from "@/hooks/useTranslation";

interface NameStepProps {
  name: string;
  onNameChange: (name: string) => void;
  onNext: () => void;
  onBack: () => void;
  error?: string;
}

export default function NameStep({
  name,
  onNameChange,
  onNext,
  onBack,
  error,
}: NameStepProps) {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { t } = useTranslation();

  // Helper function to determine if the form is valid
  const isFormValid = () => {
    return name.trim();
  };

  // Helper function to get button style
  const getButtonStyle = () => {
    const isValid = isFormValid();
    return {
      backgroundColor: isValid ? colors.primary : colors.border,
      opacity: isValid ? 1 : 0.5,
    };
  };

  // Helper function to get gradient colors
  const getGradientColors = (): [string, string] => {
    const isValid = isFormValid();
    return isValid
      ? [colors.primary, colors.primaryDark || colors.primary]
      : [colors.border, colors.border];
  };

  return (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <View
          style={[styles.stepIcon, { backgroundColor: colors.primary + "20" }]}
        >
          <User size={36} color={colors.primary} strokeWidth={2} />
        </View>
        <Text style={[styles.title, { color: colors.text }]}>
          {t('onboarding.name.title')}
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          {t('onboarding.name.subtitle')}
        </Text>
      </View>

      <View style={styles.inputContainer}>
        <TextInput
          style={[
            styles.modernInput,
            {
              borderColor: error
                ? colors.danger
                : name
                ? colors.primary
                : colors.border,
              backgroundColor: colors.card,
              color: colors.text,
            },
          ]}
          placeholder={t('onboarding.name.placeholder')}
          placeholderTextColor={colors.textTertiary}
          value={name}
          onChangeText={onNameChange}
        />
        {error && (
          <Text style={[styles.errorText, { color: colors.danger }]}>
            {error}
          </Text>
        )}
      </View>

      <View style={styles.navigationButtons}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <ChevronLeft size={20} color={colors.textSecondary} strokeWidth={2} />
          <Text
            style={[styles.backButtonText, { color: colors.textSecondary }]}
          >
            {t('common.back')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.primaryButton, getButtonStyle()]}
          onPress={onNext}
          disabled={!name.trim()}
        >
          <LinearGradient
            colors={getGradientColors()}
            style={styles.buttonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={styles.primaryButtonText}>{t('common.continue')}</Text>
            <ChevronRight size={20} color="#fff" strokeWidth={2.5} />
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({


  backButton: {
    alignItems: "center",
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 4,
  },
  buttonGradient: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    paddingHorizontal: 28,
    paddingVertical: 18,
  },
  errorText: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 4,
    marginTop: 8,
  },
  inputContainer: {
    marginBottom: 32,
  },
  modernInput: {
    borderRadius: 20,
    borderWidth: 2,
    elevation: 2,
    fontSize: 17,
    fontWeight: "500",
    paddingHorizontal: 24,
    paddingVertical: 18,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  navigationButtons: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  primaryButton: {
    borderRadius: 20,
    elevation: 4,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  primaryButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginRight: 8,
  },
  stepContainer: {
    flex: 1,
    justifyContent: "center",
  },
  stepHeader: {
    alignItems: "center",
    marginBottom: 32,
  },
  stepIcon: {
    alignItems: "center",
    borderRadius: 44,
    elevation: 4,
    height: 88,
    justifyContent: "center",
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    width: 88,
  },
  subtitle: {
    fontSize: 19,
    fontWeight: "500",
    lineHeight: 26,
    marginBottom: 16,
    textAlign: "center",
  },
  title: {
    fontSize: 34,
    fontWeight: "800",
    letterSpacing: -0.8,
    marginBottom: 12,
    textAlign: "center",
  },


});
