import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Platform,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { ChevronRight, ChevronLeft, Check, Globe } from "lucide-react-native";
import Colors from "@/constants/colors";
import { useTheme } from "@/context/theme-context";
import { useTranslation } from "@/hooks/useTranslation";
import * as Haptics from "expo-haptics";

interface LanguageStepProps {
  language: "en" | "nl";
  onLanguageChange: (lang: "en" | "nl") => void;
  onNext: () => void;
  onBack: () => void;
}

const isWeb = Platform.select({ web: true, default: false });

export default function LanguageStep({
  language,
  onLanguageChange,
  onNext,
  onBack,
}: LanguageStepProps) {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { t } = useTranslation();

  const handleLanguageSelect = (selectedLanguage: "en" | "nl") => {
    onLanguageChange(selectedLanguage);
    if (!isWeb) {
      Haptics.selectionAsync();
    }
  };

  return (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <View
          style={[styles.stepIcon, { backgroundColor: colors.primary + "20" }]}
        >
          <Globe size={36} color={colors.primary} strokeWidth={2} />
        </View>
        <Text style={[styles.title, { color: colors.text }]}>
          {t('onboarding.language.title')}
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          {t('onboarding.language.subtitle')}
        </Text>
      </View>

      <View style={styles.languageContainer}>
        <TouchableOpacity
          style={[
            styles.languageOption,
            {
              backgroundColor:
                language === "en" ? colors.primary + "15" : colors.card,
              borderColor: language === "en" ? colors.primary : colors.border,
            },
          ]}
          onPress={() => handleLanguageSelect("en")}
        >
          <View style={styles.languageContent}>
            <Text style={[styles.languageTitle, { color: colors.text }]}>
              English
            </Text>
            <Text
              style={[styles.languageSubtitle, { color: colors.textSecondary }]}
            >
              United States
            </Text>
          </View>
          {language === "en" && (
            <View
              style={[styles.checkIcon, { backgroundColor: colors.primary }]}
            >
              <Check size={16} color="#fff" strokeWidth={2.5} />
            </View>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.languageOption,
            {
              backgroundColor:
                language === "nl" ? colors.primary + "15" : colors.card,
              borderColor: language === "nl" ? colors.primary : colors.border,
            },
          ]}
          onPress={() => handleLanguageSelect("nl")}
        >
          <View style={styles.languageContent}>
            <Text style={[styles.languageTitle, { color: colors.text }]}>
              Nederlands
            </Text>
            <Text
              style={[styles.languageSubtitle, { color: colors.textSecondary }]}
            >
              Nederland
            </Text>
          </View>
          {language === "nl" && (
            <View
              style={[styles.checkIcon, { backgroundColor: colors.primary }]}
            >
              <Check size={16} color="#fff" strokeWidth={2.5} />
            </View>
          )}
        </TouchableOpacity>
      </View>

      <View style={styles.navigationButtons}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <ChevronLeft size={20} color={colors.textSecondary} strokeWidth={2} />
          <Text
            style={[styles.backButtonText, { color: colors.textSecondary }]}
          >
            {t('common.back')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.primaryButton, { backgroundColor: colors.primary }]}
          onPress={onNext}
        >
          <LinearGradient
            colors={[colors.primary, colors.primaryDark || colors.primary]}
            style={styles.buttonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={styles.primaryButtonText}>{t('common.continue')}</Text>
            <ChevronRight size={20} color="#fff" strokeWidth={2.5} />
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({


  backButton: {
    alignItems: "center",
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 4,
  },
  buttonGradient: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    paddingHorizontal: 28,
    paddingVertical: 18,
  },
  checkIcon: {
    alignItems: "center",
    borderRadius: 14,
    height: 28,
    justifyContent: "center",
    width: 28,
  },
  languageContainer: {
    marginBottom: 32,
  },
  languageContent: {
    flex: 1,
  },
  languageOption: {
    alignItems: "center",
    borderRadius: 20,
    borderWidth: 2,
    elevation: 2,
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
    padding: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  languageSubtitle: {
    fontSize: 15,
    fontWeight: "500",
  },
  languageTitle: {
    fontSize: 19,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginBottom: 4,
  },
  navigationButtons: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  primaryButton: {
    borderRadius: 20,
    elevation: 4,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  primaryButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginRight: 8,
  },
  stepContainer: {
    flex: 1,
    justifyContent: "center",
  },
  stepHeader: {
    alignItems: "center",
    marginBottom: 32,
  },
  stepIcon: {
    alignItems: "center",
    borderRadius: 44,
    elevation: 4,
    height: 88,
    justifyContent: "center",
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    width: 88,
  },
  subtitle: {
    fontSize: 19,
    fontWeight: "500",
    lineHeight: 26,
    marginBottom: 16,
    textAlign: "center",
  },
  title: {
    fontSize: 34,
    fontWeight: "800",
    letterSpacing: -0.8,
    marginBottom: 12,
    textAlign: "center",
  },


});
