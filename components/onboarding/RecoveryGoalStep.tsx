import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { useTranslation } from "@/hooks/useTranslation";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { Button } from "@/components/shared/Button";
import { OptionSelector } from "@/components/shared/OptionSelector";

interface RecoveryGoalStepProps {
  recoveryGoalType: "Abstinence" | "Harm Reduction" | undefined;
  onRecoveryGoalTypeChange: (goal: "Abstinence" | "Harm Reduction" | undefined) => void;
  onNext: () => void;
  onBack: () => void;
  personalizedMessage: string;
  error?: string;
}

const RecoveryGoalStep: React.FC<RecoveryGoalStepProps> = ({
  recoveryGoalType,
  onRecoveryGoalTypeChange,
  onNext,
  onBack,
  personalizedMessage,
  error,
}) => {
  const { t } = useTranslation();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];

  const options = [
    { label: t('onboarding.recoveryGoal.abstinence'), value: "Abstinence" },
    { label: t('onboarding.recoveryGoal.harmReduction'), value: "Harm Reduction" },
  ];

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>
        {t('onboarding.recoveryGoal.title')}
      </Text>
      <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
        {t('onboarding.recoveryGoal.description')}
      </Text>
      {personalizedMessage ? (
        <Text style={[styles.personalizedMessage, { color: colors.textSecondary }]}>
          {personalizedMessage}
        </Text>
      ) : null}

      <View style={styles.optionsContainer}>
        <OptionSelector
          options={options}
          selectedValue={recoveryGoalType}
          onSelect={(value) => onRecoveryGoalTypeChange(value as "Abstinence" | "Harm Reduction" | undefined)}
          colors={colors}
          buttonStyle={{ backgroundColor: colors.card }}
          textStyle={{ color: colors.text }}
          selectedButtonStyle={{ backgroundColor: colors.primary }}
          selectedTextStyle={{ color: colors.background }}
        />
      </View>

      {error && <Text style={styles.errorText}>{error}</Text>}

      <View style={styles.buttonContainer}>
        <Button
          title={t('common.back')}
          onPress={onBack}
          colors={colors}
          style={{ ...styles.button, backgroundColor: colors.backgroundSecondary }}
          textStyle={{ color: colors.textSecondary }}
        />
        <Button
          title={t('common.next')}
          onPress={onNext}
          colors={colors}
          style={{ ...styles.button, backgroundColor: colors.primary }}
          textStyle={{ color: colors.card }}
          disabled={recoveryGoalType === undefined}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 5,
    paddingVertical: 12,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 30,
  },
  container: {
    flex: 1,
    justifyContent: "space-between",
    paddingBottom: 20,
  },
  errorText: {
    color: "red",
    marginBottom: 10,
    marginTop: 10,
    textAlign: "center",
  },
  optionsContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
  },
  personalizedMessage: {
    fontSize: 14,
    fontStyle: "italic",
    marginBottom: 20,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: "center",
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 10,
    textAlign: "center",
  },
});

export default RecoveryGoalStep;