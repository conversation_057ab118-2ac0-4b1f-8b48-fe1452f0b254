import React from "react";
import { StyleSheet, View } from "react-native";
import { DateTimePickerEvent } from "@react-native-community/datetimepicker";

import { useTranslation } from "../../hooks/useTranslation";
import StepHeader from "./StepHeader";
import DateSelection from "./DateSelection";
import UsageInput from "./UsageInput";
import Navigation from "./Navigation";

interface SobrietyStepProps {
  sobrietyDate: Date;
  showDatePicker: boolean;
  usageAmount: string;
  usageUnit: string;
  language: "en" | "nl";
  addiction: string;
  onDateChange: (event: DateTimePickerEvent, selectedDate?: Date) => void;
  onShowDatePickerChange: (show: boolean) => void;
  onUsageAmountChange: (amount: string) => void;
  onUsageUnitChange: (unit: string) => void;
  onNext: () => void;
  onBack: () => void;
  onSkip: () => void;
  personalizedMessage?: string;
}

export default function SobrietyStep(props: SobrietyStepProps) {
  const { t } = useTranslation();

  return (
    <View style={styles.stepContainer}>
      <StepHeader
        title={t('onboarding.sobriety.title')}
        subtitle={t('onboarding.sobriety.subtitle')}
        personalizedMessage={props.personalizedMessage}
      />
      <DateSelection
        sobrietyDate={props.sobrietyDate}
        showDatePicker={props.showDatePicker}
        language={props.language}
        onDateChange={props.onDateChange}
        onShowDatePickerChange={props.onShowDatePickerChange}
      />
      <UsageInput
        usageAmount={props.usageAmount}
        usageUnit={props.usageUnit}
        language={props.language}
        addiction={props.addiction}
        onUsageAmountChange={props.onUsageAmountChange}
        onUsageUnitChange={props.onUsageUnitChange}
      />
      <Navigation
        onBack={props.onBack}
        onNext={props.onNext}
        onSkip={props.onSkip}
      />
    </View>
  );
}

// --- Styles --- //

const styles = StyleSheet.create({
  stepContainer: {
    flex: 1,
    justifyContent: "center",
  },
});
