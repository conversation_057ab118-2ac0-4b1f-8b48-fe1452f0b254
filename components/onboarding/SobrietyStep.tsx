import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Platform,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { ChevronRight, ChevronLeft, Calendar } from "lucide-react-native";
import DateTimePicker, {
  DateTimePickerEvent,
} from "@react-native-community/datetimepicker";
import Colors from "@/constants/colors";
import { useTheme } from "@/context/theme-context";
import { useTranslation } from "@/hooks/useTranslation";
import * as Haptics from "expo-haptics";
import { getUnitsForAddiction } from "@/utils";

interface SobrietyStepProps {
  sobrietyDate: Date;
  showDatePicker: boolean;
  usageAmount: string;
  usageUnit: string;
  language: "en" | "nl";
  addiction: string;
  onDateChange: (event: DateTimePickerEvent, selectedDate?: Date) => void;
  onShowDatePickerChange: (show: boolean) => void;
  onUsageAmountChange: (amount: string) => void;
  onUsageUnitChange: (unit: string) => void;
  onNext: () => void;
  onBack: () => void;
  onSkip: () => void;
  personalizedMessage?: string;
}

const isWeb = Platform.select({ web: true, default: false });

export default function SobrietyStep({
  sobrietyDate,
  showDatePicker,
  usageAmount,
  usageUnit,
  language,
  addiction,
  onDateChange,
  onShowDatePickerChange,
  onUsageAmountChange,
  onUsageUnitChange,
  onNext,
  onBack,
  onSkip,
  personalizedMessage,
}: SobrietyStepProps) {
  const { currentTheme } = useTheme();
  const { t } = useTranslation();
  const colors = Colors[currentTheme];

  // Get available units for the selected addiction using shared utility
  const getUnitsForSelectedAddiction = () => {
    return getUnitsForAddiction(addiction);
  };

  const handleUnitSelect = (unit: string) => {
    onUsageUnitChange(unit);
    if (!isWeb) {
      Haptics.selectionAsync();
    }
  };

  return (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <View
          style={[styles.stepIcon, { backgroundColor: colors.primary + "20" }]}
        >
          <Calendar size={36} color={colors.primary} strokeWidth={2} />
        </View>
        <Text style={[styles.title, { color: colors.text }]}>
          {t('onboarding.sobriety.title')}
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          {t('onboarding.sobriety.subtitle')}
        </Text>
        {personalizedMessage && (
          <Text style={[styles.personalMessage, { color: colors.accent }]}>
            {personalizedMessage}
          </Text>
        )}
      </View>

      <View style={styles.dateSection}>
        {Platform.OS === "android" && (
          <TouchableOpacity
            style={[
              styles.dateButton,
              { backgroundColor: colors.card, borderColor: colors.border },
            ]}
            onPress={() => onShowDatePickerChange(true)}
          >
            <Calendar size={20} color={colors.primary} strokeWidth={2} />
            <Text style={[styles.dateButtonText, { color: colors.text }]}>
              {sobrietyDate.toLocaleDateString(
                language === "nl" ? "nl-NL" : "en-US",
                {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                }
              )}
            </Text>
          </TouchableOpacity>
        )}

        {showDatePicker && (
          <View style={styles.datePickerContainer}>
            <DateTimePicker
              value={sobrietyDate}
              mode="date"
              display={Platform.OS === "ios" ? "spinner" : "default"}
              onChange={onDateChange}
              maximumDate={new Date()}
              style={styles.datePicker}
              locale={language === "nl" ? "nl" : "en"}
            />
          </View>
        )}
      </View>

      <View style={styles.usageSection}>
        <Text style={[styles.sectionLabel, { color: colors.text }]}>
          {t('onboarding.sobriety.usageLabel')}
        </Text>

        <View style={styles.usageInputRow}>
          <View style={styles.usageAmountContainer}>
            <TextInput
              style={[
                styles.usageInput,
                {
                  borderColor: colors.border,
                  backgroundColor: colors.card,
                  color: colors.text,
                },
              ]}
              placeholder={t('onboarding.sobriety.usagePlaceholder')}
              placeholderTextColor={colors.textTertiary}
              value={usageAmount}
              onChangeText={onUsageAmountChange}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.unitContainer}>
            <Text style={[styles.unitLabel, { color: colors.textSecondary }]}>
              {t('onboarding.sobriety.usageUnitLabel')}
            </Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.unitScrollView}
            >
              {getUnitsForSelectedAddiction().map((unit) => (
                <TouchableOpacity
                  key={unit.value}
                  style={[
                    styles.unitChip,
                    {
                      backgroundColor:
                        usageUnit === unit.value
                          ? colors.primary + "20"
                          : colors.card,
                      borderColor:
                        usageUnit === unit.value
                          ? colors.primary
                          : colors.border,
                    },
                  ]}
                  onPress={() => handleUnitSelect(unit.value)}
                >
                  <Text
                    style={[
                      styles.unitText,
                      {
                        color:
                          usageUnit === unit.value
                            ? colors.primary
                            : colors.text,
                      },
                    ]}
                  >
                    {language === "nl" ? unit.labelNL : unit.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </View>

      <View style={styles.navigationButtons}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <ChevronLeft size={20} color={colors.textSecondary} strokeWidth={2} />
          <Text
            style={[styles.backButtonText, { color: colors.textSecondary }]}
          >
            {t('common.back')}
          </Text>
        </TouchableOpacity>

        <View style={styles.rightButtons}>
          <TouchableOpacity style={styles.skipButton} onPress={onSkip}>
            <Text
              style={[styles.skipButtonText, { color: colors.textTertiary }]}
            >
              {t('common.skip')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: colors.primary }]}
            onPress={onNext}
          >
            <LinearGradient
              colors={[colors.primary, colors.primaryDark || colors.primary]}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.primaryButtonText}>
                {t('common.continue')}
              </Text>
              <ChevronRight size={20} color="#fff" strokeWidth={2.5} />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  backButton: {
    alignItems: "center",
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 4,
  },
  buttonGradient: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    paddingHorizontal: 28,
    paddingVertical: 18,
  },
  dateButton: {
    alignItems: "center",
    borderRadius: 20,
    borderWidth: 2,
    elevation: 2,
    flexDirection: "row",
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  dateButtonText: {
    flex: 1,
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 12,
  },
  datePicker: {
    width: "100%",
  },
  datePickerContainer: {
    marginTop: 16,
  },
  dateSection: {
    marginBottom: 32,
  },
  navigationButtons: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  personalMessage: {
    fontSize: 16,
    fontStyle: "italic",
    fontWeight: "500",
    lineHeight: 22,
    marginTop: 16,
    textAlign: "center",
  },
  primaryButton: {
    borderRadius: 20,
    elevation: 4,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  primaryButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginRight: 8,
  },
  rightButtons: {
    alignItems: "center",
    flexDirection: "row",
    gap: 16,
  },
  sectionLabel: {
    fontSize: 19,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginBottom: 16,
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  skipButtonText: {
    fontSize: 15,
    fontWeight: "600",
  },
  stepContainer: {
    flex: 1,
    justifyContent: "center",
  },
  stepHeader: {
    alignItems: "center",
    marginBottom: 32,
  },
  stepIcon: {
    alignItems: "center",
    borderRadius: 44,
    elevation: 4,
    height: 88,
    justifyContent: "center",
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    width: 88,
  },
  subtitle: {
    fontSize: 19,
    fontWeight: "500",
    lineHeight: 26,
    marginBottom: 16,
    textAlign: "center",
  },
  title: {
    fontSize: 34,
    fontWeight: "800",
    letterSpacing: -0.8,
    marginBottom: 12,
    textAlign: "center",
  },
  unitChip: {
    borderRadius: 24,
    borderWidth: 2,
    marginRight: 8,
    paddingHorizontal: 18,
    paddingVertical: 10,
  },
  unitContainer: {
    marginTop: 16,
  },
  unitLabel: {
    fontSize: 15,
    fontWeight: "600",
    marginBottom: 8,
  },
  unitScrollView: {
    flexDirection: "row",
  },
  unitText: {
    fontSize: 14,
    fontWeight: "600",
    letterSpacing: -0.1,
  },
  usageAmountContainer: {
    flex: 1,
  },
  usageInput: {
    borderRadius: 20,
    borderWidth: 2,
    elevation: 2,
    fontSize: 17,
    fontWeight: "500",
    paddingHorizontal: 24,
    paddingVertical: 18,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  usageInputRow: {
    gap: 16,
  },
  usageSection: {
    marginBottom: 32,
  },
});
