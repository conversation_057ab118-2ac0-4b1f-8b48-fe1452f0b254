import React from 'react';
import { View, Text, TouchableOpacity, Modal, ActivityIndicator, ScrollView, StyleSheet } from 'react-native';
import { useDatabaseContext } from '@/context/database-context';

interface MigrationModalProps {
  visible: boolean;
  onClose: () => void;
}

export const MigrationModal: React.FC<MigrationModalProps> = ({ visible, onClose }) => {
  const { migration } = useDatabaseContext();

  const handleMigrate = async () => {
    await migration.runMigration();
    if (migration.migrationResult?.success) {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Text style={styles.title}>Database Migration</Text>
          
          <ScrollView style={styles.content}>
            {migration.isChecking && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#007AFF" />
                <Text style={styles.loadingText}>Checking migration status...</Text>
              </View>
            )}

            {migration.isMigrating && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#007AFF" />
                <Text style={styles.loadingText}>Migrating data to SQLite...</Text>
              </View>
            )}

            {!migration.isChecking && !migration.isMigrating && migration.needsMigration && (
              <View>
                <Text style={styles.text}>
                  We found existing data stored in the old format. Would you like to migrate it to the new SQLite database for better performance?
                </Text>
                <Text style={styles.subtext}>
                  This process is safe and will preserve all your data.
                </Text>
              </View>
            )}

            {migration.migrationResult && (
              <View>
                <Text style={[
                  styles.text,
                  { color: migration.migrationResult.success ? '#28a745' : '#dc3545' }
                ]}>
                  {migration.migrationResult.success ? 'Migration completed successfully!' : 'Migration completed with errors'}
                </Text>
                
                {migration.migrationResult.migratedStores.length > 0 && (
                  <View>
                    <Text style={styles.subtext}>Migrated stores:</Text>
                    {migration.migrationResult.migratedStores.map((store, index) => (
                      <Text key={index} style={styles.listItem}>• {store}</Text>
                    ))}
                    <Text style={styles.subtext}>
                      Total records: {migration.migrationResult.totalRecords}
                    </Text>
                  </View>
                )}

                {migration.migrationResult.errors.length > 0 && (
                  <View>
                    <Text style={[styles.subtext, { color: '#dc3545' }]}>Errors:</Text>
                    {migration.migrationResult.errors.map((error, index) => (
                      <Text key={index} style={[styles.listItem, { color: '#dc3545' }]}>• {error}</Text>
                    ))}
                  </View>
                )}
              </View>
            )}

            {migration.error && (
              <Text style={[styles.text, { color: '#dc3545' }]}>
                Error: {migration.error}
              </Text>
            )}
          </ScrollView>

          <View style={styles.actions}>
            {!migration.isChecking && !migration.isMigrating && migration.needsMigration && !migration.migrationResult && (
              <>
                <TouchableOpacity
                  style={[styles.button, styles.buttonSecondary]}
                  onPress={onClose}
                >
                  <Text style={styles.buttonTextSecondary}>Skip for now</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.button, styles.buttonPrimary]}
                  onPress={handleMigrate}
                >
                  <Text style={styles.buttonTextPrimary}>Migrate Data</Text>
                </TouchableOpacity>
              </>
            )}

            {(migration.migrationResult || migration.error || (!migration.needsMigration && !migration.isChecking)) && (
              <TouchableOpacity
                style={[styles.button, styles.buttonPrimary]}
                onPress={onClose}
              >
                <Text style={styles.buttonTextPrimary}>Close</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  actions: {
    flexDirection: 'row',
    gap: 12,
    justifyContent: 'flex-end',
  },
  button: {
    borderRadius: 8,
    minWidth: 100,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  buttonPrimary: {
    backgroundColor: '#007AFF',
  },
  buttonSecondary: {
    backgroundColor: '#f0f0f0',
    borderColor: '#ddd',
    borderWidth: 1,
  },
  buttonTextPrimary: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  buttonTextSecondary: {
    color: '#333',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    maxHeight: '80%',
    maxWidth: 400,
    padding: 20,
    width: '100%',
  },
  content: {
    marginBottom: 20,
    maxHeight: 300,
  },
  listItem: {
    color: '#666',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
    marginLeft: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    color: '#666',
    fontSize: 16,
    marginTop: 12,
    textAlign: 'center',
  },
  overlay: {
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  subtext: {
    color: '#666',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  text: {
    color: '#333',
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 12,
  },
  title: {
    color: '#333',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
}); 