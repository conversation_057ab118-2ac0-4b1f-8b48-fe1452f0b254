import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, FlatList } from 'react-native';
import { 
  <PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MindfulnessExercise } from "@/types/mindfulness";
import { ColorScheme } from "@/hooks/ui/useColorScheme";

interface ExerciseListProps {
  colors: ColorScheme;
  language: string;
  exercises: MindfulnessExercise[];
  favoriteExercises: string[];
  onSelectExercise: (exercise: MindfulnessExercise) => void;
  onToggleFavorite: (id: string) => void;
  _onEditExercise?: (id: string) => void;
  _onDeleteExercise?: (id: string) => void;
}

export const ExerciseList: React.FC<ExerciseListProps> = ({ 
  colors, 
  language, 
  exercises,
  favoriteExercises,
  onSelectExercise,
  onToggleFavorite,
  _onEditExercise,
  _onDeleteExercise
}) => {
  
  const getExerciseIcon = (type: string) => {
    switch (type) {
      case 'breathing':
        return Wind;
      case 'meditation':
        return Brain;
      case 'grounding':
        return Anchor;
      case 'bodyscan':
        return Scan;
      case 'visualization':
        return Eye;
      case 'gratitude':
        return Heart;
      case 'muscle-relaxation':
        return Dumbbell;
      case 'custom':
      default:
        return Sparkles;
    }
  };
  
  const getExerciseTypeLabel = (type: string) => {
    if (language === 'nl') {
      switch (type) {
        case 'breathing': return 'Ademhaling';
        case 'meditation': return 'Meditatie';
        case 'grounding': return 'Grounding';
        case 'bodyscan': return 'Lichaamsscan';
        case 'visualization': return 'Visualisatie';
        case 'gratitude': return 'Dankbaarheid';
        case 'muscle-relaxation': return 'Spierontspanning';
        case 'custom': return 'Aangepast';
        default: return 'Aangepast';
      }
    } else {
      switch (type) {
        case 'breathing': return 'Breathing';
        case 'meditation': return 'Meditation';
        case 'grounding': return 'Grounding';
        case 'bodyscan': return 'Body Scan';
        case 'visualization': return 'Visualization';
        case 'gratitude': return 'Gratitude';
        case 'muscle-relaxation': return 'Muscle Relaxation';
        case 'custom': return 'Custom';
        default: return 'Custom';
      }
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'breathing': return colors.primary;
      case 'meditation': return colors.secondary;
      case 'grounding': return colors.accent;
      case 'bodyscan': return colors.success;
      case 'visualization': return colors.info;
      case 'gratitude': return colors.warning;
      case 'muscle-relaxation': return colors.danger;
      default: return colors.muted;
    }
  };

  const getTypeGradient = (type: string) => {
    const baseColor = getTypeColor(type);
    return [baseColor, baseColor + 'CC'] as const;
  };
  
  const renderExerciseItem = ({ item }: { item: MindfulnessExercise }) => {
    const isFavorite = favoriteExercises.includes(item.id);
    const IconComponent = getExerciseIcon(item.type);
    const typeColor = getTypeColor(item.type);
    const typeGradient = getTypeGradient(item.type);
    
    return (
      <TouchableOpacity 
        style={[
          styles.exerciseItem, 
          { backgroundColor: colors.card, borderColor: colors.border }
        ]}
        onPress={() => onSelectExercise(item)}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={[colors.card || colors.background, colors.cardGradient || colors.card || colors.background] as const}
          style={styles.exerciseGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.exerciseContent}>
            <View style={styles.exerciseMainContent}>
              <View style={[styles.exerciseIconContainer, { backgroundColor: typeColor + '20' }]}>
                <IconComponent size={28} color={typeColor} strokeWidth={2.5} />
              </View>
              
              <View style={styles.exerciseInfo}>
                <Text style={[styles.exerciseTitle, { color: colors.text }]}>
                  {item.title}
                </Text>
                
                <View style={styles.exerciseDetails}>
                  <View style={[styles.exerciseTypeContainer, { backgroundColor: typeColor + '15' }]}>
                    <Text style={[styles.exerciseType, { color: typeColor }]}>
                      {getExerciseTypeLabel(item.type)}
                    </Text>
                  </View>
                  
                  {item.duration && (
                    <View style={styles.exerciseDurationContainer}>
                      <Clock size={14} color={colors.muted} strokeWidth={2} />
                      <Text style={[styles.exerciseDuration, { color: colors.textSecondary }]}>
                        {item.duration} {language === 'nl' ? 'min' : 'min'}
                      </Text>
                    </View>
                  )}
                  
                  {item.isCustom && (
                    <View style={[styles.customBadge, { backgroundColor: colors.accent + '20' }]}>
                      <Text style={[styles.customBadgeText, { color: colors.accent }]}>
                        {language === 'nl' ? 'Aangepast' : 'Custom'}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </View>
            
            <View style={styles.exerciseActions}>
              <TouchableOpacity 
                style={[
                  styles.favoriteButton,
                  isFavorite && { backgroundColor: colors.danger + '15' }
                ]}
                onPress={() => onToggleFavorite(item.id)}
              >
                <Star 
                  size={20} 
                  color={isFavorite ? colors.danger : colors.muted} 
                  fill={isFavorite ? colors.danger : 'transparent'}
                  strokeWidth={2.5}
                />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.largeStartButton, { backgroundColor: typeColor }]}
                onPress={() => onSelectExercise(item)}
              >
                <Text style={styles.largeStartButtonText}>{language === 'nl' ? 'Start' : 'Start'}</Text>
              </TouchableOpacity>
            </View>
          </View>
          
          {/* Progress indicator for completed exercises */}
          <View style={[styles.progressIndicator, { backgroundColor: typeColor + '10' }]}>
            <LinearGradient
              colors={typeGradient}
              style={[styles.progressBar, styles.progressBarWidth]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };
  
  return (
    <View style={styles.container}>
      {exercises.length === 0 ? (
        <View style={[styles.emptyState, { borderColor: colors.border, backgroundColor: colors.card }]}>
          <LinearGradient
            colors={[colors.card || colors.background, colors.cardGradient || colors.card || colors.background] as const}
            style={styles.emptyStateGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={[styles.emptyIconContainer, { backgroundColor: colors.muted + '20' }]}>
              <Brain size={56} color={colors.muted} strokeWidth={1.5} />
            </View>
            <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
              {language === 'nl' ? 'Geen oefeningen gevonden' : 'No exercises found'}
            </Text>
            <Text style={[styles.emptyStateDescription, { color: colors.textSecondary }]}>
              {language === 'nl' 
                ? 'Voeg een nieuwe oefening toe om te beginnen.' 
                : 'Add a new exercise to get started.'}
            </Text>
          </LinearGradient>
        </View>
      ) : (
        <FlatList
          data={exercises}
          renderItem={renderExerciseItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({


  container: {
    flex: 1,
  },
  customBadge: {
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  customBadgeText: {
    fontSize: 12,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
  emptyIconContainer: {
    alignItems: 'center',
    borderRadius: 24,
    height: 96,
    justifyContent: 'center',
    marginBottom: 20,
    width: 96,
  },
  emptyState: {
    borderRadius: 24,
    borderStyle: 'dashed',
    borderWidth: 2,
    elevation: 4,
    margin: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  emptyStateDescription: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 22,
    textAlign: 'center',
  },
  emptyStateGradient: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 48,
  },
  emptyStateTitle: {
    fontSize: 22,
    fontWeight: '800',
    letterSpacing: -0.4,
    marginBottom: 8,
    textAlign: 'center',
  },
  exerciseActions: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  exerciseContent: {
    marginBottom: 12,
  },
  exerciseDetails: {
    alignItems: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  exerciseDuration: {
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: -0.1,
  },
  exerciseDurationContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  exerciseGradient: {
    padding: 20,
    width: '100%',
  },
  exerciseIconContainer: {
    alignItems: 'center',
    borderRadius: 20,
    height: 60,
    justifyContent: 'center',
    marginRight: 16,
    width: 60,
  },
  exerciseInfo: {
    flex: 1,
  },
  exerciseItem: {
    borderRadius: 20,
    borderWidth: 1,
    elevation: 4,
    marginBottom: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  exerciseMainContent: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 16,
  },
  exerciseTitle: {
    fontSize: 20,
    fontWeight: '800',
    letterSpacing: -0.4,
    marginBottom: 8,
  },
  exerciseType: {
    fontSize: 13,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
  exerciseTypeContainer: {
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  favoriteButton: {
    borderRadius: 16,
    padding: 12,
  },
  largeStartButton: {
    alignItems: 'center',
    borderRadius: 20,
    elevation: 3,
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  largeStartButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  listContent: {
    paddingBottom: 20,
  },
  progressBar: {
    borderRadius: 2,
    height: '100%',
  },
  progressBarWidth: {
    width: '75%',
  },
  progressIndicator: {
    borderRadius: 2,
    height: 4,
    overflow: 'hidden',
  },


});