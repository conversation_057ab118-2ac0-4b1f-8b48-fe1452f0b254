import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { StyleSheet, Text, View, FlatList, TouchableOpacity, Platform, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { VoiceControl } from './VoiceControl';
import { MindfulnessExercise } from "@/types/mindfulness";
import { useMindfulnessTimer, useTextFadeAnimation } from '@/hooks';
import { ColorScheme } from '@/hooks/ui/useColorScheme';
interface GroundingExerciseProps {
  colors: ColorScheme;
  language: string;
  activeExercise: MindfulnessExercise;
  onDurationUpdate?: (seconds: number) => void;
}
export const GroundingExercise: React.FC<GroundingExerciseProps> = ({ colors, language, onDurationUpdate }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  
  // Use the new timer hook
  const {
    exerciseState,
    timerSeconds,
    toggleTimer: baseToggleTimer,
    resetTimer: baseResetTimer,
    formatTime,
    timerRunning,
  } = useMindfulnessTimer({ onDurationUpdate });
  
  // Grounding steps
  const groundingSteps = useMemo(() => language === 'nl' 
    ? [
        'Kijk om je heen en benoem vijf dingen die je kunt zien.',
        'Let op vier dingen die je kunt voelen (zoals je kleding op je huid).',
        'Luister naar drie geluiden in je omgeving.',
        'Benoem twee geuren die je kunt ruiken of herinneren.',
        'Benoem één smaak die je nu proeft of je kunt herinneren.'
      ]
    : [
        'Look around and name five things you can see.',
        'Pay attention to four things you can feel (like your clothes on your skin).',
        'Listen for three sounds in your environment.',
        'Name two scents you can smell or remember.',
        'Name one taste you can taste now or remember.'
      ], [language]);
  // Effect to change step when timerSeconds changes
  useEffect(() => {
    // Change grounding step every 30 seconds
    if (timerRunning && timerSeconds > 0 && timerSeconds % 30 === 0) {
      setCurrentStep(prevStep => (prevStep + 1) % groundingSteps.length);
    }
  }, [timerSeconds, timerRunning, groundingSteps.length]);
  const getGroundingInstructions = useCallback(() => {
    if (exerciseState === 'idle') {
      return language === 'nl' ? 'Druk op start om te beginnen' : 'Press start to begin';
    }
    if (exerciseState === 'paused') {
      return language === 'nl' ? 'Gepauzeerd' : 'Paused';
    }
    return groundingSteps[currentStep];
  }, [exerciseState, currentStep, language, groundingSteps]);
  // Use the text fade animation hook
  const { animatedStyle, fadeToNewText } = useTextFadeAnimation();
  const [displayedText, setDisplayedText] = useState(() => getGroundingInstructions());
  useEffect(() => {
    const newInstruction = getGroundingInstructions();
    if (newInstruction !== displayedText) {
      fadeToNewText(newInstruction, setDisplayedText);
    }
  }, [getGroundingInstructions, displayedText, fadeToNewText]);
  const toggleTimer = () => {
    baseToggleTimer();
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  const resetTimer = () => {
    baseResetTimer();
    setCurrentStep(0);
    setDisplayedText(language === 'nl' ? 'Druk op start om te beginnen' : 'Press start to begin');
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };
  const toggleVoiceGuidance = () => {
    setVoiceEnabled(prev => !prev);
  };
  // Grounding steps data
  const groundingStepsData = [
    {
      number: '5',
      title: language === 'nl' ? 'Dingen die je kunt ZIEN' : 'Things you can SEE',
      description: language === 'nl' 
        ? 'Kijk om je heen en benoem vijf dingen die je kunt zien.'
        : 'Look around and name five things you can see.'
    },
    {
      number: '4',
      title: language === 'nl' ? 'Dingen die je kunt VOELEN' : 'Things you can FEEL',
      description: language === 'nl' 
        ? 'Let op vier dingen die je kunt voelen (zoals je kleding op je huid).'
        : 'Pay attention to four things you can feel (like your clothes on your skin).'
    },
    {
      number: '3',
      title: language === 'nl' ? 'Dingen die je kunt HOREN' : 'Things you can HEAR',
      description: language === 'nl' 
        ? 'Luister naar drie geluiden in je omgeving.'
        : 'Listen for three sounds in your environment.'
    },
    {
      number: '2',
      title: language === 'nl' ? 'Dingen die je kunt RUIKEN' : 'Things you can SMELL',
      description: language === 'nl' 
        ? 'Benoem twee geuren die je kunt ruiken of herinneren.'
        : 'Name two scents you can smell or remember.'
    },
    {
      number: '1',
      title: language === 'nl' ? 'Iets dat je kunt PROEVEN' : 'Something you can TASTE',
      description: language === 'nl' 
        ? 'Benoem één smaak die je nu proeft of je kunt herinneren.'
        : 'Name one taste you can taste now or remember.'
    }
  ];
  // Render grounding step item
  const renderGroundingStepItem = ({ item }: { item: typeof groundingStepsData[0] }) => (
    <View style={[styles.groundingStep, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <Text style={[styles.groundingStepNumber, { color: colors.primary }]}>{item.number}</Text>
      <View style={styles.groundingStepContent}>
        <Text style={[styles.groundingStepTitle, { color: colors.text }]}>
          {item.title}
        </Text>
        <Text style={[styles.groundingStepDescription, { color: colors.muted }]}>
          {item.description}
        </Text>
      </View>
    </View>
  );
  
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>
        {language === 'nl' ? '5-4-3-2-1 Grounding Techniek' : '5-4-3-2-1 Grounding Technique'}
      </Text>
      <Text style={[styles.description, { color: colors.muted }]}>
        {language === 'nl'
          ? 'Deze oefening helpt je om in het moment te blijven en angst te verminderen door je zintuigen te gebruiken.'
          : 'This exercise helps you stay in the present moment and reduce anxiety by using your senses.'}
      </Text>
      <VoiceControl
        colors={colors}
        language={language}
        currentInstruction={getGroundingInstructions()}
        isEnabled={voiceEnabled}
        onToggle={toggleVoiceGuidance}
      />
      {exerciseState !== 'idle' ? (
        <>
          <View style={[styles.currentStepCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <Animated.Text style={[styles.currentStepText, { color: colors.text }, animatedStyle]}>
              {displayedText}
            </Animated.Text>
            <Text style={[styles.stepIndicator, { color: colors.primary }]}>
              {currentStep + 1}/{groundingSteps.length}
            </Text>
          </View>
          <View style={styles.groundingStepsContainer}>
            <FlatList
              data={groundingStepsData}
              renderItem={renderGroundingStepItem}
              keyExtractor={(item) => item.number}
              scrollEnabled={true}
              style={styles.groundingStepsList}
            />
          </View>
          <View style={styles.timerControls}>
            <TouchableOpacity
              style={[styles.timerButton, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={resetTimer}
            >
              <Ionicons name="refresh" size={24} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.timerButton, { backgroundColor: colors.primary }]}
              onPress={toggleTimer}
            >
              {exerciseState === 'running' ? (
                <Ionicons name="pause" size={24} color="#fff" />
              ) : (
                <Ionicons name="play" size={24} color="#fff" />
              )}
            </TouchableOpacity>
            <View style={[styles.timerDisplay, { backgroundColor: colors.card, borderColor: colors.border }]}>
              <Text style={[styles.timerText, { color: colors.text }]}>{formatTime(timerSeconds)}</Text>
            </View>
          </View>
        </>
      ) : (
        <View style={styles.placeholderContainer}>
          <Text style={[styles.placeholderText, { color: colors.muted }]}>
            {language === 'nl' ? 'Klaar om te beginnen?' : 'Ready to begin?'}
          </Text>
          <TouchableOpacity
            style={[styles.largeStartButton, { backgroundColor: colors.success }]}
            onPress={toggleTimer}
          >
            <Text style={styles.largeStartButtonText}>{language === 'nl' ? 'Start' : 'Start'}</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({


  container: {
    marginBottom: 30,
  },
  currentStepCard: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    padding: 16,
  },
  currentStepText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
  },
  groundingStep: {
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 12,
    padding: 16,
  },
  groundingStepContent: {
    flex: 1,
  },
  groundingStepDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  groundingStepNumber: {
    fontSize: 24,
    fontWeight: '700',
    marginRight: 16,
  },
  groundingStepTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  groundingStepsContainer: {
    marginBottom: 24,
  },
  groundingStepsList: {
    maxHeight: 300,
  },
  largeStartButton: {
    alignItems: 'center',
    borderRadius: 25,
    elevation: 3,
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  largeStartButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholderContainer: {
    alignItems: 'center',
    height: 250,
    justifyContent: 'center',
    marginBottom: 30,
  },
  placeholderText: {
    fontSize: 18,
    fontStyle: 'italic',
    marginBottom: 20,
  },
  stepIndicator: {
    fontSize: 14,
    fontWeight: '600',
  },
  timerButton: {
    alignItems: 'center',
    borderRadius: 30,
    borderWidth: 1,
    height: 60,
    justifyContent: 'center',
    marginHorizontal: 12,
    width: 60,
  },
  timerControls: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  timerDisplay: {
    borderRadius: 20,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  timerText: {
    fontSize: 18,
    fontWeight: '600',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },


});