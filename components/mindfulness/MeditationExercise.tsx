import React from "react";
import { useState, useEffect, useCallback, useMemo } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Platform, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { VoiceControl } from './VoiceControl';
import { MindfulnessExercise } from "@/types/mindfulness";
import { useMindfulnessTimer, useTextFadeAnimation } from '@/hooks';
import { ColorScheme } from '@/hooks/ui/useColorScheme';
interface MeditationExerciseProps {
  colors: ColorScheme;
  language: string;
  activeExercise: MindfulnessExercise;
  onDurationUpdate?: (seconds: number) => void;
}
export const MeditationExercise: React.FC<MeditationExerciseProps> = ({ colors, language, onDurationUpdate }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  
  // Use the new timer hook
  const {
    exerciseState,
    timerSeconds,
    toggleTimer: baseToggleTimer,
    resetTimer: baseResetTimer,
    formatTime,
    timerRunning,
  } = useMindfulnessTimer({ onDurationUpdate });
  // Meditation steps
  const meditationSteps = useMemo(() => language === 'nl' 
    ? [
        'Neem een comfortabele positie aan. Sluit je ogen.',
        'Adem diep in door je neus, en langzaam uit door je mond.',
        'Voel hoe je lichaam ontspant met elke uitademing.',
        'Breng je aandacht naar je ademhaling. Volg elke ademhaling.',
        'Als je gedachten afdwalen, breng je aandacht rustig terug naar je ademhaling.',
        'Scan je lichaam van top tot teen en merk spanning op.',
        'Laat bij elke uitademing de spanning los.',
        'Blijf je concentreren op je ademhaling.',
        'Merk op hoe je lichaam en geest rustiger worden.',
        'Blijf in dit moment, volledig aanwezig.',
      ]
    : [
        'Find a comfortable position. Close your eyes.',
        'Breathe deeply in through your nose, and slowly out through your mouth.',
        'Feel your body relax with each exhale.',
        'Bring your attention to your breathing. Follow each breath.',
        'If your mind wanders, gently bring your attention back to your breathing.',
        'Scan your body from head to toe, noticing any tension.',
        'With each exhale, release that tension.',
        'Continue to focus on your breathing.',
        'Notice how your body and mind become calmer.',
        'Stay in this moment, fully present.',
      ], [language]);
  // Effect to change step when timerSeconds changes
  useEffect(() => {
    // Change meditation step every 30 seconds
    if (timerRunning && timerSeconds > 0 && timerSeconds % 30 === 0) {
      setCurrentStep(prevStep => (prevStep + 1) % meditationSteps.length);
    }
  }, [timerSeconds, timerRunning, meditationSteps.length]);
  const getMeditationInstructions = useCallback(() => {
    if (exerciseState === 'idle') {
      return language === 'nl' ? 'Druk op start om te beginnen' : 'Press start to begin';
    }
    if (exerciseState === 'paused') {
      return language === 'nl' ? 'Gepauzeerd' : 'Paused';
    }
    return meditationSteps[currentStep];
  }, [exerciseState, currentStep, language, meditationSteps]);
  // Use the text fade animation hook
  const { animatedStyle, fadeToNewText } = useTextFadeAnimation();
  const [displayedText, setDisplayedText] = useState(() => getMeditationInstructions());
  useEffect(() => {
    const newInstruction = getMeditationInstructions();
    if (newInstruction !== displayedText) {
      fadeToNewText(newInstruction, setDisplayedText);
    }
  }, [getMeditationInstructions, displayedText, fadeToNewText]);
  const toggleTimer = () => {
    baseToggleTimer();
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  const resetTimer = () => {
    baseResetTimer();
    setCurrentStep(0);
    setDisplayedText(language === 'nl' ? 'Druk op start om te beginnen' : 'Press start to begin');
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };
  const toggleVoiceGuidance = () => {
    setVoiceEnabled(prev => !prev);
  };
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>
        {language === 'nl' ? 'Geleide Meditatie' : 'Guided Meditation'}
      </Text>
      <Text style={[styles.description, { color: colors.muted }]}>
        {language === 'nl'
          ? 'Neem een comfortabele positie aan. Sluit je ogen en focus op je ademhaling. Laat gedachten komen en gaan zonder oordeel.'
          : 'Find a comfortable position. Close your eyes and focus on your breathing. Let thoughts come and go without judgment.'}
      </Text>
      <VoiceControl
        colors={colors}
        language={language}
        currentInstruction={getMeditationInstructions()}
        isEnabled={voiceEnabled}
        onToggle={toggleVoiceGuidance}
      />
      {exerciseState !== 'idle' ? (
        <View style={[styles.meditationCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <Animated.Text style={[styles.meditationInstructions, { color: colors.text }, animatedStyle]}>
            {displayedText}
          </Animated.Text>
        </View>
      ) : (
        <View style={styles.placeholderContainer}>
          <Text style={[styles.placeholderText, { color: colors.muted }]}>
            {language === 'nl' ? 'Klaar om te beginnen?' : 'Ready to begin?'}
          </Text>
          <TouchableOpacity
            style={[styles.largeStartButton, { backgroundColor: colors.success }]}
            onPress={toggleTimer}
          >
            <Text style={styles.largeStartButtonText}>{language === 'nl' ? 'Start' : 'Start'}</Text>
          </TouchableOpacity>
        </View>
      )}
      {exerciseState !== 'idle' && (
        <View style={styles.timerControls}>
          <TouchableOpacity
            style={[styles.timerButton, { backgroundColor: colors.card, borderColor: colors.border }]}
            onPress={resetTimer}
          >
            <Ionicons name="refresh" size={24} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.timerButton, { backgroundColor: colors.primary }]}
            onPress={toggleTimer}
          >
            {exerciseState === 'running' ? (
              <Ionicons name="pause" size={24} color="#fff" />
            ) : (
              <Ionicons name="play" size={24} color="#fff" />
            )}
          </TouchableOpacity>
          <View style={[styles.timerDisplay, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <Text style={[styles.timerText, { color: colors.text }]}>{formatTime(timerSeconds)}</Text>
          </View>
        </View>
      )}
      {exerciseState !== 'idle' && (
        <View style={styles.progressContainer}>
          <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${((currentStep / (meditationSteps.length - 1)) * 100)}%`,
                  backgroundColor: colors.primary
                }
              ]}
            />
          </View>
          <Text style={[styles.progressText, { color: colors.muted }]}>
            {currentStep + 1}/{meditationSteps.length}
          </Text>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({


  container: {
    marginBottom: 30,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
  },
  largeStartButton: {
    alignItems: 'center',
    borderRadius: 25,
    elevation: 3,
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  largeStartButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  meditationCard: {
    borderRadius: 12,
    borderWidth: 1,
    justifyContent: 'center',
    marginBottom: 24,
    minHeight: 120,
    padding: 20,
  },
  meditationInstructions: {
    fontSize: 16,
    fontStyle: 'italic',
    lineHeight: 24,
    textAlign: 'center',
  },
  placeholderContainer: {
    alignItems: 'center',
    height: 250,
    justifyContent: 'center',
    marginBottom: 30,
  },
  placeholderText: {
    fontSize: 18,
    fontStyle: 'italic',
    marginBottom: 20,
  },
  progressBar: {
    borderRadius: 3,
    height: 6,
    marginBottom: 8,
    overflow: 'hidden',
    width: '100%',
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressFill: {
    borderRadius: 3,
    height: '100%',
  },
  progressText: {
    fontSize: 14,
  },
  timerButton: {
    alignItems: 'center',
    borderRadius: 30,
    borderWidth: 1,
    height: 60,
    justifyContent: 'center',
    marginHorizontal: 12,
    width: 60,
  },
  timerControls: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  timerDisplay: {
    borderRadius: 20,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  timerText: {
    fontSize: 18,
    fontWeight: '600',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },


});