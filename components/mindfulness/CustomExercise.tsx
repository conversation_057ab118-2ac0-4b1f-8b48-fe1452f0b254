import React from "react";
import { useState, useEffect, useCallback } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Platform, FlatList, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { MindfulnessExercise } from "@/types/mindfulness";
import { VoiceControl } from './VoiceControl';
import { useMindfulnessTimer, useTextFadeAnimation } from '@/hooks';
import { ColorScheme } from '@/hooks/ui/useColorScheme';
interface CustomExerciseProps {
  colors: ColorScheme;
  language: string;
  exercise: MindfulnessExercise;
  onDurationUpdate?: (seconds: number) => void;
}
export const CustomExercise: React.FC<CustomExerciseProps> = ({ colors, language, exercise, onDurationUpdate }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  
  // Use the new timer hook
  const {
    exerciseState,
    timerSeconds,
    toggleTimer: baseToggleTimer,
    resetTimer: baseResetTimer,
    formatTime,
    timerRunning,
  } = useMindfulnessTimer({ onDurationUpdate });
  // Effect to change step when timerSeconds changes
  useEffect(() => {
    // Change step every 30 seconds if there are multiple steps
    if (timerRunning && timerSeconds > 0 && timerSeconds % 30 === 0 && exercise.steps && exercise.steps.length > 1) {
      setCurrentStep(prevStep => (prevStep + 1) % exercise.steps!.length);
    }
  }, [timerSeconds, timerRunning, exercise.steps]);
  const getCustomExerciseInstructions = useCallback(() => {
    if (exerciseState === 'idle') {
      return language === 'nl' ? 'Druk op start om te beginnen' : 'Press start to begin';
    }
    if (exerciseState === 'paused') {
      return language === 'nl' ? 'Gepauzeerd' : 'Paused';
    }
    if (exercise.steps && exercise.steps.length > 0) {
      return exercise.steps[currentStep];
    }
    return exercise.description || '';
  }, [exerciseState, currentStep, language, exercise.steps, exercise.description]);
  // Use the text fade animation hook
  const { animatedStyle, fadeToNewText } = useTextFadeAnimation();
  const [displayedText, setDisplayedText] = useState(() => getCustomExerciseInstructions());
  useEffect(() => {
    const newInstruction = getCustomExerciseInstructions();
    if (newInstruction !== displayedText) {
      fadeToNewText(newInstruction, setDisplayedText);
    }
  }, [getCustomExerciseInstructions, displayedText, fadeToNewText]);
  const toggleTimer = () => {
    baseToggleTimer();
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  const resetTimer = () => {
    baseResetTimer();
    setCurrentStep(0);
    setDisplayedText(language === 'nl' ? 'Druk op start om te beginnen' : 'Press start to begin');
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };
  const toggleVoiceGuidance = () => {
    setVoiceEnabled(prev => !prev);
  };
  const goToNextStep = () => {
    if (exercise.steps && exercise.steps.length > 0) {
      setCurrentStep(prevStep => (prevStep + 1) % exercise.steps!.length);
    }
  };
  const goToPrevStep = () => {
    if (exercise.steps && exercise.steps.length > 0) {
      setCurrentStep(prevStep => (prevStep - 1 + exercise.steps!.length) % exercise.steps!.length);
    }
  };
  // Render step item
  const renderStepItem = ({ item, index }: { item: string; index: number }) => (
    <View 
      style={[
        styles.stepItem, 
        currentStep === index && styles.activeStepItem,
        { 
          backgroundColor: currentStep === index ? colors.primary + '20' : colors.card,
          borderColor: currentStep === index ? colors.primary : colors.border
        }
      ]}
    >
      <Text style={[
        styles.stepNumber, 
        { color: currentStep === index ? colors.primary : colors.muted }
      ]}>
        {index + 1}
      </Text>
      <Text style={[
        styles.stepText, 
        { color: currentStep === index ? colors.primary : colors.text }
      ]}>
        {item}
      </Text>
    </View>
  );
  
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>
        {exercise.title}
      </Text>
      <Text style={[styles.description, { color: colors.muted }]}>
        {exercise.description}
      </Text>
      <VoiceControl
        colors={colors}
        language={language}
        currentInstruction={getCustomExerciseInstructions()}
        isEnabled={voiceEnabled}
        onToggle={toggleVoiceGuidance}
      />
      {exerciseState !== 'idle' ? (
        <>
          {exercise.steps && exercise.steps.length > 0 ? (
            <>
              <View style={[styles.stepCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
                <TouchableOpacity onPress={goToPrevStep} style={styles.stepNavButton}>
                  <Ionicons name="chevron-back" size={24} color={colors.primary} />
                </TouchableOpacity>
                <View style={styles.currentStepContainer}>
                  <Animated.Text style={[styles.currentStepText, { color: colors.text }, animatedStyle]}>
                    {displayedText}
                  </Animated.Text>
                  <Text style={[styles.stepIndicator, { color: colors.muted }]}>
                    {currentStep + 1}/{exercise.steps.length}
                  </Text>
                </View>
                <TouchableOpacity onPress={goToNextStep} style={styles.stepNavButton}>
                  <Ionicons name="chevron-forward" size={24} color={colors.primary} />
                </TouchableOpacity>
              </View>
              <View style={styles.stepsContainer}>
                <FlatList
                  data={exercise.steps}
                  renderItem={renderStepItem}
                  keyExtractor={(item, index) => `step-${index}`}
                  style={styles.stepsList}
                />
              </View>
            </>
          ) : exercise.customInstructions ? (
            <View style={[styles.instructionsCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
              <Animated.Text style={[styles.instructionsText, { color: colors.text }, animatedStyle]}>
                {displayedText}
              </Animated.Text>
            </View>
          ) : null}
          <View style={styles.timerControls}>
            <TouchableOpacity
              style={[styles.timerButton, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={resetTimer}
            >
              <Ionicons name="refresh" size={24} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.timerButton, { backgroundColor: colors.primary }]}
              onPress={toggleTimer}
            >
              {exerciseState === 'running' ? (
                <Ionicons name="pause" size={24} color="#fff" />
              ) : (
                <Ionicons name="play" size={24} color="#fff" />
              )}
            </TouchableOpacity>
            <View style={[styles.timerDisplay, { backgroundColor: colors.card, borderColor: colors.border }]}>
              <Text style={[styles.timerText, { color: colors.text }]}>{formatTime(timerSeconds)}</Text>
            </View>
          </View>
          {exercise.duration && (
            <View style={styles.durationContainer}>
              <Ionicons name="time-outline" size={16} color={colors.muted} />
              <Text style={[styles.durationText, { color: colors.muted }]}>
                {language === 'nl'
                  ? `Aanbevolen duur: ${exercise.duration} minuten`
                  : `Recommended duration: ${exercise.duration} minutes`}
              </Text>
            </View>
          )}
        </>
      ) : (
        <View style={styles.placeholderContainer}>
          <Text style={[styles.placeholderText, { color: colors.muted }]}>
            {language === 'nl' ? 'Klaar om te beginnen?' : 'Ready to begin?'}
          </Text>
          <TouchableOpacity
            style={[styles.largeStartButton, { backgroundColor: colors.success }]}
            onPress={toggleTimer}
          >
            <Text style={styles.largeStartButtonText}>{language === 'nl' ? 'Start' : 'Start'}</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({


  activeStepItem: {
    borderWidth: 2,
  },
  container: {
    marginBottom: 30,
  },
  currentStepContainer: {
    alignItems: 'center',
    flex: 1,
  },
  currentStepText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
  },
  durationContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  durationText: {
    fontSize: 14,
    marginLeft: 4,
  },
  instructionsCard: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 24,
    padding: 20,
  },
  instructionsText: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
  },
  largeStartButton: {
    alignItems: 'center',
    borderRadius: 25,
    elevation: 3,
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  largeStartButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholderContainer: {
    alignItems: 'center',
    height: 250,
    justifyContent: 'center',
    marginBottom: 30,
  },
  placeholderText: {
    fontSize: 18,
    fontStyle: 'italic',
    marginBottom: 20,
  },
  stepCard: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 16,
    padding: 16,
  },
  stepIndicator: {
    fontSize: 12,
  },
  stepItem: {
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 8,
    padding: 12,
  },
  stepNavButton: {
    padding: 8,
  },
  stepNumber: {
    fontSize: 16,
    fontWeight: '700',
    marginRight: 12,
    textAlign: 'center',
    width: 24,
  },
  stepText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  stepsContainer: {
    marginBottom: 24,
  },
  stepsList: {
    maxHeight: 200,
  },
  timerButton: {
    alignItems: 'center',
    borderRadius: 30,
    borderWidth: 1,
    height: 60,
    justifyContent: 'center',
    marginHorizontal: 12,
    width: 60,
  },
  timerControls: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 12,
  },
  timerDisplay: {
    borderRadius: 20,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  timerText: {
    fontSize: 18,
    fontWeight: '600',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },


});