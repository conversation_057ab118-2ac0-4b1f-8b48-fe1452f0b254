import React from "react";
import { useState, useEffect, useCallback, useMemo } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Platform, FlatList, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { VoiceControl } from './VoiceControl';
import { ColorScheme } from '@/hooks/ui/useColorScheme';
import { MindfulnessExercise } from "@/types/mindfulness";
import { useMindfulnessTimer, useTextFadeAnimation } from '@/hooks';
interface VisualizationExerciseProps {
  colors: ColorScheme;
  language: string;
  activeExercise: MindfulnessExercise;
  onDurationUpdate?: (seconds: number) => void;
}
export const VisualizationExercise: React.FC<VisualizationExerciseProps> = ({ colors, language, onDurationUpdate }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  
  // Use the new timer hook
  const {
    exerciseState,
    timerSeconds,
    toggleTimer: baseToggleTimer,
    resetTimer: baseResetTimer,
    formatTime,
    timerRunning,
  } = useMindfulnessTimer({ onDurationUpdate });
  // Visualization steps
  const visualizationSteps = useMemo(() => language === 'nl' 
    ? [
        'Sluit je ogen en stel je een rustige plek voor.',
        'Visualiseer de kleuren en vormen om je heen.',
        'Stel je de geluiden voor die je zou horen.',
        'Voel de texturen en temperatuur van deze plek.',
        'Adem diep in en voel je volledig ontspannen.',
        'Blijf nog even in deze vredige ruimte.',
      ]
    : [
        'Close your eyes and imagine a peaceful place.',
        'Visualize the colors and shapes around you.',
        'Imagine the sounds you would hear.',
        'Feel the textures and temperature of this place.',
        'Breathe deeply and feel completely relaxed.',
        'Stay in this peaceful space for a moment longer.',
      ], [language]);
  // Effect to change step when timerSeconds changes
  useEffect(() => {
    // Change visualization step every 45 seconds
    if (timerRunning && timerSeconds > 0 && timerSeconds % 45 === 0) {
      setCurrentStep(prevStep => (prevStep + 1) % visualizationSteps.length);
    }
  }, [timerSeconds, timerRunning, visualizationSteps.length]);
  const getVisualizationInstructions = useCallback(() => {
    if (exerciseState === 'idle') {
      return language === 'nl' ? 'Druk op start om te beginnen' : 'Press start to begin';
    }
    if (exerciseState === 'paused') {
      return language === 'nl' ? 'Gepauzeerd' : 'Paused';
    }
    return visualizationSteps[currentStep];
  }, [exerciseState, currentStep, language, visualizationSteps]);
  // Use the text fade animation hook
  const { animatedStyle, fadeToNewText } = useTextFadeAnimation();
  const [displayedText, setDisplayedText] = useState(() => getVisualizationInstructions());
  useEffect(() => {
    const newInstruction = getVisualizationInstructions();
    if (newInstruction !== displayedText) {
      fadeToNewText(newInstruction, setDisplayedText);
    }
  }, [getVisualizationInstructions, displayedText, fadeToNewText]);
  const toggleTimer = () => {
    baseToggleTimer();
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  const resetTimer = () => {
    baseResetTimer();
    setCurrentStep(0);
    setDisplayedText(language === 'nl' ? 'Druk op start om te beginnen' : 'Press start to begin');
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };
  const toggleVoiceGuidance = () => {
    setVoiceEnabled(prev => !prev);
  };
  const goToNextStep = () => {
    setCurrentStep(prevStep => (prevStep + 1) % visualizationSteps.length);
  };
  const goToPrevStep = () => {
    setCurrentStep(prevStep => (prevStep - 1 + visualizationSteps.length) % visualizationSteps.length);
  };
  // Sense prompts data
  const sensePromptsData = [
    {
      icon: 'eye-outline',
      text: language === 'nl' ? 'Wat zie je?' : 'What do you see?'
    },
    {
      icon: 'ear-outline',
      text: language === 'nl' ? 'Wat hoor je?' : 'What do you hear?'
    },
    {
      icon: 'flower-outline',
      text: language === 'nl' ? 'Wat ruik je?' : 'What do you smell?'
    },
    {
      icon: 'hand-left-outline',
      text: language === 'nl' ? 'Wat voel je?' : 'What do you feel?'
    }
  ];
  // Render sense prompt item
  const renderSensePromptItem = ({ item }: { item: typeof sensePromptsData[0] }) => (
    <View style={[styles.sensePrompt, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <Ionicons name={item.icon as keyof typeof Ionicons.glyphMap} size={24} color={colors.primary} />
      <Text style={[styles.sensePromptText, { color: colors.text }]}>
        {item.text}
      </Text>
    </View>
  );
  
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>
        {language === 'nl' ? 'Veilige Plek Visualisatie' : 'Safe Place Visualization'}
      </Text>
      <Text style={[styles.description, { color: colors.muted }]}>
        {language === 'nl'
          ? 'Creëer en verken een mentale veilige plek om stress en angst te verminderen.'
          : 'Create and explore a mental safe place to reduce stress and anxiety.'}
      </Text>
      <VoiceControl
        colors={colors}
        language={language}
        currentInstruction={getVisualizationInstructions()}
        isEnabled={voiceEnabled}
        onToggle={toggleVoiceGuidance}
      />
      {exerciseState !== 'idle' ? (
        <>
          <View style={[styles.visualizationCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <TouchableOpacity onPress={goToPrevStep} style={styles.stepNavButton}>
              <Ionicons name="chevron-back" size={24} color={colors.primary} />
            </TouchableOpacity>
            <View style={styles.currentStepContainer}>
              <Animated.Text style={[styles.currentStepText, { color: colors.text }, animatedStyle]}>
                {displayedText}
              </Animated.Text>
              <Text style={[styles.stepIndicator, { color: colors.muted }]}>
                {currentStep + 1}/{visualizationSteps.length}
              </Text>
            </View>
            <TouchableOpacity onPress={goToNextStep} style={styles.stepNavButton}>
              <Ionicons name="chevron-forward" size={24} color={colors.primary} />
            </TouchableOpacity>
          </View>
          <View style={styles.sensePrompts}>
            <FlatList
              data={sensePromptsData}
              renderItem={renderSensePromptItem}
              keyExtractor={(item, index) => `sense-${index}`}
              numColumns={2}
              scrollEnabled={false}
              columnWrapperStyle={styles.sensePromptsRow}
            />
          </View>
          <View style={styles.timerControls}>
            <TouchableOpacity
              style={[styles.timerButton, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={resetTimer}
            >
              <Ionicons name="refresh" size={24} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.timerButton, { backgroundColor: colors.primary }]}
              onPress={toggleTimer}
            >
              {exerciseState === 'running' ? (
                <Ionicons name="pause" size={24} color="#fff" />
              ) : (
                <Ionicons name="play" size={24} color="#fff" />
              )}
            </TouchableOpacity>
            <View style={[styles.timerDisplay, { backgroundColor: colors.card, borderColor: colors.border }]}>
              <Text style={[styles.timerText, { color: colors.text }]}>{formatTime(timerSeconds)}</Text>
            </View>
          </View>
        </>
      ) : (
        <View style={styles.placeholderContainer}>
          <Text style={[styles.placeholderText, { color: colors.muted }]}>
            {language === 'nl' ? 'Klaar om te beginnen?' : 'Ready to begin?'}
          </Text>
          <TouchableOpacity
            style={[styles.largeStartButton, { backgroundColor: colors.success }]}
            onPress={toggleTimer}
          >
            <Text style={styles.largeStartButtonText}>{language === 'nl' ? 'Start' : 'Start'}</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({


  container: {
    marginBottom: 30,
  },
  currentStepContainer: {
    alignItems: 'center',
    flex: 1,
  },
  currentStepText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
  },
  largeStartButton: {
    alignItems: 'center',
    borderRadius: 25,
    elevation: 3,
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  largeStartButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholderContainer: {
    alignItems: 'center',
    height: 250,
    justifyContent: 'center',
    marginBottom: 30,
  },
  placeholderText: {
    fontSize: 18,
    fontStyle: 'italic',
    marginBottom: 20,
  },
  sensePrompt: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    padding: 12,
    width: '48%',
  },
  sensePromptText: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
    textAlign: 'center',
  },
  sensePrompts: {
    marginBottom: 24,
  },
  sensePromptsRow: {
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  stepIndicator: {
    fontSize: 12,
  },
  stepNavButton: {
    padding: 8,
  },
  timerButton: {
    alignItems: 'center',
    borderRadius: 30,
    borderWidth: 1,
    height: 60,
    justifyContent: 'center',
    marginHorizontal: 12,
    width: 60,
  },
  timerControls: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  timerDisplay: {
    borderRadius: 20,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  timerText: {
    fontSize: 18,
    fontWeight: '600',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  visualizationCard: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 16,
    padding: 16,
  },


});