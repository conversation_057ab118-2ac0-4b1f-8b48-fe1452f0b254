import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Platform, FlatList, Animated, Easing } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { VoiceControl } from './VoiceControl';
import { MindfulnessExercise } from "@/types/mindfulness";
import { ColorScheme } from "@/hooks/ui/useColorScheme";
interface BodyScanExerciseProps {
  colors: ColorScheme;
  language: string;
  activeExercise: MindfulnessExercise;
  onDurationUpdate?: (seconds: number) => void;
}
export const BodyScanExercise: React.FC<BodyScanExerciseProps> = ({ colors, language, activeExercise, onDurationUpdate }) => {
  const [exerciseState, setExerciseState] = useState<'idle' | 'running' | 'paused'>('idle');
  const [timerSeconds, setTimerSeconds] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const textOpacity = useRef(new Animated.Value(1)).current;
  
  // Timer interval reference
  
  // Use steps from activeExercise prop
  const stepsToUse = useMemo(() => activeExercise.steps || [], [activeExercise.steps]);
  
  const timerRunning = exerciseState === 'running';
  // Timer interval reference
  const timerRef = useRef<number | null>(null);
  // Timer effect - updates local timerSeconds
  useEffect(() => {
    if (timerRunning) {
      timerRef.current = setInterval(() => {
        setTimerSeconds(prev => prev + 1);
      }, 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [timerRunning]);
  // Effect to report duration and change step when timerSeconds changes
  useEffect(() => {
    onDurationUpdate?.(timerSeconds);
    // Change body scan step every 30 seconds
    if (timerRunning && timerSeconds > 0 && timerSeconds % 30 === 0) {
      setCurrentStep(prevStep => (prevStep + 1) % stepsToUse.length);
    }
  }, [timerSeconds, onDurationUpdate, timerRunning, stepsToUse.length]);
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  const toggleTimer = () => {
    if (exerciseState === 'idle') {
      setExerciseState('running');
    } else if (exerciseState === 'running') {
      setExerciseState('paused');
    } else if (exerciseState === 'paused') {
      setExerciseState('running');
    }
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  const resetTimer = () => {
    setExerciseState('idle');
    setTimerSeconds(0);
    setCurrentStep(0);
    textOpacity.setValue(1); // Ensure text is visible
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };
  
  const goToNextStep = () => {
    setCurrentStep(prev => (prev + 1) % stepsToUse.length);
    
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  
  const goToPrevStep = () => {
    setCurrentStep(prev => (prev === 0 ? stepsToUse.length - 1 : prev - 1));
    
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  
  // Render step item
  const renderStepItem = ({ item, index }: { item: string; index: number }) => (
    <View 
      style={[
        styles.stepItem, 
        currentStep === index && styles.activeStepItem,
        { 
          backgroundColor: currentStep === index ? colors.primary + '20' : colors.card,
          borderColor: currentStep === index ? colors.primary : colors.border
        }
      ]}
    >
      <Text style={[
        styles.stepNumber, 
        { color: currentStep === index ? colors.primary : colors.muted }
      ]}>
        {index + 1}
      </Text>
      <Text style={[
        styles.stepText, 
        { color: currentStep === index ? colors.primary : colors.text }
      ]}>
        {item}
      </Text>
    </View>
  );
  
  const getBodyScanInstructions = useCallback(() => {
    if (exerciseState === 'idle') {
      return language === 'nl' ? 'Druk op start om te beginnen' : 'Press start to begin';
    }
    if (exerciseState === 'paused') {
      return language === 'nl' ? 'Gepauzeerd' : 'Paused';
    }
    return stepsToUse[currentStep];
  }, [exerciseState, currentStep, language, stepsToUse]);
  const [displayedText, setDisplayedText] = useState(() => getBodyScanInstructions());
  useEffect(() => {
    const newInstruction = getBodyScanInstructions();
    if (newInstruction !== displayedText) {
      // Stop any existing text animation
      textOpacity.stopAnimation();
      
      Animated.sequence([
        Animated.timing(textOpacity, {
          toValue: 0,
          duration: 200, // Short fade-out
          useNativeDriver: false,
          easing: Easing.linear,
        }),
      ]).start(() => {
        setDisplayedText(newInstruction);
        Animated.timing(textOpacity, {
          toValue: 1,
          duration: 200, // Short fade-in
          useNativeDriver: false,
          easing: Easing.linear,
        }).start();
      });
    } else if (newInstruction === displayedText) {
      // If the instruction text is the same, ensure opacity is 1 (e.g., after reset or if animation was interrupted)
      textOpacity.stopAnimation();
      Animated.timing(textOpacity, {
        toValue: 1,
        duration: 50, // A very short duration to ensure it's visible, or 0 for instant
        useNativeDriver: false,
        easing: Easing.linear,
      }).start();
    }
  }, [getBodyScanInstructions, displayedText, textOpacity]);
  const toggleVoiceGuidance = () => {
    setVoiceEnabled(prev => !prev);
  };
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>
        {language === 'nl' ? 'Lichaamsscan Meditatie' : 'Body Scan Meditation'}
      </Text>
      <Text style={[styles.description, { color: colors.muted }]}>
        {language === 'nl'
          ? 'Een progressieve scan van je hele lichaam, waarbij je bewustzijn naar elk deel brengt en spanning loslaat.'
          : 'A progressive scan of your entire body, bringing awareness to each part and releasing tension.'}
      </Text>
      <VoiceControl
        colors={colors}
        language={language}
        currentInstruction={getBodyScanInstructions()}
        isEnabled={voiceEnabled}
        onToggle={toggleVoiceGuidance}
      />
      {exerciseState !== 'idle' ? (
        <>
          <View style={[styles.bodyPartIndicator, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <TouchableOpacity onPress={goToPrevStep} style={styles.stepNavButton}>
              <Ionicons name="chevron-back" size={24} color={colors.primary} />
            </TouchableOpacity>
            <View style={styles.currentStepContainer}>
              <Animated.Text style={[styles.currentStepText, { color: colors.text, opacity: textOpacity }]}>
                {displayedText}
              </Animated.Text>
              <Text style={[styles.stepIndicator, { color: colors.muted }]}>
                {currentStep + 1}/{stepsToUse.length}
              </Text>
            </View>
            <TouchableOpacity onPress={goToNextStep} style={styles.stepNavButton}>
              <Ionicons name="chevron-forward" size={24} color={colors.primary} />
            </TouchableOpacity>
          </View>
          <View style={styles.stepsContainer}>
            <FlatList
              data={stepsToUse}
              renderItem={renderStepItem}
              keyExtractor={(item, _index) => `step-${_index}`}
              scrollEnabled={true}
              style={styles.scrollContainer}
            />
          </View>
        </>
      ) : (
        <View style={styles.placeholderContainer}>
          <Text style={[styles.placeholderText, { color: colors.muted }]}>
            {language === 'nl' ? 'Klaar om te beginnen?' : 'Ready to begin?'}
          </Text>
          <TouchableOpacity
            style={[styles.largeStartButton, { backgroundColor: colors.success }]}
            onPress={toggleTimer}
          >
            <Text style={[styles.largeStartButtonText, { color: colors.background }]}>{language === 'nl' ? 'Start' : 'Start'}</Text>
          </TouchableOpacity>
        </View>
      )}
      {exerciseState !== 'idle' && (
        <View style={styles.timerControls}>
          <TouchableOpacity
            style={[styles.timerButton, { backgroundColor: colors.card, borderColor: colors.border }]}
            onPress={resetTimer}
          >
            <Ionicons name="refresh" size={24} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.timerButton, { backgroundColor: colors.primary }]}
            onPress={toggleTimer}
          >
            {exerciseState === 'running' ? (
              <Ionicons name="pause" size={24} color="#fff" />
            ) : (
              <Ionicons name="play" size={24} color="#fff" />
            )}
          </TouchableOpacity>
          <View style={[styles.timerDisplay, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <Text style={[styles.timerText, { color: colors.text }]}>{formatTime(timerSeconds)}</Text>
          </View>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({

  activeStepItem: {
    borderWidth: 2,
  },
  bodyPartIndicator: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 16,
    padding: 16,
  },
  container: {
    marginBottom: 30,
  },
  currentStepContainer: {
    alignItems: 'center',
    flex: 1,
  },
  currentStepText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
  },
  largeStartButton: {
    alignItems: 'center',
    borderRadius: 25,
    elevation: 3,
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 12,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  largeStartButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholderContainer: {
    alignItems: 'center',
    height: 250,
    justifyContent: 'center',
    marginBottom: 30,
  },
  placeholderText: {
    fontSize: 18,
    fontStyle: 'italic',
    marginBottom: 20,
  },
  scrollContainer: {
    maxHeight: 200,
  },
  stepIndicator: {
    fontSize: 12,
  },
  stepItem: {
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 8,
    padding: 12,
  },
  stepNavButton: {
    padding: 8,
  },
  stepNumber: {
    fontSize: 16,
    fontWeight: '700',
    marginRight: 12,
    textAlign: 'center',
    width: 24,
  },
  stepText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  stepsContainer: {
    marginBottom: 24,
  },
  timerButton: {
    alignItems: 'center',
    borderRadius: 30,
    borderWidth: 1,
    height: 60,
    justifyContent: 'center',
    marginHorizontal: 12,
    width: 60,
  },
  timerControls: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  timerDisplay: {
    borderRadius: 20,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  timerText: {
    fontSize: 18,
    fontWeight: '600',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },

});