import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  Platform,
  Alert,
  Animated,
  Easing
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { VoiceControl } from './VoiceControl';
import { useSpeech } from '@/hooks/media/use-speech';
import { MindfulnessExercise } from "@/types/mindfulness";
interface ColorScheme {
  primary: string;
  primaryDark: string;
  accent: string;
  text: string;
  muted: string;
  card: string;
  border: string;
  success: string;
  danger: string;
}

interface GratitudeExerciseProps {
  colors: ColorScheme;
  language: string;
  _activeExercise: MindfulnessExercise;
  _onDurationUpdate?: (seconds: number) => void;
}
export const GratitudeExercise: React.FC<GratitudeExerciseProps> = ({ colors, language, _activeExercise, _onDurationUpdate }) => {
  const [gratitudeItems, setGratitudeItems] = useState<string[]>(['', '', '']);
  const [reflectionText, setReflectionText] = useState('');
  const [exerciseState, setExerciseState] = useState<'idle' | 'running' | 'paused'>('idle');
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const [currentPromptIndex, setCurrentPromptIndex] = useState(0);
  const textOpacity = useRef(new Animated.Value(1)).current;
  
  const { speakText } = useSpeech({
    language: language === 'nl' ? 'nl' : 'en-US',
    rate: 0.9
  });
  const updateGratitudeItem = (index: number, text: string) => {
    const newItems = [...gratitudeItems];
    newItems[index] = text;
    setGratitudeItems(newItems);
  };
  const checkCompletion = () => {
    // Check if all gratitude items have been filled
    const allItemsFilled = gratitudeItems.every(item => item.trim().length > 0);
    const reflectionFilled = reflectionText.trim().length > 0;
    return allItemsFilled && reflectionFilled;
  };
  const handleComplete = () => {
    if (!checkCompletion()) {
      if (Platform.OS === 'web') {
        alert(language === 'nl'
          ? 'Vul alstublieft alle dankbaarheidspunten en reflectie in om de oefening te voltooien.'
          : 'Please fill in all gratitude items and reflection to complete the exercise.');
      } else {
        Alert.alert(
          language === 'nl' ? 'Niet Voltooid' : 'Not Completed',
          language === 'nl'
            ? 'Vul alstublieft alle dankbaarheidspunten en reflectie in om de oefening te voltooien.'
            : 'Please fill in all gratitude items and reflection to complete the exercise.',
          [{ text: 'OK' }]
        );
      }
      return;
    }
    setExerciseState('paused'); // Mark as paused/completed
    // No need for isCompleted state if we use exerciseState for UI logic
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
    if (Platform.OS === 'web') {
      alert(language === 'nl'
        ? 'Geweldig! Je hebt de dankbaarheidsoefening voltooid.'
        : 'Great job! You have completed the gratitude exercise.');
    } else {
      Alert.alert(
        language === 'nl' ? 'Oefening Voltooid' : 'Exercise Completed',
        language === 'nl'
          ? 'Geweldig! Je hebt de dankbaarheidsoefening voltooid.'
          : 'Great job! You have completed the gratitude exercise.',
        [{ text: 'OK' }]
      );
    }
  };
  const resetExercise = () => {
    setGratitudeItems(['', '', '']);
    setReflectionText('');
    setExerciseState('idle'); // Reset to idle
    setCurrentPromptIndex(0);
    textOpacity.setValue(1); // Ensure text is visible
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  };
  
  const gratitudePrompts = useMemo(() => language === 'nl' 
    ? [
        'Iemand waar je dankbaar voor bent...',
        'Een ervaring waar je dankbaar voor bent...',
        'Iets kleins waar je dankbaar voor bent...'
      ]
    : [
        'Someone you are grateful for...',
        'An experience you are grateful for...',
        'Something small you are grateful for...'
      ], [language]);
  
  const reflectionPrompt = useMemo(() => language === 'nl'
    ? 'Hoe voelt het om stil te staan bij deze dingen?'
    : 'How does it feel to reflect on these things?', [language]);
  
  const getGratitudeInstructions = useCallback(() => {
    if (exerciseState === 'idle') {
      return language === 'nl' ? 'Druk op start om te beginnen' : 'Press start to begin';
    }
    if (exerciseState === 'paused') {
      return language === 'nl' ? 'Gepauzeerd' : 'Paused';
    }
    if (currentPromptIndex < 3) {
      return `${currentPromptIndex + 1}. ${gratitudePrompts[currentPromptIndex]}`;
    } else {
      return reflectionPrompt;
    }
  }, [exerciseState, currentPromptIndex, language, gratitudePrompts, reflectionPrompt]);
  const [displayedText, setDisplayedText] = useState(() => getGratitudeInstructions());
  useEffect(() => {
    const newInstruction = getGratitudeInstructions();
    if (newInstruction !== displayedText) {
      // Stop any existing text animation
      textOpacity.stopAnimation();
      
      Animated.sequence([
        Animated.timing(textOpacity, {
          toValue: 0,
          duration: 200, // Short fade-out
          useNativeDriver: false,
          easing: Easing.linear,
        }),
      ]).start(() => {
        setDisplayedText(newInstruction);
        Animated.timing(textOpacity, {
          toValue: 1,
          duration: 200, // Short fade-in
          useNativeDriver: false,
          easing: Easing.linear,
        }).start();
      });
    } else if (newInstruction === displayedText) {
      // If the instruction text is the same, ensure opacity is 1 (e.g., after reset or if animation was interrupted)
      textOpacity.stopAnimation();
      Animated.timing(textOpacity, {
        toValue: 1,
        duration: 50, // A very short duration to ensure it's visible, or 0 for instant
        useNativeDriver: false,
        easing: Easing.linear,
      }).start();
    }
  }, [getGratitudeInstructions, displayedText, textOpacity]);
  const toggleVoiceGuidance = () => {
    setVoiceEnabled(prev => !prev);
    // If turning on voice, speak the current prompt
    if (!voiceEnabled && exerciseState !== 'idle') { // Only speak if not idle
      if (currentPromptIndex < 3) {
        speakText(`${currentPromptIndex + 1}. ${gratitudePrompts[currentPromptIndex]}`);
      } else {
        speakText(reflectionPrompt);
      }
    }
  };
  const handleFocus = (index: number) => {
    setCurrentPromptIndex(index);
    if (voiceEnabled && exerciseState !== 'idle') { // Only speak if not idle
      if (index < 3) {
        speakText(`${index + 1}. ${gratitudePrompts[index]}`);
      } else {
        speakText(reflectionPrompt);
      }
    }
  };
  // Render item for tips
  const renderTipItem = ({ item }: { item: string }) => (
    <View style={styles.tipItem}>
      <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
      <Text style={[styles.tipText, { color: colors.text }]}>{item}</Text>
    </View>
  );
  // Tips data
  const tipsData = language === 'nl' 
    ? [
        'Begin of eindig je dag met het opschrijven van 3 dingen waar je dankbaar voor bent',
        'Wees specifiek over waarom je dankbaar bent voor iets',
        'Probeer nieuwe dingen te vinden om dankbaar voor te zijn',
        'Deel je dankbaarheid met anderen',
        'Maak er een gewoonte van om dankbaarheid te uiten'
      ]
    : [
        'Start or end your day by writing down 3 things you are grateful for',
        'Be specific about why you are grateful for something',
        'Try to find new things to be grateful for',
        'Share your gratitude with others',
        'Make expressing gratitude a habit'
      ];
  
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>
        {language === 'nl' ? 'Dankbaarheidsoefening' : 'Gratitude Practice'}
      </Text>
      <Text style={[styles.description, { color: colors.muted }]}>
        {language === 'nl'
          ? 'Focus op dingen waar je dankbaar voor bent om je perspectief te verschuiven en je stemming te verbeteren.'
          : "Focus on things you are grateful for to shift perspective and improve mood."}
      </Text>
      <VoiceControl
        colors={colors}
        language={language}
        currentInstruction={getGratitudeInstructions()}
        isEnabled={voiceEnabled}
        onToggle={toggleVoiceGuidance}
      />
      {exerciseState !== 'idle' ? (
        <>
          <View style={[styles.promptCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <Text style={[styles.promptTitle, { color: colors.text }]}>
              {language === 'nl' ? 'Waarom Dankbaarheid?' : 'Why Gratitude?'}
            </Text>
            <Animated.Text style={[styles.promptText, { color: colors.muted, opacity: textOpacity }]}>
              {displayedText}
            </Animated.Text>
          </View>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {language === 'nl' ? 'Ik ben dankbaar voor...' : 'I am grateful for...'}
          </Text>
          {gratitudeItems.map((item, index) => (
            <View key={index} style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                {index + 1}. {gratitudePrompts[index]}
              </Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: colors.card,
                    borderColor: colors.border,
                    color: colors.text
                  }
                ]}
                value={item}
                onChangeText={(text) => updateGratitudeItem(index, text)}
                placeholder={language === 'nl' ? 'Typ hier...' : 'Type here...'}
                placeholderTextColor={colors.muted}
                multiline
                editable={exerciseState !== 'paused'}
                onFocus={() => handleFocus(index)}
              />
            </View>
          ))}
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {language === 'nl' ? 'Reflectie' : 'Reflection'}
          </Text>
          <Text style={[styles.inputLabel, { color: colors.text }]}>
            {reflectionPrompt}
          </Text>
          <TextInput
            style={[
              styles.textArea,
              {
                backgroundColor: colors.card,
                borderColor: colors.border,
                color: colors.text
              }
            ]}
            value={reflectionText}
            onChangeText={setReflectionText}
            placeholder={language === 'nl' ? 'Deel je gedachten hier...' : 'Share your thoughts here...'}
            placeholderTextColor={colors.muted}
            multiline
            numberOfLines={6}
            textAlignVertical="top"
            editable={exerciseState !== 'paused'}
            onFocus={() => handleFocus(3)}
          />
          <View style={styles.tipsContainer}>
            <Text style={[styles.tipsTitle, { color: colors.text }]}>
              {language === 'nl' ? 'Tips voor Dagelijkse Dankbaarheid' : 'Tips for Daily Gratitude'}
            </Text>
            <FlatList
              data={tipsData}
              renderItem={renderTipItem}
              keyExtractor={(item, index) => `tip-${index}`}
              scrollEnabled={false}
              style={styles.tipsList}
            />
          </View>
          <View style={styles.buttonContainer}>
            {exerciseState === 'paused' ? (
              <TouchableOpacity
                style={[styles.resetButton, { backgroundColor: colors.card, borderColor: colors.border }]}
                onPress={resetExercise}
              >
                <Ionicons name="refresh" size={20} color={colors.text} />
                <Text style={[styles.resetButtonText, { color: colors.text }]}>
                  {language === 'nl' ? 'Opnieuw Beginnen' : 'Start Again'}
                </Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={[styles.completeButton, { backgroundColor: colors.primary }]}
                onPress={handleComplete}
              >
                <Text style={styles.completeButtonText}>
                  {language === 'nl' ? 'Voltooien' : 'Complete'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </>
      ) : (
        <View style={styles.placeholderContainer}>
          <Text style={[styles.placeholderText, { color: colors.muted }]}>
            {language === 'nl' ? 'Klaar om te beginnen?' : 'Ready to begin?'}
          </Text>
          <TouchableOpacity
            style={[styles.largeStartButton, { backgroundColor: colors.success }]}
            onPress={() => setExerciseState('running')}
          >
            <Text style={styles.largeStartButtonText}>{language === 'nl' ? 'Start' : 'Start'}</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({


  buttonContainer: {
    marginBottom: 40,
  },
  completeButton: {
    alignItems: 'center',
    borderRadius: 8,
    paddingVertical: 14,
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  container: {
    flex: 1,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 24,
  },
  input: {
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
    minHeight: 50,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  largeStartButton: {
    alignItems: 'center',
    borderRadius: 25,
    elevation: 3,
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  largeStartButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholderContainer: {
    alignItems: 'center',
    height: 250,
    justifyContent: 'center',
    marginBottom: 30,
  },
  placeholderText: {
    fontSize: 18,
    fontStyle: 'italic',
    marginBottom: 20,
  },
  promptCard: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 24,
    padding: 16,
  },
  promptText: {
    fontSize: 14,
    lineHeight: 20,
  },
  promptTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  resetButton: {
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 14,
  },
  resetButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  textArea: {
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
    marginBottom: 24,
    minHeight: 120,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  tipItem: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    marginBottom: 8,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 8,
  },
  tipsContainer: {
    marginBottom: 24,
  },
  tipsList: {
    marginLeft: 8,
  },
  tipsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },


});