import React from "react";
import { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  TextInput, 
  TouchableOpacity, 
  ScrollView, 
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useUserStore } from '@/store/user/user-store';
import { MindfulnessExercise } from '@/types/mindfulness';
import { ColorScheme } from '@/hooks/ui/useColorScheme';
interface ExerciseFormProps {
  colors: ColorScheme;
  language: string;
  onClose: () => void;
  editExerciseId?: string;
}
export const ExerciseForm: React.FC<ExerciseFormProps> = ({ 
  colors, 
  language, 
  onClose,
  editExerciseId
}) => {
  const { profile, addCustomExercise, editCustomExercise } = useUserStore();
  
  // Find exercise if in edit mode
  const exerciseToEdit = editExerciseId 
    ? profile?.customExercises?.find(ex => ex.id === editExerciseId) 
    : undefined;
  
  const [title, setTitle] = useState(exerciseToEdit?.title || '');
  const [description, setDescription] = useState(exerciseToEdit?.description || '');
  const [type, setType] = useState<MindfulnessExercise['type']>(exerciseToEdit?.type || 'custom');
  const [duration, setDuration] = useState(exerciseToEdit?.duration?.toString() || '5');
  const [steps, setSteps] = useState<string[]>(exerciseToEdit?.steps || ['']);
  const [customInstructions, setCustomInstructions] = useState(exerciseToEdit?.customInstructions || '');
  
  const [errors, setErrors] = useState<{
    title?: string;
    description?: string;
    duration?: string;
  }>({});
  
  const exerciseTypes = [
    { value: 'breathing', label: language === 'nl' ? 'Ademhaling' : 'Breathing' },
    { value: 'meditation', label: language === 'nl' ? 'Meditatie' : 'Meditation' },
    { value: 'grounding', label: language === 'nl' ? 'Grounding' : 'Grounding' },
    { value: 'bodyscan', label: language === 'nl' ? 'Lichaamsscan' : 'Body Scan' },
    { value: 'visualization', label: language === 'nl' ? 'Visualisatie' : 'Visualization' },
    { value: 'gratitude', label: language === 'nl' ? 'Dankbaarheid' : 'Gratitude' },
    { value: 'muscle-relaxation', label: language === 'nl' ? 'Spierontspanning' : 'Muscle Relaxation' },
    { value: 'custom', label: language === 'nl' ? 'Aangepast' : 'Custom' },
  ];
  
  const addStep = () => {
    setSteps([...steps, '']);
    
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  
  const updateStep = (index: number, text: string) => {
    const newSteps = [...steps];
    newSteps[index] = text;
    setSteps(newSteps);
  };
  
  const removeStep = (index: number) => {
    if (steps.length > 1) {
      const newSteps = [...steps];
      newSteps.splice(index, 1);
      setSteps(newSteps);
      
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    }
  };
  
  const validateForm = () => {
    const newErrors: {
      title?: string;
      description?: string;
      duration?: string;
    } = {};
    
    if (!title.trim()) {
      newErrors.title = language === 'nl' ? 'Titel is verplicht' : 'Title is required';
    }
    
    if (!description.trim()) {
      newErrors.description = language === 'nl' ? 'Beschrijving is verplicht' : 'Description is required';
    }
    
    const durationNum = parseInt(duration);
    if (isNaN(durationNum) || durationNum <= 0 || durationNum > 60) {
      newErrors.duration = language === 'nl' 
        ? 'Duur moet tussen 1 en 60 minuten zijn' 
        : 'Duration must be between 1 and 60 minutes';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = () => {
    if (!validateForm()) return;
    
    const exerciseData = {
      title,
      description,
      type,
      duration: parseInt(duration),
      steps: steps.filter(step => step.trim().length > 0),
      customInstructions: customInstructions.trim() || undefined,
    };
    
    if (editExerciseId && exerciseToEdit) {
      editCustomExercise(editExerciseId, exerciseData);
    } else {
      addCustomExercise(exerciseData);
    }
    
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
    
    onClose();
  };
  
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          {editExerciseId 
            ? (language === 'nl' ? 'Oefening Bewerken' : 'Edit Exercise')
            : (language === 'nl' ? 'Nieuwe Oefening' : 'New Exercise')}
        </Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Ionicons name="close" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.formContainer}>
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: colors.text }]}>
            {language === 'nl' ? 'Titel' : 'Title'}
          </Text>
          <TextInput
            style={[
              styles.input, 
              { 
                backgroundColor: colors.card, 
                borderColor: errors.title ? colors.danger : colors.border,
                color: colors.text
              }
            ]}
            value={title}
            onChangeText={setTitle}
            placeholder={language === 'nl' ? 'Titel van de oefening' : 'Exercise title'}
            placeholderTextColor={colors.muted}
          />
          {errors.title && (
            <Text style={[styles.errorText, { color: colors.danger }]}>{errors.title}</Text>
          )}
        </View>
        
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: colors.text }]}>
            {language === 'nl' ? 'Beschrijving' : 'Description'}
          </Text>
          <TextInput
            style={[
              styles.textArea, 
              { 
                backgroundColor: colors.card, 
                borderColor: errors.description ? colors.danger : colors.border,
                color: colors.text
              }
            ]}
            value={description}
            onChangeText={setDescription}
            placeholder={language === 'nl' ? 'Beschrijf de oefening' : 'Describe the exercise'}
            placeholderTextColor={colors.muted}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
          {errors.description && (
            <Text style={[styles.errorText, { color: colors.danger }]}>{errors.description}</Text>
          )}
        </View>
        
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: colors.text }]}>
            {language === 'nl' ? 'Type' : 'Type'}
          </Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.typeSelector}
          >
            {exerciseTypes.map((exerciseType) => (
              <TouchableOpacity
                key={exerciseType.value}
                style={[
                  styles.typeButton,
                  type === exerciseType.value && styles.typeButtonActive,
                  { 
                    backgroundColor: type === exerciseType.value ? colors.primary : colors.card,
                    borderColor: colors.border
                  }
                ]}
                onPress={() => setType(exerciseType.value as MindfulnessExercise['type'])}
              >
                <Text 
                  style={[
                    styles.typeButtonText, 
                    type === exerciseType.value ? styles.typeButtonTextSelected : { color: colors.text }
                  ]}
                >
                  {exerciseType.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
        
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: colors.text }]}>
            {language === 'nl' ? 'Duur (minuten)' : 'Duration (minutes)'}
          </Text>
          <TextInput
            style={[
              styles.input, 
              { 
                backgroundColor: colors.card, 
                borderColor: errors.duration ? colors.danger : colors.border,
                color: colors.text
              }
            ]}
            value={duration}
            onChangeText={setDuration}
            keyboardType="number-pad"
            placeholder="5"
            placeholderTextColor={colors.muted}
          />
          {errors.duration && (
            <Text style={[styles.errorText, { color: colors.danger }]}>{errors.duration}</Text>
          )}
        </View>
        
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: colors.text }]}>
            {language === 'nl' ? 'Stappen' : 'Steps'}
          </Text>
          {steps.map((step, index) => (
            <View key={index} style={styles.stepContainer}>
              <TextInput
                style={[
                  styles.stepInput, 
                  { 
                    backgroundColor: colors.card, 
                    borderColor: colors.border,
                    color: colors.text
                  }
                ]}
                value={step}
                onChangeText={(text) => updateStep(index, text)}
                placeholder={language === 'nl' ? `Stap ${index + 1}` : `Step ${index + 1}`}
                placeholderTextColor={colors.muted}
                multiline
              />
              <TouchableOpacity 
                style={[styles.removeStepButton, { backgroundColor: colors.danger }]}
                onPress={() => removeStep(index)}
              >
                <Ionicons name="remove" size={20} color="#fff" />
              </TouchableOpacity>
            </View>
          ))}
          <TouchableOpacity 
            style={[styles.addStepButton, { backgroundColor: colors.primary }]}
            onPress={addStep}
          >
            <Ionicons name="add" size={20} color="#fff" />
            <Text style={styles.addStepButtonText}>
              {language === 'nl' ? 'Stap Toevoegen' : 'Add Step'}
            </Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: colors.text }]}>
            {language === 'nl' ? 'Aangepaste Instructies (Optioneel)' : 'Custom Instructions (Optional)'}
          </Text>
          <TextInput
            style={[
              styles.textArea, 
              { 
                backgroundColor: colors.card, 
                borderColor: colors.border,
                color: colors.text
              }
            ]}
            value={customInstructions}
            onChangeText={setCustomInstructions}
            placeholder={language === 'nl' 
              ? 'Voeg extra instructies toe indien nodig' 
              : 'Add any additional instructions if needed'}
            placeholderTextColor={colors.muted}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={[styles.cancelButton, { backgroundColor: colors.card, borderColor: colors.border }]}
            onPress={onClose}
          >
            <Text style={[styles.buttonText, { color: colors.text }]}>
              {language === 'nl' ? 'Annuleren' : 'Cancel'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.saveButton, { backgroundColor: colors.primary }]}
            onPress={handleSubmit}
          >
            <Text style={[styles.buttonText, styles.saveButtonText]}>
              {language === 'nl' ? 'Opslaan' : 'Save'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};
const styles = StyleSheet.create({


  addStepButton: {
    alignItems: 'center',
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  addStepButtonText: {
    color: '#fff',
    fontWeight: '500',
    marginLeft: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 40,
    marginTop: 20,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    flex: 1,
    marginRight: 8,
    paddingVertical: 12,
  },
  closeButton: {
    position: 'absolute',
    right: 16,
  },
  container: {
    flex: 1,
  },
  errorText: {
    fontSize: 14,
    marginTop: 4,
  },
  formContainer: {
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  header: {
    alignItems: 'center',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 16,
    position: 'relative',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  input: {
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  removeStepButton: {
    alignItems: 'center',
    borderRadius: 18,
    height: 36,
    justifyContent: 'center',
    marginLeft: 8,
    width: 36,
  },
  saveButton: {
    alignItems: 'center',
    borderRadius: 8,
    flex: 1,
    marginLeft: 8,
    paddingVertical: 12,
  },
  saveButtonText: {
    color: '#fff',
  },
  stepContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 8,
  },
  stepInput: {
    borderRadius: 8,
    borderWidth: 1,
    flex: 1,
    fontSize: 16,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  textArea: {
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
    minHeight: 100,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  typeButton: {
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  typeButtonActive: {
    borderWidth: 0,
  },
  typeButtonText: {
    fontWeight: '500',
  },
  typeButtonTextSelected: {
    color: '#fff',
    fontWeight: '500',
  },
  typeSelector: {
    flexDirection: 'row',
    marginBottom: 8,
  },


});