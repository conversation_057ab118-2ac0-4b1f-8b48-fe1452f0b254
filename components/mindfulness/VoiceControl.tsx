import React from 'react';
import { StyleSheet, View, TouchableOpacity, Text, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useSpeech } from '@/hooks/media/use-speech';

interface ColorScheme {
  primary: string;
  text: string;
  card: string;
  border: string;
}

interface VoiceControlProps {
  colors: ColorScheme;
  language: string;
  currentInstruction: string;
  isEnabled: boolean;
  onToggle: () => void;
}
export const VoiceControl: React.FC<VoiceControlProps> = ({
  colors,
  language,
  currentInstruction,
  isEnabled,
  onToggle,
}) => {
  const { 
    speakText, 
    stopSpeech, 
    isSpeaking, 
    isAvailable 
  } = useSpeech({ 
    language: language === 'nl' ? 'nl' : 'en-US',
    rate: 0.9
  });
  // Ref to track if the initial "Voice enabled" confirmation has been handled for the current enabling cycle.
  const initialEnableHandledRef = React.useRef(false);
  // Ref to track the last instruction spoken by the instruction-speaking useEffect.
  const lastSpokenInstructionRef = React.useRef<string | null>(null);
  // Effect for speaking instructions when they change or when voice is enabled (after initial confirmation)
  React.useEffect(() => {
    let timeoutId: number | undefined;
    if (isEnabled) {
      if (!initialEnableHandledRef.current) {
        // isEnabled just became true. Mark that the initial phase (handled by handleToggle) is done.
        initialEnableHandledRef.current = true;
        lastSpokenInstructionRef.current = null; // Reset for this new enabling cycle
      } else {
        // isEnabled was already true, and initial confirmation was handled.
        // Speak if currentInstruction is valid and different from what was last spoken.
        if (currentInstruction && currentInstruction !== lastSpokenInstructionRef.current && isAvailable) {
          // Defer speakText to avoid potential synchronous update issues with useInsertionEffect
          timeoutId = setTimeout(() => {
            speakText(currentInstruction);
          }, 0);
          lastSpokenInstructionRef.current = currentInstruction;
        }
      }
    } else {
      // isEnabled is false. Reset flags for the next time it's enabled.
      initialEnableHandledRef.current = false;
      lastSpokenInstructionRef.current = null;
      // Stopping speech if isEnabled becomes false is handled by the next useEffect.
    }
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  // Assuming speakText from useSpeech is stable (e.g., wrapped in useCallback).
  // If not, it should be in the dependency array, but that requires careful handling in useSpeech.
  }, [currentInstruction, isEnabled, isAvailable, speakText]);
  // Effect for stopping speech when isEnabled becomes false or component unmounts
  React.useEffect(() => {
    if (!isEnabled && isSpeaking) {
      stopSpeech();
    }
    // Cleanup on unmount: if speaking, stop speech.
    return () => {
      // This check uses the 'isSpeaking' value from the closure of when the effect was set up.
      // If useSpeech cleans up itself (e.g., stops speech on unmount of its host), this might be redundant.
      if (isSpeaking) {
        stopSpeech();
      }
    };
  // Assuming stopSpeech and isSpeaking from useSpeech are stable or correctly handled.
  }, [isEnabled, isSpeaking, stopSpeech]);
  
  const handleToggle = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    const newIsEnabledState = !isEnabled; // The state we are transitioning to
    if (newIsEnabledState) { // Turning ON
      if (isAvailable) {
        // Stop any ongoing speech before speaking the confirmation.
        if (isSpeaking) {
          stopSpeech();
        }
        const confirmationMessage = language === 'nl' ? 'Spraak ingeschakeld' : 'Voice enabled';
        speakText(confirmationMessage);
      }
    } else { // Turning OFF
      if (isSpeaking) {
        stopSpeech();
      }
    }
    
    // This will trigger the useEffects above due to 'isEnabled' prop changing.
    onToggle();
  };
  
  // Don't render if speech is not available
  if (isAvailable === false) {
    return null;
  }
  
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.button,
          {
            backgroundColor: isEnabled ? colors.primary : colors.card,
            borderColor: colors.border,
          },
        ]}
        onPress={handleToggle}
        disabled={isAvailable === null} // Disable while checking availability
      >
        <Ionicons
          name={isEnabled ? "volume-high" : "volume-mute"}
          size={20}
          color={isEnabled ? "#fff" : colors.text}
        />
        <Text
          style={[
            styles.buttonText,
            isEnabled ? styles.buttonTextEnabled : { color: colors.text },
          ]}
        >
          {language === 'nl' ? 'Spraakbegeleiding' : 'Voice Guidance'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};
const styles = StyleSheet.create({


  button: {
    alignItems: 'center',
    alignSelf: 'center',
    borderRadius: 20,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  buttonTextEnabled: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  container: {
    marginBottom: 16,
  },


});