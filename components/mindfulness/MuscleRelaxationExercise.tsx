import React from "react";
import { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Platform, Animated, Easing } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { VoiceControl } from './VoiceControl';
import { MindfulnessExercise } from "@/types/mindfulness";
interface ColorScheme {
  primary: string;
  text: string;
  muted: string;
  card: string;
  border: string;
  success: string;
  danger: string;
}

interface MuscleRelaxationExerciseProps {
  colors: ColorScheme;
  language: string;
  _activeExercise: MindfulnessExercise;
  onDurationUpdate?: (seconds: number) => void;
}
export const MuscleRelaxationExercise: React.FC<MuscleRelaxationExerciseProps> = ({ colors, language, _activeExercise, onDurationUpdate }) => {
  const [exerciseState, setExerciseState] = useState<'idle' | 'running' | 'paused'>('idle');
  const [timerSeconds, setTimerSeconds] = useState(0);
  const [currentMuscleGroup, setCurrentMuscleGroup] = useState(0);
  const [tensionPhase, setTensionPhase] = useState<'tense' | 'relax'>('tense');
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const textOpacity = useRef(new Animated.Value(1)).current;
  
  // Timer interval reference
  const timerRef = useRef<number | null>(null);
  
  // Muscle groups
  const muscleGroups = useMemo(() => language === 'nl' 
    ? [
        { name: 'Voeten', instruction: 'Krul je tenen en span je voeten aan.' },
        { name: 'Kuiten', instruction: 'Span je kuitspieren aan door je voeten naar je schenen te trekken.' },
        { name: 'Bovenbenen', instruction: 'Span je dijspieren aan door je benen recht te maken.' },
        { name: 'Billen', instruction: 'Knijp je bilspieren samen.' },
        { name: 'Buik', instruction: 'Span je buikspieren aan alsof je een klap opvangt.' },
        { name: 'Borst', instruction: 'Span je borstspieren aan door je schouders naar voren te brengen.' },
        { name: 'Handen', instruction: 'Maak vuisten en span je handen aan.' },
        { name: 'Armen', instruction: 'Span je biceps aan door je onderarmen naar je schouders te brengen.' },
        { name: 'Schouders', instruction: 'Breng je schouders omhoog naar je oren.' },
        { name: 'Nek', instruction: 'Druk je hoofd licht naar achteren en span je nekspieren aan.' },
        { name: 'Gezicht', instruction: 'Knijp je ogen dicht en span alle gezichtsspieren aan.' }
      ]
    : [
        { name: 'Feet', instruction: 'Curl your toes and tense your feet.' },
        { name: 'Calves', instruction: 'Tense your calf muscles by pulling your feet towards your shins.' },
        { name: 'Thighs', instruction: 'Tense your thigh muscles by straightening your legs.' },
        { name: 'Buttocks', instruction: 'Squeeze your buttock muscles together.' },
        { name: 'Abdomen', instruction: 'Tense your abdominal muscles as if preparing for a punch.' },
        { name: 'Chest', instruction: 'Tense your chest muscles by bringing your shoulders forward.' },
        { name: 'Hands', instruction: 'Make fists and tense your hands.' },
        { name: 'Arms', instruction: 'Tense your biceps by bringing your forearms up to your shoulders.' },
        { name: 'Shoulders', instruction: 'Raise your shoulders up toward your ears.' },
        { name: 'Neck', instruction: 'Push your head back slightly and tense your neck muscles.' },
        { name: 'Face', instruction: 'Squeeze your eyes shut and tense all facial muscles.' }
      ], [language]);
  
  const timerRunning = exerciseState === 'running';
  // Timer effect - updates local timerSeconds
  useEffect(() => {
    if (timerRunning) {
      timerRef.current = setInterval(() => {
        setTimerSeconds(prev => prev + 1);
      }, 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [timerRunning]);
  // Effect to report duration and manage tension phase/muscle group when timerSeconds changes
  useEffect(() => {
    onDurationUpdate?.(timerSeconds);
    // Alternate between tension and relaxation
    if (timerRunning && timerSeconds > 0 && timerSeconds % 5 === 0) {
      if (tensionPhase === 'tense') {
        setTensionPhase('relax');
      } else {
        setTensionPhase('tense');
        // Move to next muscle group after relaxation
        setCurrentMuscleGroup(prevMuscleGroup => (prevMuscleGroup + 1) % muscleGroups.length);
      }
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    }
  }, [timerSeconds, onDurationUpdate, timerRunning, tensionPhase, muscleGroups.length]);
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  const toggleTimer = () => {
    if (exerciseState === 'idle') {
      setExerciseState('running');
    } else if (exerciseState === 'running') {
      setExerciseState('paused');
    } else if (exerciseState === 'paused') {
      setExerciseState('running');
    }
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  const resetTimer = () => {
    setExerciseState('idle');
    setTimerSeconds(0);
    setCurrentMuscleGroup(0);
    setTensionPhase('tense');
    textOpacity.setValue(1); // Ensure text is visible
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };
  
  const goToNextMuscleGroup = () => {
    setCurrentMuscleGroup(prev => (prev + 1) % muscleGroups.length);
    setTensionPhase('tense');
    
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  
  const goToPrevMuscleGroup = () => {
    setCurrentMuscleGroup(prev => (prev === 0 ? muscleGroups.length - 1 : prev - 1));
    setTensionPhase('tense');
    
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  
  const getMuscleRelaxationInstructions = useCallback(() => {
    if (exerciseState === 'idle') {
      return language === 'nl' ? 'Druk op start om te beginnen' : 'Press start to begin';
    }
    if (exerciseState === 'paused') {
      return language === 'nl' ? 'Gepauzeerd' : 'Paused';
    }
    const muscleGroup = muscleGroups[currentMuscleGroup];
    if (tensionPhase === 'tense') {
      return `${muscleGroup.name}: ${muscleGroup.instruction}`;
    } else {
      return language === 'nl'
        ? `${muscleGroup.name}: Laat los en voel het verschil. Merk op hoe je spieren ontspannen.`
        : `${muscleGroup.name}: Release and feel the difference. Notice how your muscles relax.`;
    }
  }, [exerciseState, currentMuscleGroup, tensionPhase, language, muscleGroups]);
  const [displayedText, setDisplayedText] = useState(() => getMuscleRelaxationInstructions());
  useEffect(() => {
    const newInstruction = getMuscleRelaxationInstructions();
    if (newInstruction !== displayedText) {
      // Stop any existing text animation
      textOpacity.stopAnimation();
      
      Animated.sequence([
        Animated.timing(textOpacity, {
          toValue: 0,
          duration: 200, // Short fade-out
          useNativeDriver: false,
          easing: Easing.linear,
        }),
      ]).start(() => {
        setDisplayedText(newInstruction);
        Animated.timing(textOpacity, {
          toValue: 1,
          duration: 200, // Short fade-in
          useNativeDriver: false,
          easing: Easing.linear,
        }).start();
      });
    } else if (newInstruction === displayedText) {
      // If the instruction text is the same, ensure opacity is 1 (e.g., after reset or if animation was interrupted)
      textOpacity.stopAnimation();
      Animated.timing(textOpacity, {
        toValue: 1,
        duration: 50, // A very short duration to ensure it's visible, or 0 for instant
        useNativeDriver: false,
        easing: Easing.linear,
      }).start();
    }
  }, [getMuscleRelaxationInstructions, displayedText, textOpacity]);
  const toggleVoiceGuidance = () => {
    setVoiceEnabled(prev => !prev);
  };
  // Tips data
  const tipsData = [
    language === 'nl' ? 'Span elke spiergroep 5 seconden aan.' : 'Tense each muscle group for 5 seconds.',
    language === 'nl' ? 'Ontspan daarna 10-15 seconden.' : 'Then relax for 10-15 seconds.',
    language === 'nl' ? 'Let op het verschil tussen spanning en ontspanning.' : 'Notice the difference between tension and relaxation.'
  ];
  
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>
        {language === 'nl' ? 'Progressieve Spierontspanning' : 'Progressive Muscle Relaxation'}
      </Text>
      <Text style={[styles.description, { color: colors.muted }]}>
        {language === 'nl'
          ? 'Systematisch spiergroepen aanspannen en ontspannen om fysieke spanning en stress te verminderen.'
          : 'Systematically tense and relax muscle groups to reduce physical tension and stress.'}
      </Text>
      <VoiceControl
        colors={colors}
        language={language}
        currentInstruction={getMuscleRelaxationInstructions()}
        isEnabled={voiceEnabled}
        onToggle={toggleVoiceGuidance}
      />
      {exerciseState !== 'idle' ? (
        <>
          <View style={[styles.muscleGroupCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <TouchableOpacity onPress={goToPrevMuscleGroup} style={styles.navButton}>
              <Ionicons name="chevron-back" size={24} color={colors.primary} />
            </TouchableOpacity>
            <View style={styles.muscleGroupInfo}>
              <Text style={[styles.muscleGroupName, { color: colors.text }]}>
                {muscleGroups[currentMuscleGroup].name}
              </Text>
              <Animated.Text style={[styles.tensionPhase, { color: tensionPhase === 'tense' ? colors.danger : colors.success, opacity: textOpacity }]}>
                {displayedText}
              </Animated.Text>
              <Text style={[styles.muscleGroupInstruction, { color: colors.muted }]}>
                {tensionPhase === 'tense'
                  ? muscleGroups[currentMuscleGroup].instruction
                  : (language === 'nl'
                    ? 'Laat los en voel het verschil. Merk op hoe je spieren ontspannen.'
                    : 'Release and feel the difference. Notice how your muscles relax.')}
              </Text>
            </View>
            <TouchableOpacity onPress={goToNextMuscleGroup} style={styles.navButton}>
              <Ionicons name="chevron-forward" size={24} color={colors.primary} />
            </TouchableOpacity>
          </View>
          <View style={styles.progressContainer}>
            <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${((currentMuscleGroup / (muscleGroups.length - 1)) * 100)}%`,
                    backgroundColor: colors.primary
                  }
                ]}
              />
            </View>
            <Text style={[styles.progressText, { color: colors.muted }]}>
              {currentMuscleGroup + 1}/{muscleGroups.length}
            </Text>
          </View>
          <View style={styles.timerControls}>
            <TouchableOpacity
              style={[styles.timerButton, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={resetTimer}
            >
              <Ionicons name="refresh" size={24} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.timerButton, { backgroundColor: colors.primary }]}
              onPress={toggleTimer}
            >
              {exerciseState === 'running' ? (
                <Ionicons name="pause" size={24} color="#fff" />
              ) : (
                <Ionicons name="play" size={24} color="#fff" />
              )}
            </TouchableOpacity>
            <View style={[styles.timerDisplay, { backgroundColor: colors.card, borderColor: colors.border }]}>
              <Text style={[styles.timerText, { color: colors.text }]}>{formatTime(timerSeconds)}</Text>
            </View>
          </View>
          <View style={styles.tipsContainer}>
            <Text style={[styles.tipsTitle, { color: colors.text }]}>
              {language === 'nl' ? 'Tips:' : 'Tips:'}
            </Text>
            {tipsData.map((tip, index) => (
              <Text key={index} style={[styles.tipText, { color: colors.muted }]}>
                • {tip}
              </Text>
            ))}
          </View>
        </>
      ) : (
        <View style={styles.placeholderContainer}>
          <Text style={[styles.placeholderText, { color: colors.muted }]}>
            {language === 'nl' ? 'Klaar om te beginnen?' : 'Ready to begin?'}
          </Text>
          <TouchableOpacity
            style={[styles.largeStartButton, { backgroundColor: colors.success }]}
            onPress={toggleTimer}
          >
            <Text style={styles.largeStartButtonText}>{language === 'nl' ? 'Start' : 'Start'}</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({


  container: {
    marginBottom: 30,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
  },
  largeStartButton: {
    alignItems: 'center',
    borderRadius: 25,
    elevation: 3,
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  largeStartButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  muscleGroupCard: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 16,
    padding: 16,
  },
  muscleGroupInfo: {
    alignItems: 'center',
    flex: 1,
  },
  muscleGroupInstruction: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
  muscleGroupName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  navButton: {
    padding: 8,
  },
  placeholderContainer: {
    alignItems: 'center',
    height: 250,
    justifyContent: 'center',
    marginBottom: 30,
  },
  placeholderText: {
    fontSize: 18,
    fontStyle: 'italic',
    marginBottom: 20,
  },
  progressBar: {
    borderRadius: 4,
    height: 8,
    marginBottom: 8,
    overflow: 'hidden',
    width: '100%',
  },
  progressContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  progressFill: {
    borderRadius: 4,
    height: '100%',
  },
  progressText: {
    fontSize: 14,
  },
  tensionPhase: {
    fontSize: 14,
    fontWeight: '700',
    marginBottom: 8,
  },
  timerButton: {
    alignItems: 'center',
    borderRadius: 30,
    borderWidth: 1,
    height: 60,
    justifyContent: 'center',
    marginHorizontal: 12,
    width: 60,
  },
  timerControls: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 24,
  },
  timerDisplay: {
    borderRadius: 20,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  timerText: {
    fontSize: 18,
    fontWeight: '600',
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  tipsContainer: {
    borderRadius: 12,
    padding: 16,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },


});