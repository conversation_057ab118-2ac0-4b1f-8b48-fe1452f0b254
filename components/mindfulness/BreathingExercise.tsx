import React from "react";
import { useState, useRef, useEffect, useCallback } from 'react';
import { StyleSheet, Text, View, Animated, TouchableOpacity, Platform, Easing } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { VoiceControl } from './VoiceControl';
import { MindfulnessExercise } from "@/types/mindfulness";
import { ColorScheme } from "@/hooks/ui/useColorScheme";
interface BreathingExerciseProps {
  colors: ColorScheme;
  language: string;
  _activeExercise: MindfulnessExercise;
  onDurationUpdate?: (seconds: number) => void;
}
export const BreathingExercise: React.FC<BreathingExerciseProps> = ({ colors, language, _activeExercise, onDurationUpdate }) => {
  const [exerciseState, setExerciseState] = useState<'idle' | 'running' | 'paused'>('idle');
  const [timerSeconds, setTimerSeconds] = useState(0);
  const [breathingPhase, setBreathingPhase] = useState<'inhale' | 'hold' | 'exhale' | 'rest'>('inhale');
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const breathingCircleSize = useRef(new Animated.Value(100)).current;
  const textOpacity = useRef(new Animated.Value(1)).current;
  const auraOpacity = useRef(new Animated.Value(0.1)).current; // Initial low opacity for aura
  const timerRunning = exerciseState === 'running';
  
  // Timer interval reference
  const timerRef = useRef<number | null>(null);
  
  // Breathing animation
  useEffect(() => {
    if (timerRunning) {
      // Stop any existing animations before starting new ones
      breathingCircleSize.stopAnimation();
      auraOpacity.stopAnimation();
      
      let circleSizeAnim: Animated.CompositeAnimation;
      let auraOpacityAnim: Animated.CompositeAnimation;
      const easing = Easing.inOut(Easing.quad);
      
      if (breathingPhase === 'inhale') {
        circleSizeAnim = Animated.timing(breathingCircleSize, {
          toValue: 200,
          duration: 4000,
          useNativeDriver: false,
          easing,
        });
        auraOpacityAnim = Animated.timing(auraOpacity, {
          toValue: 0.5, // More visible aura
          duration: 4000,
          useNativeDriver: false,
          easing,
        });
        Animated.parallel([circleSizeAnim, auraOpacityAnim]).start(() => {
          setBreathingPhase('hold');
        });
      } else if (breathingPhase === 'hold') {
        circleSizeAnim = Animated.timing(breathingCircleSize, {
          toValue: 200, // Stays large
          duration: 4000,
          useNativeDriver: false,
          easing,
        });
        auraOpacityAnim = Animated.timing(auraOpacity, {
          toValue: 0.5, // Aura stays visible
          duration: 4000,
          useNativeDriver: false,
          easing,
        });
        Animated.parallel([circleSizeAnim, auraOpacityAnim]).start(() => {
          setBreathingPhase('exhale');
        });
      } else if (breathingPhase === 'exhale') {
        circleSizeAnim = Animated.timing(breathingCircleSize, {
          toValue: 100,
          duration: 4000,
          useNativeDriver: false,
          easing,
        });
        auraOpacityAnim = Animated.timing(auraOpacity, {
          toValue: 0.1, // Aura fades
          duration: 4000,
          useNativeDriver: false,
          easing,
        });
        Animated.parallel([circleSizeAnim, auraOpacityAnim]).start(() => {
          setBreathingPhase('rest');
        });
      } else if (breathingPhase === 'rest') {
        circleSizeAnim = Animated.timing(breathingCircleSize, {
          toValue: 100, // Stays small
          duration: 2000,
          useNativeDriver: false,
          easing,
        });
        auraOpacityAnim = Animated.timing(auraOpacity, {
          toValue: 0.1, // Aura stays faded
          duration: 2000,
          useNativeDriver: false,
          easing,
        });
        Animated.parallel([circleSizeAnim, auraOpacityAnim]).start(() => {
          setBreathingPhase('inhale');
        });
      }
    } else {
      // Stop animations if timer is not running (e.g. paused or idle after reset)
      breathingCircleSize.stopAnimation();
      auraOpacity.stopAnimation();
    }
  }, [breathingPhase, timerRunning, breathingCircleSize, auraOpacity]);
  
  // Timer effect - updates local timerSeconds
  useEffect(() => {
    if (timerRunning) {
      timerRef.current = setInterval(() => {
        setTimerSeconds(prev => prev + 1);
      }, 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [timerRunning]);
  // Effect to report duration when timerSeconds changes
  useEffect(() => {
    onDurationUpdate?.(timerSeconds);
  }, [timerSeconds, onDurationUpdate]);
  
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  const toggleTimer = () => {
    if (exerciseState === 'idle') {
      setExerciseState('running');
    } else if (exerciseState === 'running') {
      setExerciseState('paused');
    } else if (exerciseState === 'paused') {
      setExerciseState('running');
    }
    
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };
  
  const resetTimer = () => {
    setExerciseState('idle');
    setTimerSeconds(0);
    setBreathingPhase('inhale');
    
    // Stop any ongoing animations first
    breathingCircleSize.stopAnimation();
    auraOpacity.stopAnimation();
    textOpacity.stopAnimation();
    
    // Reset animation values immediately
    breathingCircleSize.setValue(100);
    auraOpacity.setValue(0.1);
    textOpacity.setValue(1);
    
    // Update text immediately for idle state
    setDisplayedText(getBreathingInstructions());
    
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };
  
  const getBreathingInstructions = useCallback(() => {
    if (exerciseState === 'idle') {
      return language === 'nl' ? 'Druk op start om te beginnen' : 'Press start to begin';
    }
    if (exerciseState === 'paused') {
      return language === 'nl' ? 'Gepauzeerd' : 'Paused';
    }
    switch (breathingPhase) {
      case 'inhale':
        return language === 'nl' ? 'Adem in...' : 'Breathe in...';
      case 'hold':
        return language === 'nl' ? 'Houd vast...' : 'Hold...';
      case 'exhale':
        return language === 'nl' ? 'Adem uit...' : 'Breathe out...';
      case 'rest':
        return language === 'nl' ? 'Rust...' : 'Rest...';
      default:
        return '';
    }
  }, [exerciseState, breathingPhase, language]);
  
  const [displayedText, setDisplayedText] = useState(() => getBreathingInstructions());
  useEffect(() => {
    const newInstruction = getBreathingInstructions();
    if (newInstruction !== displayedText) {
      // Stop any existing text animation
      textOpacity.stopAnimation();
      
      Animated.sequence([
        Animated.timing(textOpacity, {
          toValue: 0,
          duration: 200, // Short fade-out
          useNativeDriver: false,
          easing: Easing.linear,
        }),
      ]).start(() => {
        setDisplayedText(newInstruction);
        Animated.timing(textOpacity, {
          toValue: 1,
          duration: 200, // Short fade-in
          useNativeDriver: false,
          easing: Easing.linear,
        }).start();
      });
    } else if (newInstruction === displayedText) {
      // If the instruction text is the same, ensure opacity is 1 (e.g., after reset or if animation was interrupted)
      // This will also run if textOpacity was changed externally (like in resetTimer)
      textOpacity.stopAnimation();
      Animated.timing(textOpacity, {
        toValue: 1,
        duration: 50, // A very short duration to ensure it's visible, or 0 for instant
        useNativeDriver: false,
        easing: Easing.linear,
      }).start();
    }
  }, [getBreathingInstructions, displayedText, textOpacity]);
  const toggleVoiceGuidance = () => {
    setVoiceEnabled(prev => !prev);
  };
  
  // Cleanup effect - stop all animations when component unmounts
  useEffect(() => {
    return () => {
      breathingCircleSize.stopAnimation();
      auraOpacity.stopAnimation();
      textOpacity.stopAnimation();
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [breathingCircleSize, auraOpacity, textOpacity]);
  
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>
        {language === 'nl' ? '4-4-4-2 Ademhalingsoefening' : '4-4-4-2 Breathing Exercise'}
      </Text>
      <Text style={[styles.description, { color: colors.muted }]}>
        {language === 'nl' 
          ? 'Deze techniek helpt je om te kalmeren en je focus te verbeteren. Adem 4 seconden in, houd 4 seconden vast, adem 4 seconden uit, en rust 2 seconden.'
          : 'This technique helps calm your mind and improve focus. Breathe in for 4 seconds, hold for 4 seconds, breathe out for 4 seconds, and rest for 2 seconds.'}
      </Text>
      
      <VoiceControl
        colors={colors}
        language={language}
        currentInstruction={getBreathingInstructions()}
        isEnabled={voiceEnabled}
        onToggle={toggleVoiceGuidance}
      />
      
      {exerciseState !== 'idle' ? (
        <View style={styles.breathingContainer}>
          <Animated.View // Aura circle
            style={[
              styles.auraCircle,
              styles.auraCircleRounded,
              {
                width: breathingCircleSize,
                height: breathingCircleSize,
                backgroundColor: colors.primary,
                opacity: auraOpacity,
                transform: [{ scale: 1.2 }],
              },
            ]}
          />
          <Animated.View // Main breathing circle
            style={[
              styles.breathingCircle, 
              { 
                width: breathingCircleSize, 
                height: breathingCircleSize,
                backgroundColor: colors.primary + '30',
                borderColor: colors.primary
              }
            ]} 
          />
          <Animated.Text style={[styles.breathingInstructions, { color: colors.primary, opacity: textOpacity }]}>
            {displayedText}
          </Animated.Text>
        </View>
      ) : (
        <View style={styles.placeholderContainer}>
          <Text style={[styles.placeholderText, { color: colors.muted }]}>
            {language === 'nl' ? 'Klaar om te beginnen?' : 'Ready to begin?'}
          </Text>
          <TouchableOpacity 
            style={[styles.largeStartButton, { backgroundColor: colors.success }]} 
            onPress={toggleTimer}
          >
            <Text style={styles.largeStartButtonText}>{language === 'nl' ? 'Start' : 'Start'}</Text>
          </TouchableOpacity>
        </View>
      )}
      
      {exerciseState !== 'idle' && (
        <View style={styles.timerControls}>
          <TouchableOpacity 
            style={[styles.timerButton, { backgroundColor: colors.card, borderColor: colors.border }]}
            onPress={resetTimer}
          >
            <Ionicons name="refresh" size={24} color={colors.text} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.timerButton, { backgroundColor: colors.primary }]} // This button is now only for pause/play
            onPress={toggleTimer}
          >
            {exerciseState === 'running' ? (
              <Ionicons name="pause" size={24} color="#fff" />
            ) : ( // Only 'paused' state will reach here as 'idle' hides timerControls
              <Ionicons name="play" size={24} color="#fff" />
            )}
          </TouchableOpacity>
          
          <View style={[styles.timerDisplay, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <Text style={[styles.timerText, { color: colors.text }]}>{formatTime(timerSeconds)}</Text>
          </View>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({


  auraCircle: {
    position: 'absolute',
  },
  auraCircleRounded: {
    borderRadius: 100,
  },
  breathingCircle: {
    alignItems: 'center',
    borderRadius: 999,
    borderWidth: 2,
    justifyContent: 'center',
    position: 'absolute',
  },
  breathingContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    position: 'relative',
    width: '100%', // Needed for absolute positioning of aura
  },
  breathingInstructions: {
    fontSize: 18,
    fontWeight: '600',
  },
  container: {
    marginBottom: 30,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
  },
  largeStartButton: {
    alignItems: 'center',
    borderRadius: 25, // Rounded corners
    elevation: 3, // Android shadow
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 12,
    shadowColor: '#000', // iOS shadow
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  largeStartButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholderContainer: {
    alignItems: 'center',
    height: 250,
    justifyContent: 'center',
    marginBottom: 30,
  },
  placeholderText: {
    fontSize: 18,
    fontStyle: 'italic',
    marginBottom: 20, // Add some space above the new start button
  },
  timerButton: {
    alignItems: 'center',
    borderRadius: 30,
    borderWidth: 1,
    height: 60,
    justifyContent: 'center',
    marginHorizontal: 12,
    width: 60,
  },
  timerControls: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  timerDisplay: {
    borderRadius: 20,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  timerText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },


});