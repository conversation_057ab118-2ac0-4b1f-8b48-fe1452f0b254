import React from "react";
import { useEffect, useRef } from "react";
import {
  StyleSheet,
  Text,
  View,
  Animated,
  Easing,
  TouchableOpacity,
} from "react-native";
import { CheckCircle, Circle, Flame } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { ColorScheme } from "@/hooks/ui/useColorScheme";
interface Habit {
  id: string;
  title: string;
  description: string;
  streak: number;
  completedToday: boolean;
  weeklyProgress: boolean[]; // 7 days, true = completed
  category: "health" | "mindfulness" | "social" | "personal";
}
interface HabitsCardProps {
  habits: Habit[];
  language: string;
  colors: ColorScheme;
  onPress?: () => void;
  onToggleHabit?: (habitId: string) => void;
}
export const HabitsCard: React.FC<HabitsCardProps> = ({
  habits,
  language,
  colors,
  onPress,
  onToggleHabit,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.98)).current;
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim]);
  const getCategoryColor = (category: string) => {
    switch (category) {
      case "health":
        return colors.success;
      case "mindfulness":
        return colors.primary;
      case "social":
        return colors.info;
      case "personal":
        return colors.warning;
      default:
        return colors.primary;
    }
  };
  const getCategoryGradient = (category: string): [string, string] => {
    const color = getCategoryColor(category);
    return [color + "20", color + "10"];
  };
  const completedToday = habits.filter((h) => h.completedToday).length;
  const totalHabits = habits.length;
  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <TouchableOpacity
        style={[
          styles.card,
          { backgroundColor: colors.card, borderColor: colors.border },
        ]}
        onPress={onPress}
        activeOpacity={0.8}
      >
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            {language === "nl" ? "Gewoontes" : "Habits"}
          </Text>
          <View style={styles.progressBadge}>
            <Text style={[styles.progressText, { color: colors.primary }]}>
              {completedToday}/{totalHabits}
            </Text>
          </View>
        </View>
        <View style={styles.habitsContainer}>
          {habits.slice(0, 4).map((habit, _index) => (
            <TouchableOpacity
              key={habit.id}
              style={styles.habitItem}
              onPress={() => onToggleHabit?.(habit.id)}
              activeOpacity={0.7}
            >
              <LinearGradient
                colors={getCategoryGradient(habit.category)}
                style={styles.habitIcon}
              >
                {habit.completedToday ? (
                  <CheckCircle
                    size={20}
                    color={getCategoryColor(habit.category)}
                  />
                ) : (
                  <Circle size={20} color={getCategoryColor(habit.category)} />
                )}
              </LinearGradient>
              <View style={styles.habitContent}>
                <View style={styles.habitHeader}>
                  <Text
                    style={[styles.habitTitle, { color: colors.text }]}
                    numberOfLines={1}
                  >
                    {habit.title}
                  </Text>
                  {habit.streak > 0 && (
                    <View style={styles.streakContainer}>
                      <Flame size={14} color={colors.warning} />
                      <Text
                        style={[styles.streakText, { color: colors.warning }]}
                      >
                        {habit.streak}
                      </Text>
                    </View>
                  )}
                </View>
                <View style={styles.weeklyProgress}>
                  {habit.weeklyProgress.map((completed, dayIndex) => (
                    <View
                      key={dayIndex}
                      style={[
                        styles.dayDot,
                        {
                          backgroundColor: completed
                            ? getCategoryColor(habit.category)
                            : colors.border,
                        },
                      ]}
                    />
                  ))}
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
        {habits.length > 4 && (
          <View style={styles.moreContainer}>
            <Text style={[styles.moreText, { color: colors.muted }]}>
              {language === "nl"
                ? `+${habits.length - 4} meer gewoontes`
                : `+${habits.length - 4} more habits`}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};
const styles = StyleSheet.create({


  card: {
    borderRadius: 16,
    borderWidth: 1,
    elevation: 4,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  container: {
    marginBottom: 16,
    marginHorizontal: 16,
  },
  dayDot: {
    borderRadius: 4,
    height: 8,
    width: 8,
  },
  habitContent: {
    flex: 1,
  },
  habitHeader: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 6,
  },
  habitIcon: {
    alignItems: "center",
    borderRadius: 20,
    height: 40,
    justifyContent: "center",
    width: 40,
  },
  habitItem: {
    alignItems: "center",
    flexDirection: "row",
    gap: 12,
  },
  habitTitle: {
    flex: 1,
    fontSize: 14,
    fontWeight: "600",
  },
  habitsContainer: {
    gap: 12,
  },
  header: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  moreContainer: {
    alignItems: "center",
    borderTopColor: "rgba(0,0,0,0.05)",
    borderTopWidth: 1,
    marginTop: 12,
    paddingTop: 12,
  },
  moreText: {
    fontSize: 12,
    fontWeight: "500",
  },
  progressBadge: {
    backgroundColor: "rgba(52, 152, 219, 0.1)",
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: "600",
  },
  streakContainer: {
    alignItems: "center",
    flexDirection: "row",
    gap: 2,
  },
  streakText: {
    fontSize: 12,
    fontWeight: "600",
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
  },
  weeklyProgress: {
    flexDirection: "row",
    gap: 4,
  },


});