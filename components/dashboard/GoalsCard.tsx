import React from "react";
import {
  StyleSheet,
  Text,
  View,
  Animated,
  TouchableOpacity,
} from "react-native";
import { Target, CheckCircle, Clock, TrendingUp } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useFadeSlideAnimation } from "@/hooks";

interface Goal {
  id: string;
  title: string;
  description: string;
  progress: number;
  target: number;
  unit: string;
  deadline?: string;
  completed: boolean;
  category: "sobriety" | "health" | "personal" | "financial";
}

interface Colors {
  card: string;
  border: string;
  text: string;
  primary: string;
  success: string;
  info: string;
  warning: string;
  muted: string;
}

interface GoalsCardProps {
  goals: Goal[];
  language: string;
  colors: Colors;
  onPress?: () => void;
}

export const GoalsCard: React.FC<GoalsCardProps> = ({
  goals,
  language,
  colors,
  onPress,
}) => {
  const { animatedStyle } = useFadeSlideAnimation({
    duration: 700,
    slideDistance: 20,
  });

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "sobriety":
        return <Target size={18} color={colors.primary} />;
      case "health":
        return <TrendingUp size={18} color={colors.success} />;
      case "personal":
        return <CheckCircle size={18} color={colors.info} />;
      case "financial":
        return <Clock size={18} color={colors.warning} />;
      default:
        return <Target size={18} color={colors.primary} />;
    }
  };

  const getCategoryGradient = (category: string): [string, string] => {
    switch (category) {
      case "sobriety":
        return [colors.primary + "20", colors.primary + "10"];
      case "health":
        return [colors.success + "20", colors.success + "10"];
      case "personal":
        return [colors.info + "20", colors.info + "10"];
      case "financial":
        return [colors.warning + "20", colors.warning + "10"];
      default:
        return [colors.primary + "20", colors.primary + "10"];
    }
  };

  const activeGoals = goals.filter((g) => !g.completed);
  const completedGoals = goals.filter((g) => g.completed);

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <TouchableOpacity
        style={[
          styles.card,
          { backgroundColor: colors.card, borderColor: colors.border },
        ]}
        onPress={onPress}
        activeOpacity={0.8}
      >
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            {language === "nl" ? "Doelen" : "Goals"}
          </Text>
          <View style={styles.badges}>
            {activeGoals.length > 0 && (
              <View
                style={[
                  styles.badge,
                  { backgroundColor: colors.primary + "20" },
                ]}
              >
                <Text style={[styles.badgeText, { color: colors.primary }]}>
                  {activeGoals.length} {language === "nl" ? "actief" : "active"}
                </Text>
              </View>
            )}
            {completedGoals.length > 0 && (
              <View
                style={[
                  styles.badge,
                  { backgroundColor: colors.success + "20" },
                ]}
              >
                <Text style={[styles.badgeText, { color: colors.success }]}>
                  {completedGoals.length}{" "}
                  {language === "nl" ? "voltooid" : "completed"}
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.goalsContainer}>
          {activeGoals.slice(0, 3).map((goal) => (
            <View key={goal.id} style={styles.goalItem}>
              <LinearGradient
                colors={getCategoryGradient(goal.category)}
                style={styles.goalIcon}
              >
                {getCategoryIcon(goal.category)}
              </LinearGradient>

              <View style={styles.goalContent}>
                <View style={styles.goalHeader}>
                  <Text
                    style={[styles.goalTitle, { color: colors.text }]}
                    numberOfLines={1}
                  >
                    {goal.title}
                  </Text>
                  <Text
                    style={[styles.goalProgress, { color: colors.primary }]}
                  >
                    {Math.round((goal.progress / goal.target) * 100)}%
                  </Text>
                </View>

                <View style={styles.progressContainer}>
                  <View
                    style={[
                      styles.progressBar,
                      { backgroundColor: colors.border },
                    ]}
                  >
                    <View
                      style={[
                        styles.progressFill,
                        {
                          backgroundColor: colors.primary,
                          width: `${Math.min(
                            (goal.progress / goal.target) * 100,
                            100
                          )}%`,
                        },
                      ]}
                    />
                  </View>
                  <Text style={[styles.progressText, { color: colors.muted }]}>
                    {goal.progress}/{goal.target} {goal.unit}
                  </Text>
                </View>

                {goal.deadline && (
                  <Text style={[styles.deadline, { color: colors.muted }]}>
                    {language === "nl" ? "Deadline:" : "Due:"}{" "}
                    {new Date(goal.deadline).toLocaleDateString()}
                  </Text>
                )}
              </View>
            </View>
          ))}

          {completedGoals.length > 0 && (
            <View style={styles.completedSection}>
              <Text style={[styles.completedTitle, { color: colors.success }]}>
                {language === "nl" ? "Recent voltooid" : "Recently Completed"}
              </Text>
              {completedGoals.slice(0, 2).map((goal) => (
                <View key={goal.id} style={styles.completedGoal}>
                  <CheckCircle size={16} color={colors.success} />
                  <Text
                    style={[styles.completedGoalText, { color: colors.text }]}
                    numberOfLines={1}
                  >
                    {goal.title}
                  </Text>
                </View>
              ))}
            </View>
          )}
        </View>

        {goals.length > 3 && (
          <View style={styles.moreContainer}>
            <Text style={[styles.moreText, { color: colors.muted }]}>
              {language === "nl"
                ? `+${goals.length - 3} meer doelen`
                : `+${goals.length - 3} more goals`}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({


  badge: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  badgeText: {
    fontSize: 11,
    fontWeight: "600",
  },
  badges: {
    flexDirection: "row",
    gap: 8,
  },
  card: {
    borderRadius: 16,
    borderWidth: 1,
    elevation: 4,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  completedGoal: {
    alignItems: "center",
    flexDirection: "row",
    gap: 8,
    marginBottom: 4,
  },
  completedGoalText: {
    flex: 1,
    fontSize: 12,
  },
  completedSection: {
    borderTopColor: "rgba(0,0,0,0.05)",
    borderTopWidth: 1,
    paddingTop: 12,
  },
  completedTitle: {
    fontSize: 12,
    fontWeight: "600",
    marginBottom: 8,
  },
  container: {
    marginBottom: 16,
    marginHorizontal: 16,
  },
  deadline: {
    fontSize: 11,
    marginTop: 2,
  },
  goalContent: {
    flex: 1,
  },
  goalHeader: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  goalIcon: {
    alignItems: "center",
    borderRadius: 18,
    height: 36,
    justifyContent: "center",
    width: 36,
  },
  goalItem: {
    alignItems: "flex-start",
    flexDirection: "row",
    gap: 12,
  },
  goalProgress: {
    fontSize: 14,
    fontWeight: "700",
  },
  goalTitle: {
    flex: 1,
    fontSize: 14,
    fontWeight: "600",
  },
  goalsContainer: {
    gap: 16,
  },
  header: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  moreContainer: {
    alignItems: "center",
    borderTopColor: "rgba(0,0,0,0.05)",
    borderTopWidth: 1,
    marginTop: 12,
    paddingTop: 12,
  },
  moreText: {
    fontSize: 12,
    fontWeight: "500",
  },
  progressBar: {
    borderRadius: 3,
    height: 6,
    marginBottom: 4,
    overflow: "hidden",
  },
  progressContainer: {
    marginBottom: 4,
  },
  progressFill: {
    borderRadius: 3,
    height: "100%",
  },
  progressText: {
    fontSize: 12,
    fontWeight: "500",
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
  },


});
