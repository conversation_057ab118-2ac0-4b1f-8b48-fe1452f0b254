import React from "react";
import {
  StyleSheet,
  Text,
  View,
  Animated,
  TouchableOpacity,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import {
  getTrendIcon,
  getInsightTypeIcon,
  TrendType,
  InsightType,
} from "@/utils";
import { useFadeSlideAnimation } from "@/hooks";

interface Insight {
  id: string;
  title: string;
  description: string;
  trend: TrendType;
  value: string;
  change: string;
  type: InsightType;
}

interface Colors {
  card: string;
  border: string;
  text: string;
  info: string;
  success: string;
  danger: string;
  muted: string;
  primary: string;
  warning: string;
}

interface InsightsCardProps {
  insights: Insight[];
  language: string;
  colors: Colors;
  onPress?: () => void;
  isCompact?: boolean;
}

export const InsightsCard: React.FC<InsightsCardProps> = ({
  insights,
  language,
  colors,
  onPress,
  isCompact = false,
}) => {
  const { animatedStyle } = useFadeSlideAnimation({
    duration: 600,
    slideDistance: 30,
  });

  const getTrendIconForInsight = (trend: TrendType) => {
    const trendColor =
      trend === "up"
        ? colors.success
        : trend === "down"
        ? colors.danger
        : colors.muted;

    return getTrendIcon(trend, {
      size: 16,
      color: trendColor,
    });
  };

  const getTypeIcon = (type: InsightType) => {
    const typeColor =
      type === "mood"
        ? colors.primary
        : type === "health"
        ? colors.success
        : type === "progress"
        ? colors.warning
        : colors.primary;

    return getInsightTypeIcon(type, {
      size: 20,
      color: typeColor,
    });
  };

  const getTypeGradient = (type: string): [string, string] => {
    switch (type) {
      case "mood":
        return [colors.primary + "20", colors.primary + "10"];
      case "health":
        return [colors.success + "20", colors.success + "10"];
      case "progress":
        return [colors.warning + "20", colors.warning + "10"];
      default:
        return [colors.primary + "20", colors.primary + "10"];
    }
  };

  if (isCompact) {
    const firstInsight = insights[0];
    return (
      <Animated.View style={[styles.container, animatedStyle]}>
        <TouchableOpacity
          style={[
            styles.card,
            styles.compactCard,
            { backgroundColor: colors.card, borderColor: colors.border },
          ]}
          onPress={onPress}
          activeOpacity={0.8}
        >
          <View style={styles.compactHeader}>
            <View style={styles.compactTitleContainer}>
              <LinearGradient
                colors={firstInsight ? getTypeGradient(firstInsight.type) : [colors.primary + "20", colors.primary + "10"]}
                style={styles.compactIcon}
              >
                {firstInsight ? getTypeIcon(firstInsight.type) : getTypeIcon("progress")}
              </LinearGradient>
              <Text style={[styles.compactTitle, { color: colors.text }]}>
                {language === "nl" ? "Inzichten" : "Insights"}
              </Text>
            </View>
            <View style={[styles.badge, { backgroundColor: colors.info + "20" }]}>
              <Text style={[styles.badgeText, { color: colors.info }]}>
                {insights.length}
              </Text>
            </View>
          </View>
          {firstInsight && (
            <View style={styles.compactContent}>
              <Text style={[styles.compactValue, { color: colors.primary }]}>
                {firstInsight.value}
              </Text>
              <View style={styles.compactTrend}>
                {getTrendIconForInsight(firstInsight.trend)}
                <Text
                  style={[
                    styles.compactChange,
                    {
                      color:
                        firstInsight.trend === "up"
                          ? colors.success
                          : firstInsight.trend === "down"
                          ? colors.danger
                          : colors.muted,
                    },
                  ]}
                >
                  {firstInsight.change}
                </Text>
              </View>
            </View>
          )}
          <Text style={[styles.compactSubtitle, { color: colors.muted }]}>
            {language === "nl" ? "Tik om uit te klappen" : "Tap to expand"}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <TouchableOpacity
        style={[
          styles.card,
          { backgroundColor: colors.card, borderColor: colors.border },
        ]}
        onPress={onPress}
        activeOpacity={0.8}
      >
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            {language === "nl" ? "Inzichten" : "Insights"}
          </Text>
          <View style={[styles.badge, { backgroundColor: colors.info + "20" }]}>
            <Text style={[styles.badgeText, { color: colors.info }]}>
              {insights.length}
            </Text>
          </View>
        </View>

        <View style={styles.insightsContainer}>
          {insights.slice(0, 3).map((insight) => (
            <View key={insight.id} style={styles.insightItem}>
              <LinearGradient
                colors={getTypeGradient(insight.type)}
                style={styles.insightIcon}
              >
                {getTypeIcon(insight.type)}
              </LinearGradient>

              <View style={styles.insightContent}>
                <View style={styles.insightHeader}>
                  <Text
                    style={[styles.insightTitle, { color: colors.text }]}
                    numberOfLines={1}
                  >
                    {insight.title}
                  </Text>
                  <View style={styles.trendContainer}>
                    {getTrendIconForInsight(insight.trend)}
                    <Text
                      style={[
                        styles.changeText,
                        {
                          color:
                            insight.trend === "up"
                              ? colors.success
                              : insight.trend === "down"
                              ? colors.danger
                              : colors.muted,
                        },
                      ]}
                    >
                      {insight.change}
                    </Text>
                  </View>
                </View>

                <Text style={[styles.insightValue, { color: colors.primary }]}>
                  {insight.value}
                </Text>

                <Text
                  style={[styles.insightDesc, { color: colors.muted }]}
                  numberOfLines={2}
                >
                  {insight.description}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {insights.length > 3 && (
          <View style={styles.moreContainer}>
            <Text style={[styles.moreText, { color: colors.muted }]}>
              {language === "nl"
                ? `+${insights.length - 3} meer inzichten`
                : `+${insights.length - 3} more insights`}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({


  badge: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: "600",
  },
  card: {
    borderRadius: 16,
    borderWidth: 1,
    elevation: 4,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  changeText: {
    fontSize: 12,
    fontWeight: "500",
  },
  compactCard: {
    minHeight: 60,
    padding: 12,
  },
  compactChange: {
    fontSize: 11,
    fontWeight: "500",
  },
  compactContent: {
    alignItems: "center",
    flexDirection: "row",
    gap: 8,
    marginBottom: 4,
  },
  compactHeader: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 6,
  },
  compactIcon: {
    alignItems: "center",
    borderRadius: 12,
    height: 24,
    justifyContent: "center",
    width: 24,
  },
  compactSubtitle: {
    fontSize: 11,
    fontStyle: "italic",
  },
  compactTitle: {
    fontSize: 14,
    fontWeight: "600",
  },
  compactTitleContainer: {
    alignItems: "center",
    flexDirection: "row",
    gap: 8,
  },
  compactTrend: {
    alignItems: "center",
    flexDirection: "row",
    gap: 4,
  },
  compactValue: {
    fontSize: 16,
    fontWeight: "700",
  },
  container: {
    marginBottom: 16,
    marginHorizontal: 16,
  },
  header: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  insightContent: {
    flex: 1,
  },
  insightDesc: {
    fontSize: 12,
    lineHeight: 16,
  },
  insightHeader: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  insightIcon: {
    alignItems: "center",
    borderRadius: 20,
    height: 40,
    justifyContent: "center",
    width: 40,
  },
  insightItem: {
    alignItems: "flex-start",
    flexDirection: "row",
    gap: 12,
  },
  insightTitle: {
    flex: 1,
    fontSize: 14,
    fontWeight: "600",
  },
  insightValue: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 4,
  },
  insightsContainer: {
    gap: 16,
  },
  moreContainer: {
    alignItems: "center",
    borderTopColor: "rgba(0,0,0,0.05)",
    borderTopWidth: 1,
    marginTop: 12,
    paddingTop: 12,
  },
  moreText: {
    fontSize: 12,
    fontWeight: "500",
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
  },
  trendContainer: {
    alignItems: "center",
    flexDirection: "row",
    gap: 4,
  },


});
