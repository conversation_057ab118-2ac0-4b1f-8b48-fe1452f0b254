import React from "react";
import { useEffect, useRef } from "react";
import {
  StyleSheet,
  Text,
  View,
  Animated,
  Easing,
  TouchableOpacity,
} from "react-native";
import { Wallet, ShoppingBag, TrendingUp } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useFadeScaleAnimation } from "@/hooks";
import { getSubstanceIconForSavings } from "@/utils";
interface Colors {
  success: string;
  successDark?: string;
}
interface SavingsCardProps {
  savedAmount: number;
  usageCost: number;
  costFrequency: string;
  language: string;
  currency: "USD" | "EUR";
  colors: Colors;
  usageAmount?: string;
  substanceType?: string;
}
export const SavingsCard: React.FC<SavingsCardProps> = ({
  savedAmount,
  usageCost,
  costFrequency,
  language,
  currency,
  colors,
  usageAmount,
  substanceType,
}) => {
  const currencySymbol = currency === "USD" ? "$" : "€";
  // Use shared animation hook
  const { animatedStyle } = useFadeScaleAnimation({
    duration: 800,
  });
  // Separate animation for the amount counter
  const amountAnim = useRef(new Animated.Value(0)).current;
  // Animated amount value
  const animatedAmount = amountAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, savedAmount],
  });
  // Start amount animation when component mounts
  useEffect(() => {
    Animated.timing(amountAnim, {
      toValue: 1,
      duration: 1800,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: false,
    }).start();
  }, [savedAmount, amountAnim]);
  // Calculate monthly savings
  const getMonthlySavings = () => {
    switch (costFrequency) {
      case "daily":
        return usageCost * 30;
      case "weekly":
        return usageCost * 4.3;
      case "monthly":
        return usageCost;
      default:
        return 0;
    }
  };
  const monthlySavings = getMonthlySavings();
  // Get frequency text
  const getFrequencyText = () => {
    if (language === "nl") {
      switch (costFrequency) {
        case "daily":
          return "per dag";
        case "weekly":
          return "per week";
        case "monthly":
          return "per maand";
        default:
          return "";
      }
    } else {
      switch (costFrequency) {
        case "daily":
          return "per day";
        case "weekly":
          return "per week";
        case "monthly":
          return "per month";
        default:
          return "";
      }
    }
  };
  // Get substance icon using shared utility
  const getSubstanceIcon = () => {
    return getSubstanceIconForSavings(substanceType, {
      size: 40,
      color: "#fff",
      strokeWidth: 1.5,
    });
  };
  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <LinearGradient
        colors={
          [colors.success, colors.successDark || "#218c74"] as [string, string]
        }
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.savingsCard}
      >
        <View style={styles.savingsContent}>
          <View style={styles.savingsTextContainer}>
            <Text style={styles.savingsLabel}>
              {language === "nl" ? "Bespaarde kosten" : "Money Saved"}
            </Text>
            <View style={styles.amountContainer}>
              <Text style={styles.currencySymbol}>{currencySymbol}</Text>
              <Animated.Text style={styles.savingsAmount}>
                {animatedAmount.interpolate({
                  inputRange: [0, savedAmount],
                  outputRange: [0, savedAmount].map((val) => val.toFixed(2)),
                })}
              </Animated.Text>
            </View>
            <Text style={styles.savingsDetails}>
              {language === "nl"
                ? `${currencySymbol}${usageCost} ${getFrequencyText()} niet uitgegeven`
                : `${currencySymbol}${usageCost} ${getFrequencyText()} not spent`}
            </Text>
            {usageAmount && usageAmount !== "Not specified" && (
              <Text style={styles.usageAmount}>
                {language === "nl"
                  ? `Voorheen: ${usageAmount} ${getFrequencyText()}`
                  : `Previously: ${usageAmount} ${getFrequencyText()}`}
              </Text>
            )}
          </View>
          <View style={styles.savingsIconContainer}>
            {currency === "USD" ? getSubstanceIcon() : getSubstanceIcon()}
          </View>
        </View>
        <View style={styles.statsContainer}>
          <TouchableOpacity style={styles.statItem}>
            <View style={styles.statIconContainer}>
              <Wallet size={18} color="rgba(255, 255, 255, 0.9)" />
            </View>
            <View style={styles.statTextContainer}>
              <Text style={styles.statLabel}>
                {language === "nl" ? "Maandelijks" : "Monthly"}
              </Text>
              <Text style={styles.statValue}>
                {currencySymbol}
                {monthlySavings.toFixed(0)}
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={styles.statItem}>
            <View style={styles.statIconContainer}>
              <ShoppingBag size={18} color="rgba(255, 255, 255, 0.9)" />
            </View>
            <View style={styles.statTextContainer}>
              <Text style={styles.statLabel}>
                {language === "nl" ? "Jaarlijks" : "Yearly"}
              </Text>
              <Text style={styles.statValue}>
                {currencySymbol}
                {(monthlySavings * 12).toFixed(0)}
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={styles.statItem}>
            <View style={styles.statIconContainer}>
              <TrendingUp size={18} color="rgba(255, 255, 255, 0.9)" />
            </View>
            <View style={styles.statTextContainer}>
              <Text style={styles.statLabel}>
                {language === "nl" ? "5 Jaar" : "5 Years"}
              </Text>
              <Text style={styles.statValue}>
                {currencySymbol}
                {(monthlySavings * 12 * 5).toFixed(0)}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </Animated.View>
  );
};
const styles = StyleSheet.create({


  amountContainer: {
    alignItems: "flex-start",
    flexDirection: "row",
  },
  container: {
    elevation: 8,
    marginBottom: 20,
    marginHorizontal: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  currencySymbol: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "bold",
    marginRight: 2,
    marginTop: 8,
  },
  savingsAmount: {
    color: "#fff",
    fontSize: 40,
    fontWeight: "bold",
    textShadowColor: "rgba(0, 0, 0, 0.1)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  savingsCard: {
    borderRadius: 20,
    overflow: "hidden",
    padding: 20,
  },
  savingsContent: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  savingsDetails: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 14,
    marginTop: 4,
  },
  savingsIconContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 18,
    padding: 14,
  },
  savingsLabel: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
  },
  savingsTextContainer: {
    flex: 1,
  },
  statIconContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 10,
    marginBottom: 6,
    padding: 8,
  },
  statItem: {
    alignItems: "center",
    flex: 1,
    flexDirection: "column",
    paddingHorizontal: 4,
  },
  statLabel: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 12,
    marginBottom: 2,
  },
  statTextContainer: {
    alignItems: "center",
  },
  statValue: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "bold",
  },
  statsContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    borderRadius: 14,
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
    padding: 12,
  },
  usageAmount: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 14,
    fontStyle: "italic",
    marginTop: 4,
  },


});