import React from "react";
import { StyleSheet, View, Text } from "react-native";
import { Bar<PERSON><PERSON> } from "react-native-chart-kit";
import { Dimensions } from "react-native";
import { Relapse } from "@/types/user";

interface ChartCardProps {
  title: string;
  data: Relapse[];
  colors: {
    card: string;
    border: string;
    text: string;
    muted: string;
    primary: string;
  };
  language: string;
}

export const ChartCard: React.FC<ChartCardProps> = ({
  title,
  data,
  colors,
  language,
}) => {
  // Count trigger frequencies
  const triggerCounts: Record<string, number> = {};

  data.forEach((relapse) => {
    if (relapse.triggers && Array.isArray(relapse.triggers)) {
      relapse.triggers.forEach((trigger) => {
        triggerCounts[trigger] = (triggerCounts[trigger] || 0) + 1;
      });
    }
  });

  // Sort triggers by frequency
  const sortedTriggers = Object.entries(triggerCounts)
    .sort((a, b) => (b[1] as number) - (a[1] as number))
    .slice(0, 5); // Take top 5

  // If no data, show placeholder
  if (sortedTriggers.length === 0) {
    return (
      <View
        style={[
          styles.container,
          { backgroundColor: colors.card, borderColor: colors.border },
        ]}
      >
        <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
        <View style={styles.placeholderContainer}>
          <Text style={[styles.placeholderText, { color: colors.muted }]}>
            {language === "nl"
              ? "Nog geen terugvalgegevens beschikbaar."
              : "No relapse data available yet."}
          </Text>
        </View>
      </View>
    );
  }

  const chartData = {
    labels: sortedTriggers.map(([trigger]) => trigger.substring(0, 6) + "..."),
    datasets: [
      {
        data: sortedTriggers.map(([_, count]) => count as number),
      },
    ],
  };

  const chartConfig = {
    backgroundGradientFrom: colors.card,
    backgroundGradientTo: colors.card,
    decimalPlaces: 0,
    color: () => colors.primary,
    labelColor: () => colors.muted,
    style: {
      borderRadius: 16,
    },
    barPercentage: 0.7,
  };

  const screenWidth = Dimensions.get("window").width - 32;

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: colors.card, borderColor: colors.border },
      ]}
    >
      <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
      <BarChart
        data={chartData}
        width={screenWidth}
        height={180}
        chartConfig={chartConfig}
        style={styles.chart}
        withInnerLines={false}
        showValuesOnTopOfBars={true}
        fromZero={true}
        showBarTops={true}
        yAxisLabel=""
        yAxisSuffix=""
      />
    </View>
  );
};

const styles = StyleSheet.create({


  chart: {
    borderRadius: 16,
    marginVertical: 8,
  },
  container: {
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 16,
    marginHorizontal: 16,
    padding: 16,
  },
  placeholderContainer: {
    alignItems: "center",
    height: 180,
    justifyContent: "center",
  },
  placeholderText: {
    fontSize: 14,
    paddingHorizontal: 20,
    textAlign: "center",
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 16,
  },


});
