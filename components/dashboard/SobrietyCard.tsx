import React, { memo, useMemo, useCallback, useState, useEffect, useRef } from "react";
import {
  StyleSheet,
  Text,
  View,
  Animated,
  Easing,
  TouchableOpacity,
  Platform,
  Dimensions,
  Share
} from 'react-native';
import {
  Calendar,
  Trophy,
  Flame,
  DollarSign,
  Share2,
  <PERSON>rk<PERSON>,
  Target,
  Heart,
  Award
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ColorScheme } from "@/hooks/ui/useColorScheme";
import * as Haptics from 'expo-haptics';
import { calculateSavings } from '@/components/progress/utils';

// Enhanced interfaces
interface Milestone {
  days: number;
  name: string;
  icon?: string;
  color?: string;
  achieved?: boolean;
  progress?: number;
}

interface TimeBreakdown {
  years: number;
  months: number;
  weeks: number;
  days: number;
}

interface SobrietyCardProps {
  diffDays: number;
  sobrietyDate: Date;
  nextMilestone: Milestone;
  language: string;
  colors: ColorScheme;
  profile?: {
    usageCost?: number;
    costFrequency?: string;
    addiction?: string;
  };
  onPress?: () => void;
  onShare?: () => void;
  showSavings?: boolean;
  showTimeBreakdown?: boolean;
  enableCelebration?: boolean;
}
// Utility functions
const calculateTimeBreakdown = (days: number): TimeBreakdown => {
  const years = Math.floor(days / 365);
  const remainingAfterYears = days % 365;
  const months = Math.floor(remainingAfterYears / 30);
  const remainingAfterMonths = remainingAfterYears % 30;
  const weeks = Math.floor(remainingAfterMonths / 7);
  const remainingDays = remainingAfterMonths % 7;

  return {
    years,
    months,
    weeks,
    days: remainingDays
  };
};

const getMilestoneProgress = (currentDays: number, milestone: Milestone): number => {
  if (currentDays >= milestone.days) return 1;
  return Math.min(currentDays / milestone.days, 1);
};

const getStreakLevel = (days: number): { level: string; color: string; icon: React.ComponentType<{ size?: number; color?: string; strokeWidth?: number }> } => {
  if (days >= 365) return { level: 'legendary', color: '#FFD700', icon: Award };
  if (days >= 180) return { level: 'epic', color: '#9C27B0', icon: Trophy };
  if (days >= 90) return { level: 'hot', color: '#FF5722', icon: Flame };
  if (days >= 30) return { level: 'warm', color: '#FF9800', icon: Target };
  if (days >= 7) return { level: 'building', color: '#4CAF50', icon: Heart };
  return { level: 'starting', color: '#2196F3', icon: Calendar };
};

const SobrietyCardComponent: React.FC<SobrietyCardProps> = ({
  diffDays,
  sobrietyDate,
  nextMilestone,
  language,
  colors,
  profile,
  onPress,
  onShare,
  showSavings = true,
  showTimeBreakdown = true,
  enableCelebration = true,
}) => {
  // State
  const [showDetails, setShowDetails] = useState(false);
  const [celebrationTriggered, setCelebrationTriggered] = useState(false);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const countAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const sparkleAnim = useRef(new Animated.Value(0)).current;

  // Memoized calculations
  const timeBreakdown = useMemo(() => calculateTimeBreakdown(diffDays), [diffDays]);

  const milestoneProgress = useMemo(() =>
    getMilestoneProgress(diffDays, nextMilestone),
    [diffDays, nextMilestone]
  );

  const streakLevel = useMemo(() => getStreakLevel(diffDays), [diffDays]);

  const savedAmount = useMemo(() =>
    profile ? calculateSavings(profile, diffDays) : 0,
    [profile, diffDays]
  );

  const daysUntilNextMilestone = useMemo(() =>
    Math.max(0, nextMilestone.days - diffDays),
    [nextMilestone.days, diffDays]
  );

  // Animated count value
  const animatedCount = countAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, diffDays]
  });

  // Format the date with better localization
  const formattedDate = useMemo(() =>
    sobrietyDate.toLocaleDateString(
      language === 'nl' ? 'nl-NL' : 'en-US',
      { year: 'numeric', month: 'long', day: 'numeric' }
    ),
    [sobrietyDate, language]
  );
  // Celebration effect
  const triggerCelebration = useCallback(() => {
    if (!enableCelebration || celebrationTriggered) return;

    setCelebrationTriggered(true);

    // Haptic feedback
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }

    // Sparkle animation
    Animated.sequence([
      Animated.timing(sparkleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.timing(sparkleAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
        easing: Easing.in(Easing.cubic),
      }),
    ]).start(() => setCelebrationTriggered(false));
  }, [enableCelebration, celebrationTriggered, sparkleAnim]);

  // Start animations when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.timing(countAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: false,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.timing(progressAnim, {
        toValue: milestoneProgress,
        duration: 2000,
        useNativeDriver: false,
        easing: Easing.out(Easing.cubic),
      }),
    ]).start();

    // Trigger celebration for milestone achievements
    if (milestoneProgress >= 1 && enableCelebration) {
      setTimeout(triggerCelebration, 1000);
    }
  }, [fadeAnim, scaleAnim, countAnim, progressAnim, milestoneProgress, enableCelebration, triggerCelebration]);

  // Handle card press
  const handleCardPress = useCallback(() => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    setShowDetails(!showDetails);
    onPress?.();
  }, [showDetails, onPress]);

  // Handle share
  const handleShare = useCallback(async () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    const message = language === 'nl'
      ? `Ik ben al ${diffDays} dagen nuchter! 🎉 #SobrixHealth #Recovery`
      : `I've been sober for ${diffDays} days! 🎉 #SobrixHealth #Recovery`;

    try {
      await Share.share({
        message,
        title: language === 'nl' ? 'Mijn Nuchterheid' : 'My Sobriety Journey'
      });
      onShare?.();
    } catch (error) {
      console.error('Error sharing:', error);
    }
  }, [diffDays, language, onShare]);

  // Format time breakdown text
  const getTimeBreakdownText = useCallback(() => {
    const parts = [];
    if (timeBreakdown.years > 0) {
      parts.push(`${timeBreakdown.years} ${language === 'nl' ? 'jaar' : 'year'}${timeBreakdown.years !== 1 ? (language === 'nl' ? '' : 's') : ''}`);
    }
    if (timeBreakdown.months > 0) {
      parts.push(`${timeBreakdown.months} ${language === 'nl' ? 'maand' : 'month'}${timeBreakdown.months !== 1 ? (language === 'nl' ? 'en' : 's') : ''}`);
    }
    if (timeBreakdown.weeks > 0) {
      parts.push(`${timeBreakdown.weeks} ${language === 'nl' ? 'week' : 'week'}${timeBreakdown.weeks !== 1 ? (language === 'nl' ? '' : 's') : ''}`);
    }
    if (timeBreakdown.days > 0 || parts.length === 0) {
      parts.push(`${timeBreakdown.days} ${language === 'nl' ? 'dag' : 'day'}${timeBreakdown.days !== 1 ? (language === 'nl' ? 'en' : 's') : ''}`);
    }
    return parts.slice(0, 2).join(', ');
  }, [timeBreakdown, language]);

  // Get screen dimensions for responsive design
  const screenWidth = Dimensions.get('window').width;
  const isTablet = screenWidth > 768;

  return (
    <TouchableOpacity
      activeOpacity={0.95}
      onPress={handleCardPress}
      onLongPress={handleShare}
      accessible={true}
      accessibilityRole="button"
      accessibilityLabel={`${language === 'nl' ? 'Nuchterheidskaart' : 'Sobriety card'}: ${diffDays} ${language === 'nl' ? 'dagen nuchter' : 'days sober'}`}
      accessibilityHint={language === 'nl' ? 'Tik voor details, houd ingedrukt om te delen' : 'Tap for details, long press to share'}
    >
      <Animated.View style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }]
        },
        isTablet && styles.tabletContainer
      ]}>
        {/* Celebration sparkles */}
        {enableCelebration && (
          <Animated.View
            style={[
              styles.sparkleContainer,
              {
                opacity: sparkleAnim,
                transform: [
                  {
                    scale: sparkleAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.5, 1.2]
                    })
                  }
                ]
              }
            ]}
            pointerEvents="none"
          >
            <Sparkles size={24} color="#FFD700" />
            <Sparkles size={16} color="#FFF" style={styles.sparkle2} />
            <Sparkles size={20} color="#FFD700" style={styles.sparkle3} />
          </Animated.View>
        )}

        <LinearGradient
          colors={[
            streakLevel.color,
            colors.primary,
            colors.primaryDark || colors.accent
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.sobrietyCard, isTablet && styles.tabletCard]}
        >
          {/* Main content */}
          <View style={styles.sobrietyContent}>
            <View style={styles.sobrietyTextContainer}>
              <View style={styles.labelContainer}>
                <Text style={styles.sobrietyLabel}>
                  {language === 'nl' ? 'Dagen nuchter' : 'Days Sober'}
                </Text>
                <View style={[styles.streakBadge, { backgroundColor: streakLevel.color }]}>
                  <streakLevel.icon size={12} color="#fff" />
                  <Text style={styles.streakBadgeText}>
                    {language === 'nl' ?
                      (streakLevel.level === 'legendary' ? 'Legendarisch' :
                       streakLevel.level === 'epic' ? 'Episch' :
                       streakLevel.level === 'hot' ? 'Heet' :
                       streakLevel.level === 'warm' ? 'Warm' :
                       streakLevel.level === 'building' ? 'Opbouwend' : 'Beginnend') :
                      streakLevel.level}
                  </Text>
                </View>
              </View>

              <Animated.Text style={[styles.sobrietyCount, isTablet && styles.tabletCount]}>
                {animatedCount.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, diffDays].map(Math.round),
                  extrapolate: 'clamp'
                })}
              </Animated.Text>

              {showTimeBreakdown && diffDays > 7 && (
                <Text style={styles.timeBreakdown}>
                  {getTimeBreakdownText()}
                </Text>
              )}

              <Text style={styles.sobrietyDate}>
                {language === 'nl' ? 'Sinds' : 'Since'} {formattedDate}
              </Text>
            </View>

            <View style={styles.sobrietyIconContainer}>
              <View style={styles.iconWrapper}>
                <Calendar size={isTablet ? 56 : 48} color="#fff" strokeWidth={1.5} />
                {diffDays > 0 && (
                  <View style={styles.daysBadge}>
                    <Text style={styles.daysBadgeText}>{diffDays}</Text>
                  </View>
                )}
              </View>
            </View>
          </View>

          {/* Milestone Progress */}
          <View style={styles.milestoneContainer}>
            <View style={styles.milestoneHeader}>
              <View style={styles.milestoneIconContainer}>
                <Trophy size={20} color="rgba(255, 255, 255, 0.9)" />
              </View>
              <View style={styles.milestoneTextContainer}>
                <Text style={styles.milestoneText}>
                  {daysUntilNextMilestone > 0 ? (
                    language === 'nl'
                      ? `${daysUntilNextMilestone} dagen tot ${nextMilestone.name}`
                      : `${daysUntilNextMilestone} days until ${nextMilestone.name}`
                  ) : (
                    language === 'nl'
                      ? `${nextMilestone.name} behaald!`
                      : `${nextMilestone.name} achieved!`
                  )}
                </Text>
              </View>
            </View>

            {/* Progress Bar */}
            <View style={styles.progressBarContainer}>
              <View style={styles.progressBarBackground}>
                <Animated.View
                  style={[
                    styles.progressBarFill,
                    {
                      width: progressAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0%', '100%'],
                        extrapolate: 'clamp'
                      })
                    }
                  ]}
                />
              </View>
              <Text style={styles.progressText}>
                {Math.round(milestoneProgress * 100)}%
              </Text>
            </View>
          </View>

          {/* Additional Info Row */}
          <View style={styles.infoRow}>
            {/* Savings */}
            {showSavings && savedAmount > 0 && (
              <TouchableOpacity style={styles.infoCard} activeOpacity={0.7}>
                <DollarSign size={16} color="rgba(255, 255, 255, 0.9)" />
                <Text style={styles.infoCardText}>
                  {language === 'nl' ? 'Bespaard' : 'Saved'}
                </Text>
                <Text style={styles.infoCardValue}>
                  €{savedAmount.toFixed(0)}
                </Text>
              </TouchableOpacity>
            )}

            {/* Streak Level */}
            <TouchableOpacity style={styles.infoCard} activeOpacity={0.7}>
              <streakLevel.icon size={16} color="rgba(255, 255, 255, 0.9)" />
              <Text style={styles.infoCardText}>
                {language === 'nl' ? 'Niveau' : 'Level'}
              </Text>
              <Text style={styles.infoCardValue}>
                {language === 'nl' ?
                  (streakLevel.level === 'legendary' ? 'Legendarisch' :
                   streakLevel.level === 'epic' ? 'Episch' :
                   streakLevel.level === 'hot' ? 'Heet' :
                   streakLevel.level === 'warm' ? 'Warm' :
                   streakLevel.level === 'building' ? 'Opbouwend' : 'Beginnend') :
                  streakLevel.level}
              </Text>
            </TouchableOpacity>

            {/* Share Button */}
            <TouchableOpacity
              style={styles.shareButton}
              onPress={handleShare}
              activeOpacity={0.7}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel={language === 'nl' ? 'Deel vooruitgang' : 'Share progress'}
            >
              <Share2 size={16} color="rgba(255, 255, 255, 0.9)" />
            </TouchableOpacity>
          </View>

          {/* Detailed View (Expandable) */}
          {showDetails && (
            <Animated.View style={styles.detailsContainer}>
              <View style={styles.detailsGrid}>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>
                    {language === 'nl' ? 'Totaal uren' : 'Total hours'}
                  </Text>
                  <Text style={styles.detailValue}>
                    {(diffDays * 24).toLocaleString()}
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>
                    {language === 'nl' ? 'Totaal minuten' : 'Total minutes'}
                  </Text>
                  <Text style={styles.detailValue}>
                    {(diffDays * 24 * 60).toLocaleString()}
                  </Text>
                </View>
                {profile?.addiction && (
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>
                      {language === 'nl' ? 'Vrij van' : 'Free from'}
                    </Text>
                    <Text style={styles.detailValue}>
                      {profile.addiction}
                    </Text>
                  </View>
                )}
              </View>
            </Animated.View>
          )}
        </LinearGradient>
      </Animated.View>
    </TouchableOpacity>
  );
};

// Memoized component with display name
SobrietyCardComponent.displayName = 'SobrietyCard';
export const SobrietyCard = memo(SobrietyCardComponent);

const styles = StyleSheet.create({
  container: {
    elevation: 8,
    marginHorizontal: 20,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  daysBadge: {
    alignItems: 'center',
    backgroundColor: '#FF6B6B',
    borderRadius: 12,
    height: 24,
    justifyContent: 'center',
    minWidth: 24,
    paddingHorizontal: 6,
    position: 'absolute',
    right: -8,
    top: -8,
  },
  daysBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  detailItem: {
    alignItems: 'center',
    flex: 1,
    minWidth: '30%',
  },
  detailLabel: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 10,
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  detailValue: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '700',
    textAlign: 'center',
  },
  detailsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    marginTop: 16,
    padding: 12,
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  iconWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  infoCard: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    flex: 1,
    gap: 4,
    padding: 10,
  },
  infoCardText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 10,
    fontWeight: '500',
  },
  infoCardValue: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '700',
  },
  infoRow: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    marginTop: 12,
  },
  labelContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  milestoneContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 14,
    marginTop: 16,
    padding: 12,
  },
  milestoneHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 8,
  },
  milestoneIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 10,
    marginRight: 10,
    padding: 8,
  },
  milestoneText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  milestoneTextContainer: {
    flex: 1,
  },
  progressBarBackground: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 3,
    flex: 1,
    height: 6,
    overflow: 'hidden',
  },
  progressBarContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 10,
  },
  progressBarFill: {
    backgroundColor: '#4CAF50',
    borderRadius: 3,
    height: '100%',
  },
  progressText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    minWidth: 35,
    textAlign: 'right',
  },
  shareButton: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    justifyContent: 'center',
    padding: 12,
  },
  sobrietyCard: {
    borderRadius: 20,
    overflow: 'hidden',
    padding: 20,
  },
  sobrietyContent: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sobrietyCount: {
    color: '#fff',
    fontSize: 52,
    fontWeight: 'bold',
    marginBottom: 4,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  sobrietyDate: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
  sobrietyIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 18,
    padding: 14,
  },
  sobrietyLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontWeight: '500',
  },
  sobrietyTextContainer: {
    flex: 1,
  },
  sparkle2: {
    left: 15,
    position: 'absolute',
    top: -5,
  },
  sparkle3: {
    left: -10,
    position: 'absolute',
    top: 10,
  },
  sparkleContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    position: 'absolute',
    right: 10,
    top: 10,
    zIndex: 10,
  },
  streakBadge: {
    alignItems: 'center',
    borderRadius: 12,
    flexDirection: 'row',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  streakBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  tabletCard: {
    borderRadius: 24,
    padding: 30,
  },
  tabletContainer: {
    marginHorizontal: 40,
    marginVertical: 15,
  },
  tabletCount: {
    fontSize: 64,
  },
  timeBreakdown: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
});
  tabletCard: {
    borderRadius: 24,
    padding: 30,
  },
  sobrietyContent: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sobrietyTextContainer: {
    flex: 1,
  },
  labelContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  sobrietyLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontWeight: '500',
  },
  streakBadge: {
    alignItems: 'center',
    borderRadius: 12,
    flexDirection: 'row',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  streakBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  sobrietyCount: {
    color: '#fff',
    fontSize: 52,
    fontWeight: 'bold',
    marginBottom: 4,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  tabletCount: {
    fontSize: 64,
  },
  timeBreakdown: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  sobrietyDate: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
  sobrietyIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 18,
    padding: 14,
  },
  iconWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  daysBadge: {
    alignItems: 'center',
    backgroundColor: '#FF6B6B',
    borderRadius: 12,
    height: 24,
    justifyContent: 'center',
    minWidth: 24,
    paddingHorizontal: 6,
    position: 'absolute',
    right: -8,
    top: -8,
  },
  daysBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  milestoneContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 14,
    marginTop: 16,
    padding: 12,
  },
  milestoneHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 8,
  },
  milestoneIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 10,
    marginRight: 10,
    padding: 8,
  },
  milestoneTextContainer: {
    flex: 1,
  },
  milestoneText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  progressBarContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 10,
  },
  progressBarBackground: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 3,
    flex: 1,
    height: 6,
    overflow: 'hidden',
  },
  progressBarFill: {
    backgroundColor: '#4CAF50',
    borderRadius: 3,
    height: '100%',
  },
  progressText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    minWidth: 35,
    textAlign: 'right',
  },
  infoRow: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    marginTop: 12,
  },
  infoCard: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    flex: 1,
    gap: 4,
    padding: 10,
  },
  infoCardText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 10,
    fontWeight: '500',
  },
  infoCardValue: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '700',
  },
  shareButton: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    justifyContent: 'center',
    padding: 12,
  },
  detailsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    marginTop: 16,
    padding: 12,
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  detailItem: {
    alignItems: 'center',
    flex: 1,
    minWidth: '30%',
  },
  detailLabel: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 10,
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  detailValue: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '700',
    textAlign: 'center',
  },
});