import React from "react";
import { useEffect, useRef } from 'react';
import { StyleSheet, Text, View, Animated, Easing, TouchableOpacity } from 'react-native';
import { Calendar, Trophy, Clock } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ColorScheme } from "@/hooks/ui/useColorScheme";
interface SobrietyCardProps {
  diffDays: number;
  sobrietyDate: Date;
  nextMilestone: { days: number; name: string };
  language: string;
  colors: ColorScheme;
}
export const SobrietyCard: React.FC<SobrietyCardProps> = ({
  diffDays,
  sobrietyDate,
  nextMilestone,
  language,
  colors,
}) => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const countAnim = useRef(new Animated.Value(0)).current;
  
  // Calculate days until next milestone
  const daysUntilNextMilestone = nextMilestone.days - diffDays;
  
  // Animated count value
  const animatedCount = countAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, diffDays]
  });
  
  // Start animations when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.timing(countAnim, {
        toValue: diffDays,
        duration: 1500,
        useNativeDriver: false,
        easing: Easing.out(Easing.cubic),
      }),
    ]).start();
  }, [fadeAnim, scaleAnim, countAnim, diffDays]);
  
  // Format the date
  const formattedDate = sobrietyDate.toLocaleDateString(
    language === 'nl' ? 'nl-NL' : 'en-US', 
    { year: 'numeric', month: 'long', day: 'numeric' }
  );
  
  return (
    <Animated.View style={[
      styles.container,
      {
        opacity: fadeAnim,
        transform: [{ scale: scaleAnim }]
      }
    ]}>
      <LinearGradient
        colors={[colors.primary, colors.primaryDark || colors.accent]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.sobrietyCard}
      >
        <View style={styles.sobrietyContent}>
          <View style={styles.sobrietyTextContainer}>
            <Text style={styles.sobrietyLabel}>
              {language === 'nl' ? 'Dagen nuchter' : 'Days Sober'}
            </Text>
            <Animated.Text style={styles.sobrietyCount}>
              {animatedCount.interpolate({
                inputRange: [0, diffDays],
                outputRange: [0, diffDays].map(Math.round),
                extrapolate: 'clamp'
              })}
            </Animated.Text>
            <Text style={styles.sobrietyDate}>
              {language === 'nl' ? 'Sinds' : 'Since'} {formattedDate}
            </Text>
          </View>
          <View style={styles.sobrietyIconContainer}>
            <Calendar size={48} color="#fff" strokeWidth={1.5} />
          </View>
        </View>
        
        <View style={styles.milestoneContainer}>
          <View style={styles.milestoneIconContainer}>
            <Trophy size={20} color="rgba(255, 255, 255, 0.9)" />
          </View>
          <View style={styles.milestoneTextContainer}>
            <Text style={styles.milestoneText}>
              {language === 'nl' 
                ? `${daysUntilNextMilestone > 0 ? daysUntilNextMilestone : 0} dagen tot ${nextMilestone.name}` 
                : `${daysUntilNextMilestone > 0 ? daysUntilNextMilestone : 0} days until ${nextMilestone.name}`}
            </Text>
          </View>
        </View>
        
        <TouchableOpacity style={styles.streakContainer}>
          <View style={styles.streakIconContainer}>
            <Clock size={18} color="rgba(255, 255, 255, 0.9)" />
          </View>
          <Text style={styles.streakText}>
            {language === 'nl' 
              ? `Langste streak: ${diffDays} dagen` 
              : `Longest streak: ${diffDays} days`}
          </Text>
        </TouchableOpacity>
      </LinearGradient>
    </Animated.View>
  );
};
const styles = StyleSheet.create({


  container: {
    elevation: 8,
    marginHorizontal: 20,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  milestoneContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 14,
    flexDirection: 'row',
    marginTop: 16,
    padding: 12,
  },
  milestoneIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 10,
    marginRight: 10,
    padding: 8,
  },
  milestoneText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  milestoneTextContainer: {
    flex: 1,
  },
  sobrietyCard: {
    borderRadius: 20,
    overflow: 'hidden',
    padding: 20,
  },
  sobrietyContent: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sobrietyCount: {
    color: '#fff',
    fontSize: 52,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  sobrietyDate: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginTop: 4,
  },
  sobrietyIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 18,
    padding: 14,
  },
  sobrietyLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  sobrietyTextContainer: {
    flex: 1,
  },
  streakContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 14,
    flexDirection: 'row',
    marginTop: 12,
    padding: 10,
  },
  streakIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    marginRight: 10,
    padding: 6,
  },
  streakText: {
    color: '#fff',
    fontSize: 13,
    fontWeight: '500',
  },


});