import React, { useRef, useEffect } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Animated, Easing } from 'react-native';
import { Calendar, TrendingUp, TrendingDown, Minus } from 'lucide-react-native';
import { useTranslation } from '@/hooks/useTranslation';

interface DayProgress {
  date: string;
  dayName?: string;
  mood: number | null;
  cravingIntensity: number;
  completed: boolean;
  goalsCompleted?: number;
  goalsTracked?: number;
}

interface WeeklyProgressCardProps {
  weekData: DayProgress[];
  language: string;
  colors: {
    card: string;
    border: string;
    text: string;
    muted: string;
    primary: string;
    success: string;
    danger: string;
  };
  onPress?: () => void;
  isCompact?: boolean;
}

export const WeeklyProgressCard: React.FC<WeeklyProgressCardProps> = ({
  weekData,
  language: _language, // Keep for backward compatibility but don't use
  colors,
  onPress,
  isCompact = false,
}) => {
  const { t } = useTranslation();
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const barAnimations = useRef(
    weekData.map(() => new Animated.Value(0))
  ).current;

  // Calculate completed days
  const completedDays = weekData.filter(day => day.completed).length;

  // Get day name from date
  const getDayName = (dateStr: string) => {
    const date = new Date(dateStr);
    const dayNames = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
    return dayNames[date.getDay()];
  };

  // Get color based on mood and craving
  const getProgressColor = (mood: number | null, cravingIntensity: number) => {
    if (!mood) return colors.muted;
    return mood >= 4 && cravingIntensity <= 2 ? colors.success : colors.danger;
  };

  // Calculate weekly trend
  const calculateWeeklyTrend = () => {
    const firstHalf = weekData.slice(0, 3).filter(d => d.completed);
    const secondHalf = weekData.slice(4, 7).filter(d => d.completed);
    
    const firstHalfAvg = firstHalf.length > 0 
      ? firstHalf.reduce((sum, d) => sum + (d.mood || 3), 0) / firstHalf.length 
      : 3;
    const secondHalfAvg = secondHalf.length > 0 
      ? secondHalf.reduce((sum, d) => sum + (d.mood || 3), 0) / secondHalf.length 
      : 3;
    
    const diff = secondHalfAvg - firstHalfAvg;
    return diff > 0.5 ? 'up' : diff < -0.5 ? 'down' : 'stable';
  };

  const trend = calculateWeeklyTrend();

  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return <TrendingUp size={16} color={colors.success} />;
      case 'down': return <TrendingDown size={16} color={colors.danger} />;
      default: return <Minus size={16} color={colors.muted} />;
    }
  };

  const getTrendText = () => {
    switch (trend) {
      case 'up': return '+12%';
      case 'down': return '-8%';
      default: return '0%';
    }
  };

  // Start animations when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 600,
        easing: Easing.out(Easing.back(1.1)),
        useNativeDriver: true
      }),
      Animated.stagger(100, 
        barAnimations.map(anim => 
          Animated.timing(anim, {
            toValue: 1,
            duration: 800,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: false
          })
        )
      )
    ]).start();
  }, [fadeAnim, scaleAnim, barAnimations]);

  if (isCompact) {
    return (
      <Animated.View style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }]
        }
      ]}>
        <TouchableOpacity
          style={[styles.card, styles.compactCard, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={onPress}
          activeOpacity={0.8}
        >
          <View style={styles.compactHeader}>
            <View style={styles.compactTitleContainer}>
              <Calendar size={20} color={colors.primary} />
              <Text style={[styles.compactTitle, { color: colors.text }]}>
                {t('dashboard.weeklyProgress.thisWeek')}
              </Text>
            </View>
            <View style={styles.compactStats}>
              <Text style={[styles.compactValue, { color: colors.text }]}>
                {completedDays}/7
              </Text>
              <View style={styles.compactTrend}>
                {getTrendIcon()}
              </View>
            </View>
          </View>
          <Text style={[styles.compactSubtitle, { color: colors.muted }]}>
            {t('dashboard.weeklyProgress.tapToExpand')}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={[
      styles.container,
      {
        opacity: fadeAnim,
        transform: [{ scale: scaleAnim }]
      }
    ]}>
      <TouchableOpacity
        style={[styles.card, { backgroundColor: colors.card, borderColor: colors.border }]}
        onPress={onPress}
        activeOpacity={0.8}
      >
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Calendar size={20} color={colors.primary} />
            <Text style={[styles.title, { color: colors.text }]}>
              {t('dashboard.weeklyProgress.thisWeek')}
            </Text>
          </View>
          <View style={styles.trendContainer}>
            {getTrendIcon()}
            <Text style={[styles.trendText, { color: colors.muted }]}>
              {getTrendText()}
            </Text>
          </View>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.text }]}>
              {completedDays}/7
            </Text>
            <Text style={[styles.statLabel, { color: colors.muted }]}>
              {t('dashboard.weeklyProgress.days')}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.text }]}>
              {Math.round((completedDays / 7) * 100)}%
            </Text>
            <Text style={[styles.statLabel, { color: colors.muted }]}>
              {t('dashboard.weeklyProgress.complete')}
            </Text>
          </View>
        </View>

        <View style={styles.chartContainer}>
          {weekData.map((day, index) => {
            const height = barAnimations[index].interpolate({
              inputRange: [0, 1],
              outputRange: [4, Math.max(4, ((day.mood ?? 3) / 5) * 40)]
            });

            return (
              <View key={day.date} style={styles.dayContainer}>
                <View style={styles.barContainer}>
                  <Animated.View
                    style={[
                      styles.bar,
                      {
                        height,
                        backgroundColor: day.completed 
                          ? getProgressColor(day.mood, day.cravingIntensity)
                          : colors.border
                      }
                    ]}
                  />
                </View>
                <Text style={[styles.dayLabel, { color: colors.muted }]}>
                  {getDayName(day.date)}
                </Text>
              </View>
            );
          })}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  bar: {
    borderRadius: 4,
    minHeight: 4,
    width: 8,
  },
  barContainer: {
    height: 44,
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  card: {
    borderRadius: 16,
    borderWidth: 1,
    elevation: 4,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  chartContainer: {
    alignItems: 'flex-end',
    flexDirection: 'row',
    height: 60,
    justifyContent: 'space-between',
  },
  compactCard: {
    minHeight: 60,
    padding: 12,
  },
  compactHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  compactStats: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  compactSubtitle: {
    fontSize: 11,
    fontStyle: 'italic',
  },
  compactTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  compactTitleContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  compactTrend: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  compactValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  container: {
    marginBottom: 16,
    marginHorizontal: 16,
  },
  dayContainer: {
    alignItems: 'center',
    flex: 1,
  },
  dayLabel: {
    fontSize: 11,
    fontWeight: '500',
  },
  header: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 2,
  },
  statsContainer: {
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 12,
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingVertical: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  titleContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  trendContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '500',
  },
});