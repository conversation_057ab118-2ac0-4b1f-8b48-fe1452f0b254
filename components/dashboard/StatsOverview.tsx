import React, { useRef, useEffect } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Animated, Easing } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Calendar, AlertTriangle } from 'lucide-react-native';
import { UserProfile } from '@/types/user';
import Colors from '@/constants/colors';
import { getSubstanceIcon } from '@/utils/ui/iconUtils';
import { useFadeScaleAnimation } from '@/hooks/animation';
import { useTranslation } from '@/hooks/useTranslation';

interface StatsOverviewProps {
  profile: UserProfile;
  diffDays: number;
  language: string;
  colors: typeof Colors.light;
}

export const StatsOverview: React.FC<StatsOverviewProps> = ({ profile, diffDays, language: _language, colors }) => {
  const { t } = useTranslation();
  
  // Animation values for counter animations
  const daysAnim = useRef(new Animated.Value(0)).current;
  const savingsAnim = useRef(new Animated.Value(0)).current;
  const relapsesAnim = useRef(new Animated.Value(0)).current;

  // Use shared fade scale animation for container
  const { animatedStyle } = useFadeScaleAnimation({
    duration: 800,
  });

  // Calculate money saved
  const calculateSavings = () => {
    if (!profile.usageCost || !profile.costFrequency) return 0;
    
    let dailyCost = 0;
    
    switch (profile.costFrequency) {
      case 'daily':
        dailyCost = profile.usageCost;
        break;
      case 'weekly':
        dailyCost = profile.usageCost / 7;
        break;
      case 'monthly':
        dailyCost = profile.usageCost / 30;
        break;
      case 'yearly':
        dailyCost = profile.usageCost / 365;
        break;
      default:
        dailyCost = 0;
    }
    
    return dailyCost * diffDays;
  };
  
  const savings = calculateSavings();
  const currency = profile.currency === 'EUR' ? '€' : '$';
  const relapseCount = profile.relapses?.length || 0;
  
  // Start counter animations when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(daysAnim, {
        toValue: 1,
        duration: 1200,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: false
      }),
      Animated.timing(savingsAnim, {
        toValue: 1,
        duration: 1500,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: false
      }),
      Animated.timing(relapsesAnim, {
        toValue: 1,
        duration: 1000,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: false
      })
    ]).start();
  }, [diffDays, savings, relapseCount, daysAnim, savingsAnim, relapsesAnim]);

  return (
    <Animated.View style={[
      styles.container,
      animatedStyle,
      {
        backgroundColor: colors.card,
        borderColor: colors.border
      }
    ]}>
      <LinearGradient
        colors={[colors.card, colors.cardGradient || colors.card] as [string, string]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <TouchableOpacity style={styles.statItem} activeOpacity={0.7}>
          <View style={[styles.iconContainer, styles.daysIconBackground]}>
            <Calendar size={20} color={colors.primary} />
          </View>
          <Animated.Text style={[styles.statValue, { color: colors.text }]}>
            {daysAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0, diffDays].map(Math.round),
              extrapolate: 'clamp'
            })}
          </Animated.Text>
          <Text style={[styles.statLabel, { color: colors.muted }]}>
            {t('dashboard.stats.days')}
          </Text>
        </TouchableOpacity>
        
        <View style={[styles.divider, { backgroundColor: colors.border }]} />
        
        <TouchableOpacity style={styles.statItem} activeOpacity={0.7}>
          <View style={[styles.iconContainer, styles.savingsIconBackground]}>
            {getSubstanceIcon(profile.substanceType, {
              size: 20,
              color: colors.success,
              strokeWidth: 2,
            })}
          </View>
          <Text style={[styles.statValue, { color: colors.text }]}>
            {currency}{Math.round(savings)}
          </Text>
          <Text style={[styles.statLabel, { color: colors.muted }]}>
            {t('dashboard.stats.saved')}
          </Text>
          {profile.usageAmount && profile.usageAmount !== 'Not specified' && (
            <Text style={[styles.usageAmount, { color: colors.muted }]}>
              {profile.usageAmount}
            </Text>
          )}
        </TouchableOpacity>
        
        <View style={[styles.divider, { backgroundColor: colors.border }]} />
        
        <TouchableOpacity style={styles.statItem} activeOpacity={0.7}>
          <View style={[styles.iconContainer, styles.relapsesIconBackground]}>
            <AlertTriangle size={20} color={colors.danger} />
          </View>
          <Animated.Text style={[styles.statValue, { color: colors.text }]}>
            {relapsesAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0, relapseCount].map(Math.round),
              extrapolate: 'clamp'
            })}
          </Animated.Text>
          <Text style={[styles.statLabel, { color: colors.muted }]}>
            {t('dashboard.stats.relapses')}
          </Text>
        </TouchableOpacity>
      </LinearGradient>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    borderWidth: 1,
    elevation: 4,
    marginBottom: 20,
    marginHorizontal: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  daysIconBackground: {
    backgroundColor: 'rgba(52, 152, 219, 0.1)',
  },
  divider: {
    marginHorizontal: 8,
    width: 1,
  },
  gradient: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  iconContainer: {
    borderRadius: 12,
    marginBottom: 8,
    padding: 8,
  },
  relapsesIconBackground: {
    backgroundColor: 'rgba(231, 76, 60, 0.1)',
  },
  savingsIconBackground: {
    backgroundColor: 'rgba(39, 174, 96, 0.1)',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  usageAmount: {
    fontSize: 10,
    fontStyle: 'italic',
    marginTop: 2,
  },
});