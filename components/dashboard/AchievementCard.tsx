import React from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
} from "react-native";
import { Ionicons } from '@expo/vector-icons';
import { ColorScheme } from '@/hooks/ui/useColorScheme';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  isUnlocked: boolean;
  unlockedAt?: string;
}



interface AchievementCardProps {
  colors: ColorScheme;
  language: string;
  achievement: Achievement;
  onPress?: () => void;
}

export const AchievementCard: React.FC<AchievementCardProps> = ({
  colors,
  language,
  achievement,
  onPress,
}) => {

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: colors.card,
          borderColor: colors.border,
        },
        achievement.isUnlocked ? styles.unlocked : styles.locked,
      ]}
      onPress={onPress}
      disabled={!achievement.isUnlocked}
    >
      <View style={styles.iconContainer}>
        <Ionicons
          name={achievement.icon}
          size={24}
          color={achievement.isUnlocked ? colors.primary : colors.muted}
        />
      </View>

      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>
          {achievement.title}
        </Text>
        <Text style={[styles.description, { color: colors.muted }]}>
          {achievement.description}
        </Text>

        {achievement.isUnlocked && achievement.unlockedAt && (
          <Text style={[styles.unlockedDate, { color: colors.success }]}>
            {language === 'nl' ? 'Behaald op' : 'Unlocked on'}{' '}
            {new Date(achievement.unlockedAt).toLocaleDateString()}
          </Text>
        )}
      </View>

      {achievement.isUnlocked && (
        <View style={styles.checkmark}>
          <Ionicons name="checkmark-circle" size={20} color={colors.success} />
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  checkmark: {
    marginLeft: 8,
  },
  container: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 12,
    padding: 16,
  },
  content: {
    flex: 1,
    marginLeft: 12,
  },
  description: {
    fontSize: 14,
    lineHeight: 18,
  },
  iconContainer: {
    alignItems: 'center',
    height: 40,
    justifyContent: 'center',
    width: 40,
  },
  locked: {
    opacity: 0.6,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  unlocked: {
    opacity: 1,
  },
  unlockedDate: {
    fontSize: 12,
    marginTop: 4,
  },
});