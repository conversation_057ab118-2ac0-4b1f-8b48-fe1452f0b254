import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Platform,
  Animated,
} from "react-native";
import { useRouter, Href } from "expo-router";
import {
  Phone,
  Heart,
  Footprints,
} from "lucide-react-native";
import * as Haptics from "expo-haptics";
import { useFadeSlideAnimation, useStaggeredAnimation } from "@/hooks";
import { useCardBounceAnimation } from "@/hooks/animation/card-interactions";
import { useTranslation } from "@/hooks/useTranslation";

interface ButtonConfig {
  icon: React.ReactNode;
  text: string;
  route: Href;
  color: string;
}

interface Colors {
  danger: string;
  primary: string;
  info: string;
  secondary: string;
  text: string;
  card: string;
  border: string;
}

interface ActionButtonsProps {
  colors: Colors;
  language: string;
}

const AnimatedButton: React.FC<{
  button: ButtonConfig;
  onPress: () => void;
  colors: Colors;
}> = ({ button, onPress, colors }) => {
  const { animatedStyle, triggerBounce } = useCardBounceAnimation();

  const handlePress = () => {
    triggerBounce();
    setTimeout(() => onPress(), 100); // Slight delay for bounce effect
  };

  return (
    <Animated.View style={[styles.buttonWrapper, animatedStyle]}>
      <TouchableOpacity
        style={[
          styles.buttonContainer,
          { backgroundColor: colors.card, borderColor: colors.border }
        ]}
        onPress={handlePress}
        activeOpacity={0.8}
      >
        <View style={styles.button}>
          <View style={[
            styles.iconContainer,
            { backgroundColor: button.color + "15" }
          ]}>
            {button.icon}
          </View>
          <Text style={[styles.buttonText, { color: colors.text }]} numberOfLines={1}>
            {button.text}
          </Text>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  colors,
  language: _language, // Keep for backward compatibility but don't use
}) => {
  const router = useRouter();
  const { t } = useTranslation();
  
  const { animatedStyle } = useFadeSlideAnimation({
    duration: 800,
    slideDistance: 20,
  });

  const { items } = useStaggeredAnimation({
    itemCount: 3,
    staggerDelay: 50,
    duration: 400,
  });

  const handleButtonPress = (route: Href) => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.push(route);
  };

  const buttonConfigs: ButtonConfig[] = [
    {
      icon: <Phone size={28} strokeWidth={2.5} color="#FF8C42" />,
      text: t('dashboard.emergency'),
      route: "/(tabs)/contacts",
      color: "#FF8C42",
    },
    {
      icon: <Heart size={28} strokeWidth={2.5} color="#4ECDC4" />,
      text: t('dashboard.mindfulness'),
      route: "/(tabs)/mindfulness",
      color: "#4ECDC4",
    },
    {
      icon: <Footprints size={28} strokeWidth={2.5} color="#45B7D1" />,
      text: t('dashboard.progress'),
      route: "/(tabs)/progress",
      color: "#45B7D1",
    },
  ];

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        {t('dashboard.quickActions')}
      </Text>
      <View style={styles.buttonsContainer}>
        <View style={styles.buttonsRow}>
          {buttonConfigs.map((button, index) => (
            <Animated.View
              key={index}
              style={[items[index].animatedStyle, styles.buttonWrapper]}
            >
              <AnimatedButton
                button={button}
                onPress={() => handleButtonPress(button.route)}
                colors={colors}
              />
            </Animated.View>
          ))}
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  button: {
    alignItems: "center",
    borderRadius: 45,
    height: 90,
    justifyContent: "center",
    padding: 16,
    width: 90,
  },
  buttonContainer: {
    borderRadius: 45,
    borderWidth: 1,
    elevation: 2,
    height: 90,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    width: 90,
  },
  buttonText: {
    fontSize: 11,
    fontWeight: "600",
    marginTop: 6,
    textAlign: "center",
  },
  buttonWrapper: {
    alignItems: "center",
    flex: 1,
  },
  buttonsContainer: {
    paddingHorizontal: 20,
  },
  buttonsRow: {
    flexDirection: "row",
    gap: 12,
    justifyContent: "center",
    marginBottom: 12,
  },
  container: {
    marginBottom: 20,
  },
  iconContainer: {
    alignItems: "center",
    borderRadius: 20,
    height: 40,
    justifyContent: "center",
    width: 40,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 16,
    paddingHorizontal: 20,
  },
});