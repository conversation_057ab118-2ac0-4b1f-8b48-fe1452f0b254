import React from 'react';
import { StyleSheet, View, Text, Dimensions } from 'react-native';
import { LineChart } from 'react-native-chart-kit';

interface MoodEntry {
  id: string;
  date: string;
  mood: number;
  cravingIntensity: number;
  notes?: string;
}

interface ColorScheme {
  primary: string;
  danger: string;
  text: string;
  muted: string;
  card: string;
  border: string;
}

interface MoodTrendCardProps {
  moodEntries: MoodEntry[];
  language: string;
  colors: ColorScheme;
}
export const MoodTrendCard: React.FC<MoodTrendCardProps> = ({ moodEntries, language, colors }) => {
  // Get the last 7 entries or fewer if not enough data
  const recentEntries = [...moodEntries]
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 7)
    .reverse();
  
  // Prepare data for the chart
  const moodData = recentEntries.map(entry => entry.mood);
  const cravingData = recentEntries.map(entry => entry.cravingIntensity);
  
  // If no data, show placeholder
  if (recentEntries.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: colors.card, borderColor: colors.border }]}>
        <Text style={[styles.title, { color: colors.text }]}>
          {language === 'nl' ? 'Stemming & Verlangen' : 'Mood & Craving'}
        </Text>
        <View style={styles.placeholderContainer}>
          <Text style={[styles.placeholderText, { color: colors.muted }]}>
            {language === 'nl' 
              ? 'Nog geen stemmingsgegevens. Voeg je eerste stemming toe.' 
              : 'No mood data yet. Add your first mood entry.'}
          </Text>
        </View>
      </View>
    );
  }
  
  // Format dates for labels (show only day of month)
  const labels = recentEntries.map(entry => {
    const date = new Date(entry.date);
    return date.getDate().toString();
  });
  
  const chartData = {
    labels: labels,
    datasets: [
      {
        data: moodData,
        color: () => colors.primary,
        strokeWidth: 2,
      },
      {
        data: cravingData,
        color: () => colors.danger,
        strokeWidth: 2,
      },
    ],
    legend: [
      language === 'nl' ? 'Stemming' : 'Mood', 
      language === 'nl' ? 'Verlangen' : 'Craving'
    ],
  };
  
  const chartConfig = {
    backgroundGradientFrom: colors.card,
    backgroundGradientTo: colors.card,
    decimalPlaces: 0,
    color: () => colors.text,
    labelColor: () => colors.muted,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '5',
      strokeWidth: '2',
    },
  };
  
  const screenWidth = Dimensions.get('window').width - 32;
  
  return (
    <View style={[styles.container, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <Text style={[styles.title, { color: colors.text }]}>
        {language === 'nl' ? 'Stemming & Verlangen' : 'Mood & Craving'}
      </Text>
      <LineChart
        data={chartData}
        width={screenWidth}
        height={180}
        chartConfig={chartConfig}
        bezier
        style={styles.chart}
        withInnerLines={false}
        withOuterLines={true}
        withShadow={false}
        withDots={true}
        withVerticalLines={false}
        withHorizontalLines={true}
        yAxisInterval={1}
        yAxisSuffix=""
        yAxisLabel=""
        fromZero={true}
        segments={5}
      />
    </View>
  );
};
const styles = StyleSheet.create({


  chart: {
    borderRadius: 16,
    marginVertical: 8,
  },
  container: {
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 16,
    marginHorizontal: 16,
    padding: 16,
  },
  placeholderContainer: {
    alignItems: 'center',
    height: 180,
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 14,
    paddingHorizontal: 20,
    textAlign: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },


});