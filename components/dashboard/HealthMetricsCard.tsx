import React from "react";
import { useEffect, useRef, useCallback } from "react";
import {
  StyleSheet,
  Text,
  View,
  Animated,
  Easing,
  TouchableOpacity,
} from "react-native";
import {
  Heart,
  Activity,
  Moon,
  Droplets,
  Pill,
} from "lucide-react-native";

interface HealthMetric {
  id: string;
  type: "sleep" | "pills" | "hydration" | "exercise";
  value: number;
  maxValue: number | null;
  unit: string;
  trend: "up" | "down" | "stable";
}

interface HealthMetricsCardProps {
  metrics: HealthMetric[];
  language: string;
  colors: {
    primary: string;
    warning: string;
    info: string;
    success: string;
    danger: string;
    card: string;
    border: string;
    text: string;
    muted: string;
  };
  onPress?: () => void;
}

export const HealthMetricsCard: React.FC<HealthMetricsCardProps> = ({
  metrics,
  language,
  colors,
  onPress,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  const startAnimation = useCallback(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim]);

  useEffect(() => {
    startAnimation();
  }, [startAnimation]);

  const getMetricIcon = (type: string) => {
    switch (type) {
      case "sleep":
        return <Moon size={20} color={colors.primary} />;
      case "pills":
        return <Pill size={20} color={colors.info} />;
      case "hydration":
        return <Droplets size={20} color={colors.info} />;
      case "exercise":
        return <Heart size={20} color={colors.success} />;
      default:
        return <Activity size={20} color={colors.primary} />;
    }
  };

  const getMetricName = (type: string) => {
    if (language === "nl") {
      switch (type) {
        case "sleep":
          return "Slaap";
        case "pills":
          return "Pillen";
        case "hydration":
          return "Hydratatie";
        case "exercise":
          return "Beweging";
        default:
          return type;
      }
    } else {
      switch (type) {
        case "sleep":
          return "Sleep";
        case "pills":
          return "Pills";
        case "hydration":
          return "Hydration";
        case "exercise":
          return "Exercise";
        default:
          return type;
      }
    }
  };

  const getMetricColor = (type: string) => {
    switch (type) {
      case "sleep":
        return colors.primary;
      case "pills":
        return colors.info;
      case "hydration":
        return colors.info;
      case "exercise":
        return colors.success;
      default:
        return colors.primary;
    }
  };

  if (metrics.length === 0) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <TouchableOpacity
        style={[
          styles.card,
          { backgroundColor: colors.card, borderColor: colors.border },
        ]}
        onPress={onPress}
        activeOpacity={0.8}
      >
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Heart size={20} color={colors.primary} />
            <Text style={[styles.title, { color: colors.text }]}>
              {language === "nl" ? "Gezondheid" : "Health"}
            </Text>
          </View>
        </View>
        <View style={styles.metricsGrid}>
          {metrics.slice(0, 4).map((metric) => (
            <View
              key={metric.id}
              style={[
                styles.metricItem,
                styles.metricItemThemed,
                {
                  backgroundColor: colors.card,
                  borderColor: colors.border,
                },
              ]}
            >
              <View
                style={[
                  styles.metricIcon,
                  { backgroundColor: getMetricColor(metric.type) + "20" },
                ]}
              >
                {getMetricIcon(metric.type)}
              </View>
              <Text style={[styles.metricName, { color: colors.muted }]}>
                {getMetricName(metric.type)}
              </Text>
              <Text style={[styles.metricValue, { color: colors.text }]}>
                {metric.value} {metric.unit}
              </Text>
              <View style={styles.progressContainer}>
                <View
                  style={[
                    styles.progressBar,
                    { backgroundColor: colors.border },
                  ]}
                >
                  <View
                    style={[
                      styles.progressFill,
                      {
                        backgroundColor: getMetricColor(metric.type),
                        width: `${
                          metric.maxValue
                            ? (metric.value / metric.maxValue) * 100
                            : 0
                        }%`,
                      },
                    ]}
                  />
                </View>
              </View>
            </View>
          ))}
        </View>
        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: colors.muted }]}>
            {language === "nl"
              ? "Tap voor meer details"
              : "Tap for more details"}
          </Text>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 16,
    borderWidth: 1,
    elevation: 4,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  container: {
    marginBottom: 16,
    marginHorizontal: 16,
  },
  footer: {
    alignItems: "center",
    borderTopColor: "rgba(0,0,0,0.05)",
    borderTopWidth: 1,
    marginTop: 16,
    paddingTop: 12,
  },
  footerText: {
    fontSize: 12,
    fontStyle: "italic",
  },
  header: {
    marginBottom: 16,
  },
  metricIcon: {
    alignItems: "center",
    borderRadius: 18,
    height: 36,
    justifyContent: "center",
    marginBottom: 8,
    width: 36,
  },
  metricItem: {
    alignItems: "center",
    borderWidth: 1,
    elevation: 2,
    flex: 1,
    margin: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
  },
  metricItemThemed: {
    borderRadius: 16,
    padding: 16,
  },
  metricName: {
    fontSize: 12,
    fontWeight: "500",
    marginBottom: 4,
    textAlign: "center",
  },
  metricValue: {
    fontSize: 16,
    fontWeight: "700",
    marginBottom: 8,
    textAlign: "center",
  },
  metricsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginHorizontal: -4,
  },
  progressBar: {
    borderRadius: 2,
    height: 4,
    overflow: "hidden",
    width: "100%",
  },
  progressContainer: {
    width: "100%",
  },
  progressFill: {
    borderRadius: 2,
    height: "100%",
  },
  title: {
    fontSize: 18,
    fontWeight: "700",
    marginLeft: 8,
  },
  titleContainer: {
    alignItems: "center",
    flexDirection: "row",
  },
});