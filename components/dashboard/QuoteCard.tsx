import React from "react";
import { StyleSheet, View, Text, TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";
import { BookOpen } from "lucide-react-native";
import { useQuotesStore } from "@/store/content/quotes-store";
import { ColorScheme } from "@/hooks/ui/useColorScheme";

interface QuoteCardProps {
  colors: ColorScheme;
  language: string;
}

export const QuoteCard: React.FC<QuoteCardProps> = ({ colors, language }) => {
  const router = useRouter();
  const { dailyQuote, quotes } = useQuotesStore();

  // Use the daily quote if available, otherwise use the first quote from the quotes array
  const quote = dailyQuote || (quotes && quotes.length > 0 ? quotes[0] : null);

  const handlePress = () => {
    // Navigate to mindfulness tab since resources are removed
    router.push("/(tabs)/mindfulness");
  };

  if (!quote) {
    return (
      <TouchableOpacity
        style={[
          styles.container,
          { backgroundColor: colors.card, borderColor: colors.border },
        ]}
        onPress={handlePress}
      >
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            {language === "nl" ? "Dagelijkse Inspiratie" : "Daily Inspiration"}
          </Text>
          <BookOpen size={20} color={colors.primary} />
        </View>
        <Text style={[styles.loadingText, { color: colors.muted }]}>
          {language === "nl" ? "Quotes worden geladen..." : "Loading quotes..."}
        </Text>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: colors.card, borderColor: colors.border },
      ]}
      onPress={handlePress}
    >
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          {language === "nl" ? "Dagelijkse Inspiratie" : "Daily Inspiration"}
        </Text>
        <BookOpen size={20} color={colors.primary} />
      </View>
      <Text style={[styles.quote, { color: colors.text }]}>&quot;{quote.text}&quot;</Text>
      <Text style={[styles.author, { color: colors.muted }]}>
        - {quote.author}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({


  author: {
    fontSize: 14,
    textAlign: "right",
  },
  container: {
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 16,
    marginHorizontal: 16,
    padding: 16,
  },
  header: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  loadingText: {
    fontSize: 14,
    fontStyle: "italic",
    marginVertical: 16,
    textAlign: "center",
  },
  quote: {
    fontSize: 16,
    fontStyle: "italic",
    lineHeight: 24,
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
  },


});
