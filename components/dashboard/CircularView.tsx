import React from "react";
import {
  StyleSheet,
  View,
  Text,
  Animated,
  Dimensions,
  Pressable,
} from "react-native";
import {
  Calendar,
  Target,
  Award,
  TrendingUp,
  DollarSign,
} from "lucide-react-native";
import Svg, {
  Circle,
  G,
  Defs,
  LinearGradient as SvgLinearGradient,
  Stop,
} from "react-native-svg";
import { useEffect, useRef, useState } from "react";
import { LinearGradient } from "expo-linear-gradient";
import Colors from "@/constants/colors";

interface CircularViewProps {
  diffDays: number;
  sobrietyDate: Date;
  nextMilestone: { days: number; name: string };
  savedAmount: number;
  usageCost: number;
  _costFrequency: string;
  language: string;
  currency: "USD" | "EUR";
  colors: typeof Colors.light;
  usageAmount?: string;
  _substanceType?: string;
}

export const CircularView: React.FC<CircularViewProps> = ({
  diffDays,
  sobrietyDate,
  nextMilestone,
  savedAmount,
  usageCost,
  _costFrequency,
  language,
  currency,
  colors,
  usageAmount,
  _substanceType,
}) => {
  // Animation values
  const progressAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const scaleAnimation = useRef(new Animated.Value(0.95)).current;

  // State for animated values
  const [animatedDays, setAnimatedDays] = useState(0);
  const [animatedSavings, setAnimatedSavings] = useState(0);
  const [animatedStrokeDashoffset, setAnimatedStrokeDashoffset] = useState(0);

  // Calculate progress for the next milestone
  const nextMilestoneProgress = Math.min(1, diffDays / nextMilestone.days);

  // SVG circle properties
  const screenWidth = Dimensions.get("window").width;
  const radius = Math.min(screenWidth * 0.3, 95);
  const strokeWidth = 12;
  const circumference = 2 * Math.PI * radius;
  const halfCircle = radius + strokeWidth;

  // Calculate days until next milestone
  const daysUntilNextMilestone = Math.max(0, nextMilestone.days - diffDays);
  const currencySymbol = currency === "USD" ? "$" : "€";

  // Animations
  useEffect(() => {
    const progressListener = progressAnimation.addListener(({ value }) => {
      const strokeDashoffset = circumference - value * circumference;
      setAnimatedStrokeDashoffset(strokeDashoffset);
    });

    // Entrance animations
    Animated.parallel([
      Animated.spring(scaleAnimation, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(progressAnimation, {
        toValue: nextMilestoneProgress,
        duration: 1800,
        useNativeDriver: false,
      }),
    ]).start();

    // Counter animations
    const animateDays = () => {
      let start = 0;
      const increment = diffDays / 60;
      const timer = setInterval(() => {
        start += increment;
        if (start >= diffDays) {
          setAnimatedDays(diffDays);
          clearInterval(timer);
        } else {
          setAnimatedDays(Math.floor(start));
        }
      }, 30);
    };

    const animateSavings = () => {
      let start = 0;
      const increment = savedAmount / 80;
      const timer = setInterval(() => {
        start += increment;
        if (start >= savedAmount) {
          setAnimatedSavings(savedAmount);
          clearInterval(timer);
        } else {
          setAnimatedSavings(start);
        }
      }, 25);
    };

    setTimeout(animateDays, 500);
    setTimeout(animateSavings, 800);

    return () => {
      progressAnimation.removeListener(progressListener);
    };
  }, [diffDays, savedAmount, nextMilestoneProgress, circumference, progressAnimation, scaleAnimation, fadeAnimation]);

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          opacity: fadeAnimation,
          transform: [{ scale: scaleAnimation }],
        },
      ]}
    >
      {/* Main Circular Progress */}
      <View style={styles.heroSection}>
        <LinearGradient
          colors={[colors.primary, colors.primaryDark]}
          style={styles.circularContainer}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.svgContainer}>
            <Svg
              width={radius * 2 + strokeWidth * 2}
              height={radius * 2 + strokeWidth * 2}
              viewBox={`0 0 ${halfCircle * 2} ${halfCircle * 2}`}
            >
              <Defs>
                <SvgLinearGradient
                  id="progressGradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="100%"
                >
                  <Stop offset="0%" stopColor="#FFFFFF" stopOpacity="1" />
                  <Stop offset="100%" stopColor="rgba(255, 255, 255, 0.8)" stopOpacity="1" />
                </SvgLinearGradient>
              </Defs>
              
              <G rotation="-90" origin={`${halfCircle}, ${halfCircle}`}>
                {/* Background Circle */}
                <Circle
                  cx={halfCircle}
                  cy={halfCircle}
                  r={radius}
                  fill="transparent"
                  stroke="rgba(255, 255, 255, 0.2)"
                  strokeWidth={strokeWidth}
                  strokeLinecap="round"
                />
                {/* Progress Circle */}
                <Circle
                  cx={halfCircle}
                  cy={halfCircle}
                  r={radius}
                  fill="transparent"
                  stroke="url(#progressGradient)"
                  strokeWidth={strokeWidth}
                  strokeDasharray={circumference}
                  strokeDashoffset={animatedStrokeDashoffset}
                  strokeLinecap="round"
                />
              </G>
            </Svg>
          </View>
          
          {/* Center Content */}
          <View style={styles.circleContent}>
            <Text style={styles.daysCount}>
              {animatedDays}
            </Text>
            <Text style={styles.daysLabel}>
              {language === "nl" ? "dagen nuchter" : "days sober"}
            </Text>
            
            {/* Progress Badge */}
            <View style={styles.progressBadge}>
              <TrendingUp size={12} color="#FFFFFF" strokeWidth={2} />
              <Text style={styles.progressText}>
                {Math.round(nextMilestoneProgress * 100)}%
              </Text>
            </View>
            
            {/* Next Milestone */}
            <Text style={styles.milestoneText}>
              {nextMilestone.name}
            </Text>
          </View>
        </LinearGradient>
      </View>

      {/* Clean Stats Grid */}
      <View style={styles.statsContainer}>
        {/* Stats Grid - All cards in one row when possible */}
        <View style={styles.statsGrid}>
          {/* Start Date */}
          <Pressable
            style={({ pressed }) => [
              styles.statCard,
              { transform: [{ scale: pressed ? 0.98 : 1 }] },
            ]}
          >
            <View style={[styles.statIconWrapper, { backgroundColor: colors.primary + '12' }]}>
              <Calendar size={20} color={colors.primary} strokeWidth={2} />
            </View>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              {language === "nl" ? "Start" : "Start"}
            </Text>
            <Text style={[styles.statValue, { color: colors.text }]}>
              {sobrietyDate.toLocaleDateString(
                language === "nl" ? "nl-NL" : "en-US",
                { month: "short", day: "numeric" }
              )}
            </Text>
          </Pressable>

          {/* Next Goal */}
          <Pressable
            style={({ pressed }) => [
              styles.statCard,
              { transform: [{ scale: pressed ? 0.98 : 1 }] },
            ]}
          >
            <View style={[styles.statIconWrapper, { backgroundColor: colors.warning + '12' }]}>
              <Target size={20} color={colors.warning} strokeWidth={2} />
            </View>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              {language === "nl" ? "Doel" : "Goal"}
            </Text>
            <Text style={[styles.statValue, { color: colors.text }]}>
              {daysUntilNextMilestone} {language === "nl" ? "dagen" : "days"}
            </Text>
          </Pressable>

          {/* Savings */}
          {usageCost > 0 && (
            <Pressable
              style={({ pressed }) => [
                styles.statCard,
                { transform: [{ scale: pressed ? 0.98 : 1 }] },
              ]}
            >
              <View style={[styles.statIconWrapper, { backgroundColor: colors.success + '12' }]}>
                <DollarSign size={20} color={colors.success} strokeWidth={2} />
              </View>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                {language === "nl" ? "Bespaard" : "Saved"}
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]} numberOfLines={1} adjustsFontSizeToFit>
                {currencySymbol}{animatedSavings.toFixed(0)}
              </Text>
              {usageAmount && usageAmount !== "Not specified" && (
                <Text style={[styles.statSubtext, { color: colors.textTertiary }]} numberOfLines={1}>
                  {language === "nl" ? "Was: " : "Was: "}{usageAmount}
                </Text>
              )}
            </Pressable>
          )}

          {/* Achievement */}
          <Pressable
            style={({ pressed }) => [
              styles.statCard,
              { transform: [{ scale: pressed ? 0.98 : 1 }] },
            ]}
          >
            <View style={[styles.statIconWrapper, { backgroundColor: colors.secondary + '12' }]}>
              <Award size={20} color={colors.secondary} strokeWidth={2} />
            </View>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              {language === "nl" ? "Niveau" : "Level"}
            </Text>
            <Text style={[styles.statValue, { color: colors.text }]}>
              {diffDays >= 365
                ? language === "nl" ? "Goud" : "Gold"
                : diffDays >= 180
                ? language === "nl" ? "Zilver" : "Silver"
                : diffDays >= 30
                ? language === "nl" ? "Brons" : "Bronze"
                : language === "nl" ? "Starter" : "Starter"}
            </Text>
          </Pressable>
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  circleContent: {
    alignItems: "center",
    height: "100%",
    justifyContent: "center",
    paddingHorizontal: 20,
    position: "absolute",
    width: "100%",
  },
  circularContainer: {
    alignItems: "center",
    borderRadius: 999,
    elevation: 12,
    height: 260,
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    width: 260,
  },
  container: {
    alignItems: "center",
    paddingBottom: 16,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  daysCount: {
    color: "#FFFFFF",
    fontSize: 56,
    fontWeight: "800",
    letterSpacing: -2,
    textAlign: "center",
    textShadowColor: "rgba(0, 0, 0, 0.2)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  daysLabel: {
    color: "rgba(255, 255, 255, 0.9)",
    fontSize: 16,
    fontWeight: "500",
    marginTop: 2,
    textAlign: "center",
  },
  heroSection: {
    alignItems: "center",
    marginBottom: 32,
  },
  milestoneText: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 13,
    fontWeight: "500",
    marginTop: 6,
    paddingHorizontal: 8,
    textAlign: "center",
  },
  progressBadge: {
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 16,
    flexDirection: "row",
    marginTop: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  progressText: {
    color: "#FFFFFF",
    fontSize: 13,
    fontWeight: "600",
    marginLeft: 4,
  },
  statCard: {
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    elevation: 2,
    flex: 1,
    justifyContent: "center",
    minHeight: 100,
    paddingHorizontal: 8,
    paddingVertical: 18,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
  },
  statIconWrapper: {
    alignItems: "center",
    borderRadius: 12,
    elevation: 1,
    height: 36,
    justifyContent: "center",
    marginBottom: 7,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    width: 36,
  },
  statLabel: {
    fontSize: 11,
    fontWeight: "500",
    marginBottom: 4,
    textAlign: "center",
  },
  statSubtext: {
    fontSize: 9,
    marginTop: 1,
    textAlign: "center",
  },
  statValue: {
    fontSize: 13,
    fontWeight: "600",
    lineHeight: 16,
    textAlign: "center",
  },
  statsContainer: {
    paddingHorizontal: 4,
    width: "100%",
  },
  statsGrid: {
    alignItems: "stretch",
    flexDirection: "row",
    gap: 10,
    justifyContent: "space-between",
  },
  svgContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
});