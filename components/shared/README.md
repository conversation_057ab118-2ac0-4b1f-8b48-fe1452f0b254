# Shared Components Library

This directory contains reusable UI components that can be used across different parts of the SobrixHealth app.

## Components

### Animation Hooks

#### useFadeScaleAnimation

A reusable hook for fade and scale animations commonly used in dashboard components.

**Features:**

- Configurable duration, delay, and easing
- Automatic animation start on mount
- Manual control with start/reset functions
- Optimized for native driver

**Usage:**

```tsx
import { useFadeScaleAnimation } from "@/components/shared";

const { animatedStyle, startAnimation, resetAnimation } = useFadeScaleAnimation(
  {
    duration: 800,
    delay: 100,
  }
);

<Animated.View style={[styles.container, animatedStyle]}>
  {/* Your content */}
</Animated.View>;
```

#### useFadeSlideAnimation

A reusable hook for fade and slide animations with configurable slide distance.

**Usage:**

```tsx
import { useFadeSlideAnimation } from "@/components/shared";

const { animatedStyle } = useFadeSlideAnimation({
  duration: 600,
  slideDistance: 30,
});
```

#### useStaggeredAnimation

A reusable hook for staggered animations across multiple items (perfect for button grids).

**Usage:**

```tsx
import { useStaggeredAnimation } from "@/components/shared";

const { items } = useStaggeredAnimation({
  itemCount: 4,
  staggerDelay: 50,
});

{
  items.map((item, index) => (
    <Animated.View key={index} style={item.animatedStyle}>
      {/* Button content */}
    </Animated.View>
  ));
}
```

### MoodSelector

A customizable mood selection component with 5 mood states (great, good, neutral, bad, terrible).

**Features:**

- Configurable size (small, medium, large)
- Optional title display
- Custom title support
- Consistent theming
- Multi-language support

**Usage:**

```tsx
import { MoodSelector, MoodType } from "@/components/shared";

const [mood, setMood] = useState<MoodType>("neutral");

<MoodSelector
  selectedMood={mood}
  onMoodChange={setMood}
  size="medium"
  showTitle={true}
  title="How are you feeling?"
/>;
```

### JournalEditor

A comprehensive journal entry editor with date picker, mood selector, and rich text editing.

**Features:**

- Date selection with platform-specific pickers
- Optional mood tracking
- Title and content editing
- Save/Delete/Cancel actions
- Validation
- Multi-language support

**Usage:**

```tsx
import { JournalEditor, MoodType } from "@/components/shared";

<JournalEditor
  entryId="entry-123"
  initialTitle="My Journal Entry"
  initialContent="Today was a good day..."
  initialMood="good"
  isEditing={true}
  showMoodSelector={true}
  onSave={(entry) => console.log("Saved:", entry)}
  onDelete={(id) => console.log("Deleted:", id)}
  onCancel={() => console.log("Cancelled")}
/>;
```

### QuotesList

A scrollable list component for displaying inspirational quotes.

**Features:**

- Scrollable list with custom height
- Optional title display
- Quote categorization
- Empty state handling
- Consistent theming

**Usage:**

```tsx
import { QuotesList, Quote } from "@/components/shared";

const quotes: Quote[] = [
  {
    text: "The only way to do great work is to love what you do.",
    author: "Steve Jobs",
    category: "Motivation",
  },
];

<QuotesList
  quotes={quotes}
  title="Daily Inspiration"
  showTitle={true}
  maxHeight={300}
/>;
```

## Design Principles

1. **Consistency**: All components follow the same design patterns and use the app's theme system
2. **Reusability**: Components are designed to be flexible and configurable for different use cases
3. **Accessibility**: Components support proper accessibility features
4. **Internationalization**: All text is localized using the user's language preference
5. **Type Safety**: Full TypeScript support with exported types

## Adding New Components

When adding new shared components:

1. Create the component file in this directory
2. Export it from `index.ts`
3. Add documentation to this README
4. Follow the existing patterns for theming and internationalization
5. Include proper TypeScript types
6. Test across different screen sizes and themes

## Migration from Resources

These components were originally part of the Resources feature but have been refactored into a shared library for broader use across the app. The components have been enhanced with:

- Better TypeScript support
- More configuration options
- Improved accessibility
- Consistent styling patterns
- Better error handling
