import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import * as Haptics from 'expo-haptics';

interface ColorScheme {
  background: string;
  card: string;
  border: string;
  text: string;
  primary: string;
  muted: string;
}

interface Option {
  label: string;
  value: string;
  labelNL?: string;
}

interface OptionSelectorProps {
  options: Option[];
  selectedValue: string | undefined;
  onSelect: (value: string | undefined) => void;
  colors: ColorScheme;
  language?: 'en' | 'nl';
  multiSelect?: boolean;
  selectedValues?: string[];
  onMultiSelect?: (values: string[]) => void;
  containerStyle?: object;
  optionStyle?: object;
  buttonStyle?: object;
  textStyle?: object;
  selectedButtonStyle?: object;
  selectedTextStyle?: object;
}

export const OptionSelector: React.FC<OptionSelectorProps> = ({
  options,
  selectedValue,
  onSelect,
  colors,
  language = 'en',
  multiSelect = false,
  selectedValues = [],
  onMultiSelect,
  containerStyle,
  optionStyle,
  buttonStyle,
  textStyle,
  selectedButtonStyle,
  selectedTextStyle,
}) => {
  const handleSelect = (value: string) => {
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
    // Allow deselection if it's not multi-select and the same value is selected again
    if (!multiSelect && selectedValue === value) {
      onSelect(undefined);
      return;
    }

    if (multiSelect && onMultiSelect) {
      const newValues = selectedValues.includes(value)
        ? selectedValues.filter(v => v !== value)
        : [...selectedValues, value];
      onMultiSelect(newValues);
    } else {
      onSelect(value);
    }
  };

  const isSelected = (value: string) => {
    if (multiSelect) {
      return selectedValues.includes(value);
    }
    return selectedValue === value;
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {options.map((option) => (
        <TouchableOpacity
          key={option.value}
          style={[
            styles.option,
            {
              backgroundColor: isSelected(option.value) ? (selectedButtonStyle ? undefined : colors.primary) : (buttonStyle ? undefined : colors.card),
              borderColor: isSelected(option.value) ? colors.primary : colors.border,
              ...(isSelected(option.value) ? selectedButtonStyle : buttonStyle),
            },
            optionStyle,
          ]}
          onPress={() => handleSelect(option.value)}
        >
          <Text
            style={[
              styles.optionText,
              isSelected(option.value) ? (selectedTextStyle ? undefined : styles.optionTextSelected) : (textStyle ? undefined : { color: colors.text }),
              isSelected(option.value) ? selectedTextStyle : textStyle,
            ]}
          >
            {language === 'nl' && option.labelNL ? option.labelNL : option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({


  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  option: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
    marginRight: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  optionTextSelected: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },


}); 