import React from "react";
import { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
} from "react-native";
import { useUserStore } from "@/store/user/user-store";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import DateTimePicker, {
  DateTimePickerEvent,
} from "@react-native-community/datetimepicker";
import { Save, Trash2, Calendar } from "lucide-react-native";
import MoodSelector, { MoodType } from "./MoodSelector";
interface JournalEditorProps {
  entryId?: string;
  initialTitle?: string;
  initialContent?: string;
  initialDate?: Date;
  initialMood?: MoodType;
  onSave?: (entry: {
    id?: string;
    title: string;
    content: string;
    date: Date;
    mood: MoodType;
  }) => void;
  onDelete?: (entryId: string) => void;
  onCancel?: () => void;
  showMoodSelector?: boolean;
  isEditing?: boolean;
}
export default function JournalEditor({
  entryId,
  initialTitle = "",
  initialContent = "",
  initialDate = new Date(),
  initialMood = "neutral",
  onSave,
  onDelete,
  onCancel,
  showMoodSelector = true,
  isEditing = false,
}: JournalEditorProps) {
  const { profile } = useUserStore();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const [journalTitle, setJournalTitle] = useState(initialTitle);
  const [journalContent, setJournalContent] = useState(initialContent);
  const [journalDate, setJournalDate] = useState(initialDate);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [journalMood, setJournalMood] = useState<MoodType>(initialMood);
  const handleDateChange = (
    event: DateTimePickerEvent,
    selectedDate?: Date
  ) => {
    const currentDate = selectedDate || journalDate;
    setShowDatePicker(Platform.OS === "ios");
    setJournalDate(currentDate);
  };
  const handleSave = () => {
    if (!journalTitle.trim() && !journalContent.trim()) {
      Alert.alert(
        profile?.language === "nl" ? "Fout" : "Error",
        profile?.language === "nl"
          ? "Voer een titel of inhoud in."
          : "Please enter a title or content."
      );
      return;
    }
    const entry = {
      id: entryId,
      title: journalTitle,
      content: journalContent,
      date: journalDate,
      mood: journalMood,
    };
    if (onSave) {
      onSave(entry);
    } else {
      // Default behavior - show success message
      Alert.alert(
        profile?.language === "nl" ? "Dagboek Opgeslagen" : "Journal Saved",
        profile?.language === "nl"
          ? "Je dagboek is succesvol opgeslagen."
          : "Your journal entry has been successfully saved."
      );
    }
  };
  const handleDelete = () => {
    if (!entryId) return;
    Alert.alert(
      profile?.language === "nl" ? "Bevestig verwijderen" : "Confirm Deletion",
      profile?.language === "nl"
        ? "Weet je zeker dat je dit dagboek wilt verwijderen?"
        : "Are you sure you want to delete this journal entry?",
      [
        {
          text: profile?.language === "nl" ? "Annuleren" : "Cancel",
          style: "cancel",
        },
        {
          text: profile?.language === "nl" ? "Verwijderen" : "Delete",
          style: "destructive",
          onPress: () => {
            if (onDelete) {
              onDelete(entryId);
            }
          },
        },
      ]
    );
  };
  return (
    <View style={styles.journalEditor}>
      {/* Date Picker Section */}
      <View style={[styles.datePickerSection, { borderColor: colors.border }]}>
        <View style={styles.datePickerHeader}>
          <Text style={[styles.datePickerLabel, { color: colors.text }]}>
            {profile?.language === "nl" ? "Datum" : "Date"}
          </Text>
          <TouchableOpacity
            style={[styles.datePickerButton, { backgroundColor: colors.card }]}
            onPress={() => setShowDatePicker(true)}
          >
            <Calendar size={18} color={colors.primary} />
            <Text style={[styles.datePickerButtonText, { color: colors.text }]}>
              {journalDate.toLocaleDateString()}
            </Text>
          </TouchableOpacity>
        </View>
        {showDatePicker && (
          <View style={styles.datePickerContainer}>
            {Platform.OS === "ios" ? (
              <View
                style={[
                  styles.iosDatePickerWrapper,
                  { backgroundColor: colors.card },
                ]}
              >
                <View style={styles.iosDatePickerHeader}>
                  <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                    <Text style={{ color: colors.danger }}>
                      {profile?.language === "nl" ? "Annuleren" : "Cancel"}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                    <Text style={{ color: colors.primary }}>
                      {profile?.language === "nl" ? "Gereed" : "Done"}
                    </Text>
                  </TouchableOpacity>
                </View>
                <DateTimePicker
                  value={journalDate}
                  mode="date"
                  display="spinner"
                  onChange={handleDateChange}
                  maximumDate={new Date()}
                  textColor={colors.text}
                />
              </View>
            ) : (
              <DateTimePicker
                value={journalDate}
                mode="date"
                display="default"
                onChange={handleDateChange}
                maximumDate={new Date()}
              />
            )}
          </View>
        )}
      </View>
      {/* Mood Selection Section */}
      {showMoodSelector && (
        <MoodSelector
          selectedMood={journalMood}
          onMoodChange={setJournalMood}
        />
      )}
      {/* Journal Title and Content */}
      <TextInput
        style={[
          styles.journalTitleInput,
          { color: colors.text, borderColor: colors.border },
        ]}
        value={journalTitle}
        onChangeText={setJournalTitle}
        placeholder={
          profile?.language === "nl"
            ? "Titel van je dagboek..."
            : "Journal title..."
        }
        placeholderTextColor={colors.muted}
      />
      <TextInput
        style={[
          styles.journalContentInput,
          { color: colors.text, borderColor: colors.border },
        ]}
        value={journalContent}
        onChangeText={setJournalContent}
        placeholder={
          profile?.language === "nl"
            ? "Begin hier met schrijven..."
            : "Start writing here..."
        }
        placeholderTextColor={colors.muted}
        multiline
        textAlignVertical="top"
      />
      <View style={styles.journalActions}>
        <TouchableOpacity
          style={[
            styles.journalActionButton,
            { backgroundColor: colors.primary },
          ]}
          onPress={handleSave}
        >
          <Save size={20} color="#fff" />
          <Text style={styles.journalActionButtonText}>
            {profile?.language === "nl" ? "Opslaan" : "Save"}
          </Text>
        </TouchableOpacity>
        {isEditing && entryId && (
          <TouchableOpacity
            style={[
              styles.journalActionButton,
              { backgroundColor: colors.danger },
            ]}
            onPress={handleDelete}
          >
            <Trash2 size={20} color="#fff" />
            <Text style={styles.journalActionButtonText}>
              {profile?.language === "nl" ? "Verwijderen" : "Delete"}
            </Text>
          </TouchableOpacity>
        )}
        {onCancel && (
          <TouchableOpacity
            style={[
              styles.journalActionButton,
              { backgroundColor: colors.muted },
            ]}
            onPress={onCancel}
          >
            <Text
              style={[styles.journalActionButtonText, { color: colors.text }]}
            >
              {profile?.language === "nl" ? "Annuleren" : "Cancel"}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}
const styles = StyleSheet.create({


  datePickerButton: {
    alignItems: "center",
    borderRadius: 6,
    flexDirection: "row",
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  datePickerButtonText: {
    fontSize: 16,
    marginLeft: 8,
  },
  datePickerContainer: {
    marginTop: 10,
  },
  datePickerHeader: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  datePickerLabel: {
    fontSize: 16,
    fontWeight: "500",
  },
  datePickerSection: {
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
    padding: 12,
  },
  iosDatePickerHeader: {
    borderBottomColor: "rgba(0,0,0,0.1)",
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 10,
  },
  iosDatePickerWrapper: {
    borderRadius: 10,
    overflow: "hidden",
  },
  journalActionButton: {
    alignItems: "center",
    borderRadius: 8,
    flexDirection: "row",
    flex: 1,
    justifyContent: "center",
    marginHorizontal: 4,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  journalActionButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  journalActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 40,
  },
  journalContentInput: {
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 16,
    minHeight: 200,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  journalEditor: {
    flex: 1,
  },
  journalTitleInput: {
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },


});