import React from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { Save } from 'lucide-react-native';

interface ButtonGroupProps {
  onCancel?: () => void;
  onSubmit?: () => void;
  cancelText?: string;
  submitText?: string;
  colors: {
    primary: string;
    text: string;
    border: string;
  };
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  onCancel,
  onSubmit,
  cancelText = 'Cancel',
  submitText = 'Save',
  colors,
}) => {
  return (
    <View style={[styles.footer, { borderTopColor: colors.border }]}>
      <TouchableOpacity
        style={[styles.cancelButton, { borderColor: colors.border }]}
        onPress={onCancel}
      >
        <Text style={[styles.cancelButtonText, { color: colors.text }]}>
          {cancelText}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.saveButton, { backgroundColor: colors.primary }]}
        onPress={onSubmit}
      >
        <Save size={20} color="#fff" />
        <Text style={styles.saveButtonText}>
          {submitText}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({


  cancelButton: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flex: 1,
    justifyContent: 'center',
    marginRight: 8,
    padding: 16,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  footer: {
    borderTopWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  saveButton: {
    alignItems: 'center',
    borderRadius: 12,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    marginLeft: 8,
    padding: 16,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },


}); 