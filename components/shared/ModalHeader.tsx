import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { X } from 'lucide-react-native';

interface ColorScheme {
  background: string;
  card: string;
  border: string;
  text: string;
  primary: string;
  muted: string;
}

interface ModalHeaderProps {
  title: string;
  onClose: () => void;
  colors: ColorScheme;
  showCloseButton?: boolean;
  closeButtonStyle?: 'default' | 'background';
}

export const ModalHeader: React.FC<ModalHeaderProps> = ({
  title,
  onClose,
  colors,
  showCloseButton = true,
  closeButtonStyle = 'default',
}) => {
  return (
    <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
      <Text style={[styles.modalTitle, { color: colors.text }]}>
        {title}
      </Text>
      {showCloseButton && (
        <TouchableOpacity
          onPress={onClose}
          style={[
            styles.closeButton,
            closeButtonStyle === 'background' && {
              backgroundColor: colors.background,
            },
          ]}
        >
          <X size={24} color={colors.text} strokeWidth={2.5} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({


  closeButton: {
    alignItems: 'center',
    borderRadius: 22,
    height: 44,
    justifyContent: 'center',
    width: 44,
  },
  modalHeader: {
    alignItems: 'center',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
  },


}); 