import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/context/theme-context';
import { useUserStore } from '@/store/user/user-store';
import Colors from '@/constants/colors';
import {
  Bug,
  X,
  Database,
  FileText,
  Image,
  User,
  Smartphone,
  HardDrive,
  RefreshCw,
} from 'lucide-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface DebugPanelProps {
  visible: boolean;
  onClose: () => void;
}

interface StorageInfo {
  totalKeys: number;
  userProfileSize: string;
  totalStorageSize: string;
  storageQuota?: string;
  storageUsage?: string;
}

export const DebugPanel: React.FC<DebugPanelProps> = ({ visible, onClose }) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { profile } = useUserStore();
  const [storageInfo, setStorageInfo] = useState<StorageInfo | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  const styles = StyleSheet.create({
    categoryCard: {
      backgroundColor: colors.card,
      borderColor: colors.border,
      borderRadius: 8,
      borderWidth: 1,
      marginBottom: 12,
      padding: 12,
    },
    categoryTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 8,
    },
    closeButton: {
      alignItems: 'center',
      backgroundColor: colors.muted + '20',
      borderRadius: 20,
      height: 40,
      justifyContent: 'center',
      width: 40,
    },
    content: {
      flex: 1,
      maxHeight: 600,
      padding: 20,
    },
    dataItem: {
      backgroundColor: colors.background,
      borderRadius: 6,
      marginBottom: 6,
      padding: 8,
    },
    dataLabel: {
      fontSize: 12,
      fontWeight: '500',
      marginBottom: 2,
    },
    dataValue: {
      fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
      fontSize: 11,
    },
    header: {
      alignItems: 'center',
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
      flexDirection: 'row',
      paddingBottom: 16,
      paddingHorizontal: 20,
      paddingTop: 10,
    },
    headerTitle: {
      flex: 1,
      fontSize: 20,
      fontWeight: '700',
      marginLeft: 16,
    },
    infoRow: {
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 4,
    },
    modalContent: {
      borderRadius: 24,
      elevation: 8,
      flex: 1,
      maxHeight: '90%',
      overflow: 'hidden',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.25,
      shadowRadius: 20,
      width: '100%',
    },
    modalOverlay: {
      alignItems: 'center',
      backgroundColor: 'rgba(0,0,0,0.5)',
      flex: 1,
      justifyContent: 'center',
      padding: 20,
    },
    refreshButton: {
      alignItems: 'center',
      backgroundColor: colors.primary + '20',
      borderRadius: 20,
      height: 40,
      justifyContent: 'center',
      marginRight: 8,
      width: 40,
    },
    sectionIcon: {
      alignItems: 'center',
      backgroundColor: colors.primary + '20',
      borderRadius: 8,
      height: 32,
      justifyContent: 'center',
      marginRight: 12,
      width: 32,
    },
    sectionRow: {
      alignItems: 'center',
      flexDirection: 'row',
      marginBottom: 8,
    },
    statsCard: {
      backgroundColor: colors.card,
      borderColor: colors.border,
      borderRadius: 12,
      borderWidth: 1,
      marginBottom: 20,
      padding: 16,
    },
    statsTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 12,
    },
  });

  const getStorageInfo = async (): Promise<StorageInfo> => {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const userProfileKey = keys.find(key => key.includes('user-profile-storage'));
      
      let userProfileSize = '0 B';
      let totalSize = 0;

      if (userProfileKey) {
        const userProfileData = await AsyncStorage.getItem(userProfileKey);
        if (userProfileData) {
          const sizeInBytes = new Blob([userProfileData]).size;
          userProfileSize = formatBytes(sizeInBytes);
          totalSize += sizeInBytes;
        }
      }

      // Get other storage items
      for (const key of keys) {
        if (key !== userProfileKey) {
          const data = await AsyncStorage.getItem(key);
          if (data) {
            totalSize += new Blob([data]).size;
          }
        }
      }

      const result: StorageInfo = {
        totalKeys: keys.length,
        userProfileSize,
        totalStorageSize: formatBytes(totalSize),
      };

      // Web-specific storage quota info
      if (Platform.OS === 'web' && 'storage' in navigator && 'estimate' in navigator.storage) {
        try {
          const estimate = await navigator.storage.estimate();
          if (estimate.usage && estimate.quota) {
            result.storageUsage = formatBytes(estimate.usage);
            result.storageQuota = formatBytes(estimate.quota);
          }
        } catch (error) {
          console.warn('Could not get storage estimate:', error);
        }
      }

      return result;
    } catch (error) {
      console.error('Error getting storage info:', error);
      return {
        totalKeys: 0,
        userProfileSize: '0 B',
        totalStorageSize: '0 B',
      };
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const refreshData = async () => {
    const info = await getStorageInfo();
    setStorageInfo(info);
    setRefreshKey(prev => prev + 1);
  };

  useEffect(() => {
    if (visible) {
      refreshData();
    }
  }, [visible]);

  const renderDocuments = () => {
    const documents = profile?.documents || [];
    const categories = ['EmergencyPlan', 'RelapsePlan'];
    
    return (
      <View style={styles.categoryCard}>
        <View style={styles.sectionRow}>
          <View style={styles.sectionIcon}>
            <FileText size={16} color={colors.primary} />
          </View>
          <Text style={[styles.categoryTitle, { color: colors.text }]}>
            Documents ({documents.length})
          </Text>
        </View>
        
        {categories.map(category => {
          const categoryDocs = documents.filter(doc => doc.category === category);
          return (
            <View key={category} style={styles.dataItem}>
              <Text style={[styles.dataLabel, { color: colors.text }]}>
                {category}: {categoryDocs.length} items
              </Text>
              {categoryDocs.map(doc => (
                <Text key={doc.id} style={[styles.dataValue, { color: colors.muted }]}>
                  • {doc.title} ({new Date(doc.date).toLocaleDateString()})
                </Text>
              ))}
            </View>
          );
        })}
      </View>
    );
  };

  const renderMediaFiles = () => {
    const mediaFiles = profile?.mediaFiles || [];
    const categories = ['EmergencyCard'];
    
    return (
      <View style={styles.categoryCard}>
        <View style={styles.sectionRow}>
          <View style={styles.sectionIcon}>
            <Image size={16} color={colors.primary} />
          </View>
          <Text style={[styles.categoryTitle, { color: colors.text }]}>
            Media Files ({mediaFiles.length})
          </Text>
        </View>
        
        {categories.map(category => {
          const categoryFiles = mediaFiles.filter(file => file.category === category);
          return (
            <View key={category} style={styles.dataItem}>
              <Text style={[styles.dataLabel, { color: colors.text }]}>
                {category}: {categoryFiles.length} items
              </Text>
              {categoryFiles.map(file => (
                <Text key={file.id} style={[styles.dataValue, { color: colors.muted }]}>
                  • {file.title} ({file.type}, {formatBytes(file.fileSize || 0)})
                </Text>
              ))}
            </View>
          );
        })}
        
        <View style={styles.dataItem}>
          <Text style={[styles.dataLabel, { color: colors.text }]}>
            All Media Files: {mediaFiles.length} items
          </Text>
          {mediaFiles.map(file => (
            <Text key={file.id} style={[styles.dataValue, { color: colors.muted }]}>
              • {file.title} ({file.category || 'No category'}, {file.type})
            </Text>
          ))}
        </View>
      </View>
    );
  };

  const renderUserProfile = () => {
    return (
      <View style={styles.categoryCard}>
        <View style={styles.sectionRow}>
          <View style={styles.sectionIcon}>
            <User size={16} color={colors.primary} />
          </View>
          <Text style={[styles.categoryTitle, { color: colors.text }]}>
            User Profile
          </Text>
        </View>
        
        <View style={styles.dataItem}>
          <Text style={[styles.dataLabel, { color: colors.text }]}>Basic Info</Text>
          <Text style={[styles.dataValue, { color: colors.muted }]}>
            Name: {profile?.name || 'Not set'}
          </Text>
          <Text style={[styles.dataValue, { color: colors.muted }]}>
            Language: {profile?.language || 'Not set'}
          </Text>
          <Text style={[styles.dataValue, { color: colors.muted }]}>
            Onboarding: {profile?.hasCompletedOnboarding ? 'Completed' : 'Not completed'}
          </Text>
        </View>
        
        <View style={styles.dataItem}>
          <Text style={[styles.dataLabel, { color: colors.text }]}>Data Arrays</Text>
          <Text style={[styles.dataValue, { color: colors.muted }]}>
            Emergency Contacts: {profile?.emergencyContacts?.length || 0}
          </Text>
          <Text style={[styles.dataValue, { color: colors.muted }]}>
            Mood Entries: {profile?.moodEntries?.length || 0}
          </Text>
          <Text style={[styles.dataValue, { color: colors.muted }]}>
            Health Metrics: {profile?.healthMetrics?.length || 0}
          </Text>
          <Text style={[styles.dataValue, { color: colors.muted }]}>
            Custom Exercises: {profile?.customExercises?.length || 0}
          </Text>
        </View>
      </View>
    );
  };

  const renderStorageStats = () => {
    return (
      <View style={styles.statsCard}>
        <View style={styles.sectionRow}>
          <View style={styles.sectionIcon}>
            <HardDrive size={16} color={colors.primary} />
          </View>
          <Text style={[styles.statsTitle, { color: colors.text }]}>
            Storage Statistics
          </Text>
        </View>
        
        {storageInfo && (
          <>
            <View style={styles.infoRow}>
              <Text style={[styles.dataLabel, { color: colors.text }]}>Total Storage Keys:</Text>
              <Text style={[styles.dataValue, { color: colors.muted }]}>{storageInfo.totalKeys}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={[styles.dataLabel, { color: colors.text }]}>User Profile Size:</Text>
              <Text style={[styles.dataValue, { color: colors.muted }]}>{storageInfo.userProfileSize}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={[styles.dataLabel, { color: colors.text }]}>Total Storage Size:</Text>
              <Text style={[styles.dataValue, { color: colors.muted }]}>{storageInfo.totalStorageSize}</Text>
            </View>
            {storageInfo.storageUsage && (
              <View style={styles.infoRow}>
                <Text style={[styles.dataLabel, { color: colors.text }]}>Browser Storage Used:</Text>
                <Text style={[styles.dataValue, { color: colors.muted }]}>{storageInfo.storageUsage}</Text>
              </View>
            )}
            {storageInfo.storageQuota && (
              <View style={styles.infoRow}>
                <Text style={[styles.dataLabel, { color: colors.text }]}>Browser Storage Quota:</Text>
                <Text style={[styles.dataValue, { color: colors.muted }]}>{storageInfo.storageQuota}</Text>
              </View>
            )}
          </>
        )}
      </View>
    );
  };

  const renderSystemInfo = () => {
    return (
      <View style={styles.categoryCard}>
        <View style={styles.sectionRow}>
          <View style={styles.sectionIcon}>
            <Smartphone size={16} color={colors.primary} />
          </View>
          <Text style={[styles.categoryTitle, { color: colors.text }]}>
            System Info
          </Text>
        </View>
        
        <View style={styles.dataItem}>
          <Text style={[styles.dataLabel, { color: colors.text }]}>Platform</Text>
          <Text style={[styles.dataValue, { color: colors.muted }]}>
            {Platform.OS} {Platform.Version}
          </Text>
        </View>
        
        <View style={styles.dataItem}>
          <Text style={[styles.dataLabel, { color: colors.text }]}>Theme</Text>
          <Text style={[styles.dataValue, { color: colors.muted }]}>
            {currentTheme}
          </Text>
        </View>
        
        <View style={styles.dataItem}>
          <Text style={[styles.dataLabel, { color: colors.text }]}>Refresh Key</Text>
          <Text style={[styles.dataValue, { color: colors.muted }]}>
            {refreshKey}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <Modal 
      visible={visible} 
      animationType="slide" 
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
          <View style={[styles.header, { borderBottomColor: colors.border }]}>
            <View style={[styles.sectionIcon, { backgroundColor: colors.warning + '20' }]}>
              <Bug size={20} color={colors.warning} />
            </View>
            <Text style={[styles.headerTitle, { color: colors.text }]}>
              Debug Panel
            </Text>
            <TouchableOpacity style={styles.refreshButton} onPress={refreshData}>
              <RefreshCw size={16} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <X size={20} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {renderStorageStats()}
            {renderUserProfile()}
            {renderDocuments()}
            {renderMediaFiles()}
            {renderSystemInfo()}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}; 