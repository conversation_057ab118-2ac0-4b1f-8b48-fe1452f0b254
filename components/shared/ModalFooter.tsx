import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface ColorScheme {
  background: string;
  card: string;
  border: string;
  text: string;
  primary: string;
  primaryDark?: string;
  muted: string;
}

interface ModalFooterProps {
  colors: ColorScheme;
  onCancel?: () => void;
  onSubmit?: () => void;
  cancelText?: string;
  submitText?: string;
  showCancel?: boolean;
  showSubmit?: boolean;
  submitDisabled?: boolean;
  useGradient?: boolean;
}

export const ModalFooter: React.FC<ModalFooterProps> = ({
  colors,
  onCancel,
  onSubmit,
  cancelText = 'Cancel',
  submitText = 'Save',
  showCancel = true,
  showSubmit = true,
  submitDisabled = false,
  useGradient = false,
}) => {
  if (!showCancel && !showSubmit) {
    return null;
  }

  return (
    <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
      {showCancel && onCancel && (
        <TouchableOpacity
          style={[
            styles.cancelButton,
            {
              borderColor: colors.border,
              backgroundColor: colors.background,
            },
            !showSubmit && styles.fullWidth,
          ]}
          onPress={onCancel}
        >
          <Text style={[styles.cancelButtonText, { color: colors.text }]}>
            {cancelText}
          </Text>
        </TouchableOpacity>
      )}

      {showSubmit && onSubmit && (
        <TouchableOpacity
          style={[
            styles.submitButton,
            {
              backgroundColor: colors.primary,
            },
            submitDisabled && styles.submitButtonDisabled,
            !showCancel && styles.fullWidth,
          ]}
          onPress={onSubmit}
          disabled={submitDisabled}
        >
          {useGradient && colors.primaryDark ? (
            <LinearGradient
              colors={[colors.primary, colors.primaryDark]}
              style={styles.submitButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.submitButtonText}>{submitText}</Text>
            </LinearGradient>
          ) : (
            <Text style={styles.submitButtonText}>{submitText}</Text>
          )}
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({


  cancelButton: {
    alignItems: 'center',
    borderRadius: 16,
    borderWidth: 2,
    flex: 1,
    justifyContent: 'center',
    marginRight: 6,
    padding: 16,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.2,
  },
  fullWidth: {
    marginLeft: 0,
    marginRight: 0,
  },
  modalFooter: {
    borderTopWidth: 1,
    flexDirection: 'row',
    gap: 12,
    padding: 20,
  },
  submitButton: {
    alignItems: 'center',
    borderRadius: 16,
    flex: 1,
    justifyContent: 'center',
    marginLeft: 6,
    overflow: 'hidden',
    padding: 16,
  },
  submitButtonDisabled: {
    opacity: 0.6,
  },
  submitButtonGradient: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    width: '100%',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: -0.2,
  },


}); 