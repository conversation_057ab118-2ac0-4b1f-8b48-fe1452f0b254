import React, { useRef } from 'react';
import { TouchableOpacity, View, StyleSheet } from 'react-native';
import { useDebug } from '@/hooks/useDebug';

interface DebugTriggerProps {
  children?: React.ReactNode;
  tapCount?: number; // Number of taps required to trigger debug panel
}

export const DebugTrigger: React.FC<DebugTriggerProps> = ({ 
  children, 
  tapCount = 3 
}) => {
  const debug = useDebug();
  const tapCountRef = useRef(0);
  const tapTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const handleTap = () => {
    if (!debug.isEnabled) return;

    tapCountRef.current += 1;

    // Clear existing timeout
    if (tapTimeoutRef.current) {
      clearTimeout(tapTimeoutRef.current);
    }

    // Set new timeout to reset tap count
    tapTimeoutRef.current = setTimeout(() => {
      tapCountRef.current = 0;
    }, 1000); // Reset after 1 second

    // Check if we've reached the required tap count
    if (tapCountRef.current >= tapCount) {
      tapCountRef.current = 0;
      if (tapTimeoutRef.current) {
        clearTimeout(tapTimeoutRef.current);
      }
      debug.toggleDebugPanel();
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
  });

  if (!debug.isEnabled) {
    return <>{children}</>;
  }

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={handleTap}
      activeOpacity={1}
    >
      <View style={styles.container}>
        {children}
      </View>
    </TouchableOpacity>
  );
}; 