import React from "react";
import { StyleSheet, View, Text, TouchableOpacity } from "react-native";
import { useUserStore } from "@/store/user/user-store";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import { Smile, Frown, Meh } from "lucide-react-native";
export type MoodType = "great" | "good" | "neutral" | "bad" | "terrible";
interface MoodSelectorProps {
  selectedMood: MoodType;
  onMoodChange: (mood: MoodType) => void;
  title?: string;
  showTitle?: boolean;
  size?: "small" | "medium" | "large";
}
export default function MoodSelector({
  selectedMood,
  onMoodChange,
  title,
  showTitle = true,
  size = "medium",
}: MoodSelectorProps) {
  const { profile } = useUserStore();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const getMoodIcon = (mood: string, iconSize: number, selected: boolean) => {
    const color = selected ? colors.primary : colors.muted;
    switch (mood) {
      case "great":
        return <Smile size={iconSize} color={color} />;
      case "good":
        return <Smile size={iconSize} color={color} />;
      case "neutral":
        return <Meh size={iconSize} color={color} />;
      case "bad":
        return <Frown size={iconSize} color={color} />;
      case "terrible":
        return <Frown size={iconSize} color={color} />;
      default:
        return <Meh size={iconSize} color={color} />;
    }
  };
  const getMoodText = (mood: string) => {
    switch (mood) {
      case "great":
        return profile?.language === "nl" ? "Uitstekend" : "Great";
      case "good":
        return profile?.language === "nl" ? "Goed" : "Good";
      case "neutral":
        return profile?.language === "nl" ? "Neutraal" : "Neutral";
      case "bad":
        return profile?.language === "nl" ? "Slecht" : "Bad";
      case "terrible":
        return profile?.language === "nl" ? "Verschrikkelijk" : "Terrible";
      default:
        return profile?.language === "nl" ? "Neutraal" : "Neutral";
    }
  };
  const getDefaultTitle = () => {
    return profile?.language === "nl"
      ? "Hoe voel je je vandaag?"
      : "How are you feeling today?";
  };
  const moods: MoodType[] = ["great", "good", "neutral", "bad", "terrible"];
  const getSizeConfig = () => {
    switch (size) {
      case "small":
        return { iconSizes: [24, 22, 22, 22, 24], optionWidth: "16%" as const };
      case "large":
        return { iconSizes: [40, 36, 36, 36, 40], optionWidth: "18%" as const };
      default:
        return { iconSizes: [32, 28, 28, 28, 32], optionWidth: "18%" as const };
    }
  };
  const { iconSizes, optionWidth } = getSizeConfig();
  return (
    <View style={[styles.moodSection, { borderColor: colors.border }]}>
      {showTitle && (
        <Text style={[styles.moodSectionLabel, { color: colors.text }]}>
          {title || getDefaultTitle()}
        </Text>
      )}
      <View style={styles.moodOptions}>
        {moods.map((mood, index) => (
          <TouchableOpacity
            key={mood}
            style={[
              styles.moodOption,
              { width: optionWidth },
              selectedMood === mood && {
                backgroundColor: colors.primary + "20",
              },
            ]}
            onPress={() => onMoodChange(mood)}
          >
            {getMoodIcon(mood, iconSizes[index], selectedMood === mood)}
            <Text
              style={[
                styles.moodText,
                {
                  color: selectedMood === mood ? colors.primary : colors.muted,
                },
                size === "small" && styles.moodTextSmall,
                size === "large" && styles.moodTextLarge,
                size === "medium" && styles.moodTextMedium,
              ]}
            >
              {getMoodText(mood)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}
const styles = StyleSheet.create({


  moodOption: {
    alignItems: "center",
    borderRadius: 8,
    padding: 10,
  },
  moodOptions: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  moodSection: {
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
    padding: 12,
  },
  moodSectionLabel: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 12,
  },
  moodText: {
    marginTop: 4,
    textAlign: "center",
  },
  moodTextLarge: {
    fontSize: 14,
  },
  moodTextMedium: {
    fontSize: 12,
  },
  moodTextSmall: {
    fontSize: 10,
  },


});