import React from 'react';
import { StyleSheet, View, Text, TextInput, TextInputProps } from 'react-native';
import { LucideIcon } from 'lucide-react-native';

interface ColorScheme {
  card: string;
  background: string;
  text: string;
  border: string;
  muted: string;
  danger: string;
  primary: string;
}

interface FormInputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  error?: string;
  colors: ColorScheme;
  icon?: LucideIcon;
  iconColor?: string;
  required?: boolean;
  containerStyle?: object;
  inputStyle?: object;
}

export const FormInput: React.FC<FormInputProps> = ({
  label,
  error,
  colors,
  icon: IconComponent,
  iconColor,
  required = false,
  containerStyle,
  inputStyle,
  ...textInputProps
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={[styles.label, { color: colors.text }]}>
          {label}{required && '*'}
        </Text>
      )}
      
      <View style={[
        styles.inputContainer,
        IconComponent && styles.inputContainerWithIcon,
        { borderColor: error ? colors.danger : colors.border }
      ]}>
        {IconComponent && (
          <IconComponent 
            size={20} 
            color={iconColor || colors.primary} 
            style={styles.icon}
            strokeWidth={2}
          />
        )}
        
        <TextInput
          style={[
            styles.input,
            IconComponent && styles.inputWithIcon,
            {
              backgroundColor: colors.background,
              color: colors.text,
            },
            inputStyle
          ]}
          placeholderTextColor={colors.muted}
          {...textInputProps}
        />
      </View>
      
      {error && (
        <Text style={[styles.errorText, { color: colors.danger }]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({


  container: {
    marginBottom: 20,
  },
  errorText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    marginTop: 6,
  },
  icon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    padding: 16,
  },
  inputContainer: {
    alignItems: 'center',
    borderRadius: 16,
    borderWidth: 2,
    flexDirection: 'row',
  },
  inputContainerWithIcon: {
    paddingLeft: 16,
  },
  inputWithIcon: {
    paddingLeft: 12,
  },
  label: {
    fontSize: 17,
    fontWeight: '600',
    letterSpacing: -0.2,
    marginBottom: 8,
  },


}); 