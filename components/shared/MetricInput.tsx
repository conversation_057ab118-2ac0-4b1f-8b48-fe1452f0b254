import React from "react";
import { StyleSheet, View, Text, TextInput } from "react-native";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";

interface MetricInputProps {
  IconComponent: React.ElementType;
  iconBgColor: string;
  metricName: string;
  currentValue: string;
  setValue: (text: string) => void;
  unit: string;
  goal: number;
  goalUnitText: string;
  progressColor: string;
  keyboardType?: "numeric" | "default";
  maxLength?: number;
}

export const MetricInput: React.FC<MetricInputProps> = ({
  IconComponent,
  iconBgColor,
  metricName,
  currentValue,
  setValue,
  unit,
  goal,
  goalUnitText,
  progressColor,
  keyboardType = "numeric",
  maxLength,
}) => {
  const { currentTheme } = useTheme();
  const themeColors = currentTheme === "light" ? Colors.light : Colors.dark;
  const numericValue = parseFloat(currentValue) || 0;
  const progress = goal > 0 ? Math.min(numericValue / goal, 1) * 100 : 0;

  return (
    <View style={styles.metricItemContainer}>
      <View
        style={[
          styles.metricIconOuterContainer,
          { backgroundColor: iconBgColor },
        ]}
      >
        <IconComponent color={progressColor} size={24} />
      </View>
      <View style={styles.metricDetailsContainer}>
        <Text style={[styles.metricName, { color: themeColors.text }]}>
          {metricName}
        </Text>
        <View style={styles.metricInputRow}>
          <TextInput
            style={[
              styles.metricInputBox,
              {
                borderColor: themeColors.border,
                color: themeColors.text,
                backgroundColor: themeColors.background,
              },
            ]}
            value={currentValue}
            onChangeText={setValue}
            keyboardType={keyboardType}
            maxLength={maxLength}
          />
          <Text
            style={[
              styles.metricUnitText,
              { color: themeColors.textSecondary },
            ]}
          >
            {unit}{" "}
            <Text style={{ color: themeColors.textTertiary }}>
              {goalUnitText}
            </Text>
          </Text>
        </View>
        <View
          style={[
            styles.progressBarBackground,
            { backgroundColor: themeColors.border },
          ]}
        >
          <View
            style={[
              styles.progressBarFill,
              { width: `${progress}%`, backgroundColor: progressColor },
            ]}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({


  metricDetailsContainer: {
    flex: 1,
  },
  metricIconOuterContainer: {
    alignItems: "center",
    borderRadius: 24,
    height: 48,
    justifyContent: "center",
    marginRight: 15,
    width: 48,
  },
  metricInputBox: {
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
    fontWeight: "500",
    height: 40,
    marginRight: 10,
    paddingVertical: 0,
    textAlign: "center",
    width: 60,
  },
  metricInputRow: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: 8,
  },
  metricItemContainer: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: 20,
    width: "100%",
  },
  metricName: {
    fontSize: 17,
    fontWeight: "600",
    marginBottom: 8,
  },
  metricUnitText: {
    flexShrink: 1,
    fontSize: 14,
  },
  progressBarBackground: {
    borderRadius: 4,
    height: 8,
    overflow: "hidden",
    width: "100%",
  },
  progressBarFill: {
    borderRadius: 4,
    height: "100%",
  },


}); 