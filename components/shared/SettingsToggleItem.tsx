import React from 'react';
import { View, Text, Switch, StyleSheet, Platform } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { LucideIcon } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';

interface ColorScheme {
  background: string;
  card: string;
  cardGradient?: string;
  border: string;
  text: string;
  textSecondary?: string;
  primary: string;
  muted: string;
}

interface SettingsToggleItemProps {
  id: string;
  title: string;
  description?: string;
  icon?: LucideIcon;
  iconColor?: string;
  value: boolean;
  onToggle: (id: string, value: boolean) => void;
  colors: ColorScheme;
  useGradient?: boolean;
  disabled?: boolean;
}

export const SettingsToggleItem: React.FC<SettingsToggleItemProps> = ({
  id,
  title,
  description,
  icon: IconComponent,
  iconColor,
  value,
  onToggle,
  colors,
  useGradient = false,
  disabled = false,
}) => {
  const handleToggle = (newValue: boolean) => {
    if (disabled) return;
    
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
    onToggle(id, newValue);
  };

  const itemColor = iconColor || colors.primary;
  const cardColor = colors.card || colors.background;
  const cardGradientColor = colors.cardGradient || colors.card || colors.background;

  return (
    <View style={[styles.toggleItem, { borderBottomColor: colors.border }]}>
      {useGradient ? (
        <LinearGradient
          colors={[cardColor, cardGradientColor]}
          style={styles.toggleItemGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.toggleItemContent}>
            {IconComponent && (
              <View style={[styles.iconContainer, { backgroundColor: itemColor + '20' }]}>
                <IconComponent size={20} color={itemColor} strokeWidth={2.5} />
              </View>
            )}
            
            <View style={styles.toggleInfo}>
              <Text style={[styles.toggleTitle, { color: colors.text }]}>
                {title}
              </Text>
              {description && (
                <Text style={[styles.toggleDescription, { color: colors.textSecondary || colors.muted }]}>
                  {description}
                </Text>
              )}
            </View>
            
            <Switch
              trackColor={{ false: colors.border, true: itemColor + '80' }}
              thumbColor={value ? itemColor : '#f4f3f4'}
              ios_backgroundColor={colors.border}
              onValueChange={handleToggle}
              value={value}
              disabled={disabled}
            />
          </View>
        </LinearGradient>
      ) : (
        <View style={[styles.toggleItemContent, { backgroundColor: cardColor }]}>
          {IconComponent && (
            <View style={[styles.iconContainer, { backgroundColor: itemColor + '20' }]}>
              <IconComponent size={20} color={itemColor} strokeWidth={2.5} />
            </View>
          )}
          
          <View style={styles.toggleInfo}>
            <Text style={[styles.toggleTitle, { color: colors.text }]}>
              {title}
            </Text>
            {description && (
              <Text style={[styles.toggleDescription, { color: colors.textSecondary || colors.muted }]}>
                {description}
              </Text>
            )}
          </View>
          
          <Switch
            trackColor={{ false: colors.border, true: itemColor + '80' }}
            thumbColor={value ? itemColor : '#f4f3f4'}
            ios_backgroundColor={colors.border}
            onValueChange={handleToggle}
            value={value}
            disabled={disabled}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({


  iconContainer: {
    alignItems: 'center',
    borderRadius: 22,
    height: 44,
    justifyContent: 'center',
    marginRight: 16,
    width: 44,
  },
  toggleDescription: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  toggleInfo: {
    flex: 1,
    marginRight: 16,
  },
  toggleItem: {
    borderBottomWidth: 1,
    borderRadius: 16,
    elevation: 2,
    marginBottom: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  toggleItemContent: {
    alignItems: 'center',
    flexDirection: 'row',
    padding: 16,
  },
  toggleItemGradient: {
    padding: 16,
  },
  toggleTitle: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.2,
    marginBottom: 4,
  },


}); 