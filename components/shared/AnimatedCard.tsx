import React from 'react';
import {
  Animated,
  TouchableOpacity,
  View,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { useCardPressAnimation, useCardHoverAnimation } from '@/hooks/animation/card-interactions';

interface AnimatedCardProps {
  children: React.ReactNode;
  onPress?: () => void;
  style?: ViewStyle;
  disabled?: boolean;
  enableHover?: boolean;
  enablePress?: boolean;
}

export const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  onPress,
  style,
  disabled = false,
  enableHover = true,
  enablePress = true,
}) => {
  const pressAnimation = useCardPressAnimation();
  const hoverAnimation = useCardHoverAnimation();

  const handlePressInWithHover = () => {
    if (enablePress && !disabled) {
      pressAnimation.animatePress();
    }
    if (enableHover && !disabled) {
      hoverAnimation.animateHoverIn();
    }
  };

  const handlePressOutWithHover = () => {
    if (enablePress && !disabled) {
      pressAnimation.animateRelease();
    }
    if (enableHover && !disabled) {
      hoverAnimation.animateHoverOut();
    }
  };

  // Properly combine animated styles to avoid conflicts
  const getAnimatedStyle = () => {
    // Only use one animation at a time to avoid conflicts
    // Press animation takes priority when both are enabled
    if (enablePress) {
      return pressAnimation.animatedStyle;
    } else if (enableHover) {
      return hoverAnimation.animatedStyle;
    }
    return {};
  };

  // Combine shadow styles properly
  const getShadowStyle = () => {
    // Only use one shadow animation at a time to avoid conflicts
    // Press animation takes priority when both are enabled
    if (enablePress) {
      return pressAnimation.shadowStyle;
    } else if (enableHover) {
      return hoverAnimation.shadowStyle;
    }
    return {};
  };

  if (onPress && !disabled) {
    return (
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressInWithHover}
        onPressOut={handlePressOutWithHover}
        activeOpacity={1}
        style={styles.container}
      >
        <Animated.View
          style={[
            style,
            getAnimatedStyle(),
            getShadowStyle(),
          ]}
        >
          {children}
        </Animated.View>
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          style,
          getAnimatedStyle(),
          getShadowStyle(),
        ]}
      >
        {children}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
}); 