import React from "react";
import { StyleSheet, View, Text, ScrollView } from "react-native";
import { useUserStore } from "@/store/user/user-store";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
export interface Quote {
  text: string;
  author: string;
  category?: string;
}
interface QuotesListProps {
  quotes: Quote[];
  title?: string;
  showTitle?: boolean;
  maxHeight?: number;
}
export default function QuotesList({
  quotes,
  title,
  showTitle = true,
  maxHeight,
}: QuotesListProps) {
  const { profile } = useUserStore();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const getDefaultTitle = () => {
    return profile?.language === "nl"
      ? "Inspirerende Quotes"
      : "Inspiring Quotes";
  };
  const getNoQuotesText = () => {
    return profile?.language === "nl"
      ? "<PERSON>n quotes gevonden"
      : "No quotes found";
  };
  const containerStyle = maxHeight
    ? [styles.quotesList, { maxHeight }]
    : styles.quotesList;
  return (
    <View style={containerStyle}>
      {showTitle && (
        <Text style={[styles.quotesListTitle, { color: colors.text }]}>
          {title || getDefaultTitle()}
        </Text>
      )}
      {quotes && quotes.length > 0 ? (
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.quotesScrollView}
        >
          {quotes.map((quote, index) => (
            <View
              key={index}
              style={[
                styles.quoteItem,
                { backgroundColor: colors.card, borderColor: colors.border },
              ]}
            >
              <Text style={[styles.quoteText, { color: colors.text }]}>
                &ldquo;{quote.text}&rdquo;
              </Text>
              <Text style={[styles.quoteAuthor, { color: colors.muted }]}>
                - {quote.author}
              </Text>
              {quote.category && (
                <Text style={[styles.quoteCategory, { color: colors.primary }]}>
                  {quote.category}
                </Text>
              )}
            </View>
          ))}
        </ScrollView>
      ) : (
        <View style={styles.noQuotesContainer}>
          <Text style={[styles.noQuotesText, { color: colors.muted }]}>
            {getNoQuotesText()}
          </Text>
        </View>
      )}
    </View>
  );
}
const styles = StyleSheet.create({


  noQuotesContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    minHeight: 100,
  },
  noQuotesText: {
    fontSize: 16,
    textAlign: "center",
  },
  quoteAuthor: {
    fontSize: 14,
    textAlign: "right",
  },
  quoteCategory: {
    fontSize: 12,
    fontWeight: "500",
    marginTop: 4,
    textAlign: "right",
  },
  quoteItem: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    padding: 16,
  },
  quoteText: {
    fontSize: 16,
    fontStyle: "italic",
    lineHeight: 24,
    marginBottom: 8,
  },
  quotesList: {
    flex: 1,
  },
  quotesListTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 16,
  },
  quotesScrollView: {
    flex: 1,
  },


});