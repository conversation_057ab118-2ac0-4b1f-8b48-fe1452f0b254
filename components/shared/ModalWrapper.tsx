import React, { ReactNode } from "react";
import { Modal, View, StyleSheet, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useModalAnimation } from '@/hooks';
interface ColorScheme {
  background: string;
  card: string;
  cardGradient?: string;
  border: string;
  text: string;
  primary: string;
  muted: string;
  textSecondary?: string;
  primaryDark?: string;
  success?: string;
  successDark?: string;
  danger?: string;
  dangerDark?: string;
  info?: string;
  secondary?: string;
  warning?: string;
  accent?: string;
}
interface ModalWrapperProps {
  visible: boolean;
  onClose: () => void;
  children: ReactNode;
  colors: ColorScheme;
  animationType?: 'slide' | 'fade' | 'none';
  presentationStyle?: 'fullScreen' | 'pageSheet' | 'formSheet' | 'overFullScreen';
  useAnimation?: boolean;
  useGradient?: boolean;
  maxHeight?: number | `${number}%`;
  borderRadius?: number;
}
export const ModalWrapper: React.FC<ModalWrapperProps> = ({
  visible,
  onClose,
  children,
  colors,
  animationType = 'slide',
  presentationStyle,
  useAnimation = false,
  useGradient = false,
  maxHeight = '80%',
  borderRadius = 24,
}) => {
  const { animatedStyle } = useModalAnimation();
  // Create dynamic styles
  const dynamicStyles = {
    modalOverlay: {
      ...styles.modalOverlay,
      backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalContent: {
      ...styles.modalContent,
      backgroundColor: colors.card,
      maxHeight,
      borderRadius,
      minHeight: 600, // Ensure good minimum height
    },
  };
  return (
    <Modal
      visible={visible}
      animationType={animationType}
      transparent={true}
      onRequestClose={onClose}
      presentationStyle={presentationStyle}
    >
      <View style={dynamicStyles.modalOverlay}>
        {useAnimation ? (
          <Animated.View
            style={[
              dynamicStyles.modalContent,
              animatedStyle,
            ]}
          >
            {useGradient ? (
              <LinearGradient
                colors={[colors.background, colors.cardGradient || colors.card]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.contentWrapper}
              >
                {children}
              </LinearGradient>
            ) : (
              <View style={styles.contentWrapper}>{children}</View>
            )}
          </Animated.View>
        ) : (
          <View style={dynamicStyles.modalContent}>
            {useGradient ? (
              <LinearGradient
                colors={[colors.background, colors.cardGradient || colors.card]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.contentWrapper}
              >
                {children}
              </LinearGradient>
            ) : (
              <View style={styles.contentWrapper}>{children}</View>
            )}
          </View>
        )}
      </View>
    </Modal>
  );
};
const styles = StyleSheet.create({


  contentWrapper: {
    flex: 1,
  },
  modalContent: {
    elevation: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    width: '100%',
  },
  modalOverlay: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    padding: 5, // Minimal padding for maximum space
  },


}); 