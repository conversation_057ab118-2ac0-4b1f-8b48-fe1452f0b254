import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { LucideIcon } from 'lucide-react-native';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'outline';
  icon?: LucideIcon;
  disabled?: boolean;
  colors: {
    primary: string;
    text: string;
    border: string;
  };
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  icon: Icon,
  disabled = false,
  colors,
  style,
  textStyle: customTextStyle,
}) => {
  const isPrimary = variant === 'primary';
  
  const buttonStyle: ViewStyle = {
    ...styles.button,
    backgroundColor: isPrimary ? colors.primary : 'transparent',
    borderColor: isPrimary ? colors.primary : colors.border,
    borderWidth: isPrimary ? 0 : 1,
    opacity: disabled ? 0.6 : 1,
    ...style,
  };

  const finalTextStyle: TextStyle = {
    ...styles.text,
    color: isPrimary ? '#fff' : colors.text,
    ...customTextStyle,
  };

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      {Icon && <Icon size={20} color={isPrimary ? '#fff' : colors.text} style={styles.icon} />}
      <Text style={finalTextStyle}>{title}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({


  button: {
    alignItems: 'center',
    borderRadius: 12,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 16,
  },
  icon: {
    marginRight: 8,
  },
  text: {
    fontSize: 16,
    fontWeight: '500',
  },


}); 