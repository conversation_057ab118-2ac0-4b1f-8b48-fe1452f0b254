{"name": "sobrixhealth", "version": "1.0.0", "private": true, "type": "module", "main": "expo-router/entry", "engines": {"node": ">=18", "yarn": ">=1.22.0"}, "scripts": {"preinstall": "node enforce-yarn.cjs", "start": "expo start --tunnel", "start-web": "expo start --web --tunnel", "start-web-dev": "DEBUG=expo* expo start --web --tunnel", "android": "expo run:android", "ios": "expo run:ios", "build": "expo export", "build:web": "expo export --platform web", "build:production": "expo export --platform web --minify", "fix": "node scripts/fix-app.js", "fix-and-start": "node scripts/fix-app.js && yarn start --clear", "clean": "rm -rf node_modules yarn.lock && yarn install", "reset": "npx expo start --clear"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/slider": "4.5.6", "@react-native/codegen": "^0.79.2", "@react-navigation/native": "^7.1.6", "date-fns": "^4.1.0", "expo": "53.0.10", "expo-audio": "~0.4.6", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-contacts": "~14.2.5", "expo-dev-client": "~5.2.0", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.2.0", "expo-image-manipulator": "~13.1.6", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-location": "~18.1.5", "expo-media-library": "~17.1.7", "expo-notifications": "~0.31.3", "expo-print": "^14.1.4", "expo-router": "~5.0.7", "expo-sharing": "^13.1.5", "expo-speech": "~13.1.7", "expo-splash-screen": "~0.30.9", "expo-sqlite": "~15.2.11", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-updates": "~0.28.14", "expo-video": "~2.2.0", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "node": "^22.16.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "tailwindcss": "^3.4.1", "wa-sqlite": "^1.0.0", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-export-namespace-from": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@eslint/js": "^9.27.0", "@expo/ngrok": "^4.1.0", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "autoprefixer": "^10.4.21", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.27.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "globals": "^16.2.0", "postcss": "^8.5.4", "typescript": "~5.8.3", "typescript-eslint": "^8.33.0"}}