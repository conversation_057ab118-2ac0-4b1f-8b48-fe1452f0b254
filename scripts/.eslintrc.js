module.exports = {
  env: {
    node: true,
    es2021: true,
  },
  extends: [
    'eslint:recommended',
  ],
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module',
  },
  rules: {
    // Allow console.log in scripts
    'no-console': 'off',
    // Allow require() in .cjs files
    '@typescript-eslint/no-var-requires': 'off',
    // Allow unused variables in catch blocks
    'no-unused-vars': ['error', { 'argsIgnorePattern': '^_', 'varsIgnorePattern': '^_' }],
  },
}; 