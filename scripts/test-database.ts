import { initDatabase, simpleDatabaseService } from '../services/database-simple';

async function testDatabase() {
  try {
    console.log('🔄 Initializing database...');
    await initDatabase();
    console.log('✅ Database initialized successfully');

    // Test user profile operations
    console.log('🔄 Testing user profile operations...');
    const testProfile = {
      name: 'Test User',
      sobrietyDate: '2024-01-01',
      addiction: 'alcohol',
      hasCompletedOnboarding: true
    };

    await simpleDatabaseService.saveUserProfile(testProfile);
    console.log('✅ User profile saved');

    const retrievedProfile = await simpleDatabaseService.getUserProfile();
    console.log('✅ User profile retrieved:', retrievedProfile);

    // Test health data operations
    console.log('🔄 Testing health data operations...');
    const testHealthData = {
      sleep: 8.5,
      pills: 1,
      hydration: 2.5,
      exercise: 1
    };

    await simpleDatabaseService.saveHealthData('2024-01-15', testHealthData);
    console.log('✅ Health data saved');

    const retrievedHealthData = await simpleDatabaseService.getHealthData('2024-01-15');
    console.log('✅ Health data retrieved:', retrievedHealthData);

    // Test mood entry operations
    console.log('🔄 Testing mood entry operations...');
    const testMoodEntry = {
      id: 'mood-test-1',
      date: '2024-01-15',
      mood: 4,
      cravingIntensity: 2,
      notes: 'Feeling good today'
    };

    await simpleDatabaseService.addMoodEntry(testMoodEntry);
    console.log('✅ Mood entry added');

    const moodEntries = await simpleDatabaseService.getMoodEntries(10);
    console.log('✅ Mood entries retrieved:', moodEntries.length, 'entries');

    // Test emergency contact operations
    console.log('🔄 Testing emergency contact operations...');
    const testContact = {
      id: 'contact-test-1',
      name: 'Emergency Contact',
      phone: '+**********',
      email: '<EMAIL>',
      relationship: 'friend',
      notes: 'Available 24/7'
    };

    await simpleDatabaseService.addEmergencyContact(testContact);
    console.log('✅ Emergency contact added');

    const contacts = await simpleDatabaseService.getEmergencyContacts();
    console.log('✅ Emergency contacts retrieved:', contacts.length, 'contacts');

    // Test database stats
    console.log('🔄 Testing database stats...');
    const stats = await simpleDatabaseService.getDatabaseStats();
    console.log('✅ Database stats:', stats);

    console.log('\n🎉 All database tests passed successfully!');
    console.log('📊 Database is ready for production use');

  } catch (error) {
    console.error('❌ Database test failed:', error);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testDatabase();
}

export { testDatabase }; 