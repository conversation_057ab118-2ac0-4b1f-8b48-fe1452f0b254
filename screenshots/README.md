# Screenshots Directory

This directory contains screenshots of the SobrixHealth app used in the main README.md file.

## Required Screenshots

To complete the README documentation, please add the following screenshots:

### Main App Screenshots (250px width recommended)

- `dashboard.png` - Main recovery dashboard showing sobriety counter and key metrics
- `progress.png` - Progress tracking page with charts and mood logging
- `mindfulness.png` - Mindfulness exercises list or active exercise screen
- `health.png` - Health metrics tracking page
- `resources.png` - Emergency resources and contacts page
- `settings.png` - Settings and profile configuration page

### Detailed Feature Screenshots (600px width recommended)

- `dashboard-detail.png` - Detailed view of dashboard features and widgets
- `progress-charts.png` - Progress charts showing mood, milestones, and analytics
- `mindfulness-exercises.png` - Mindfulness exercises in action or selection screen
- `emergency-resources.png` - Emergency contacts and support resources
- `onboarding-flow.png` - Onboarding process screens (can be a collage)

## Screenshot Guidelines

### Technical Requirements

- **Format**: PNG preferred for crisp quality
- **Device**: Use iPhone or Android simulator for consistent appearance
- **Orientation**: Portrait mode for mobile screenshots
- **Quality**: High resolution, clear and readable text

### Content Guidelines

- Use demo/test data that looks realistic but doesn't contain personal information
- Ensure good lighting and contrast for readability
- Show the app in a positive, functional state
- Include diverse content where applicable (different languages, themes)

### Naming Convention

- Use lowercase with hyphens for multi-word names
- Be descriptive but concise
- Match the exact filenames referenced in README.md

## How to Capture Screenshots

### iOS Simulator

1. Open the app in iOS Simulator
2. Navigate to the desired screen
3. Use `Cmd + S` to save screenshot
4. Rename and move to this directory

### Android Emulator

1. Open the app in Android Emulator
2. Navigate to the desired screen
3. Use the camera icon in emulator controls
4. Rename and move to this directory

### Physical Device

1. Take screenshots using device screenshot function
2. Transfer to computer
3. Resize if needed to match guidelines
4. Add to this directory

## Image Optimization

After adding screenshots, consider optimizing them:

- Use tools like ImageOptim, TinyPNG, or similar
- Maintain quality while reducing file size
- Ensure images load quickly in README

## Notes

- Screenshots should represent the current state of the app
- Update screenshots when major UI changes are made
- Consider creating both light and dark theme versions
- Test that all image links work correctly in the README
